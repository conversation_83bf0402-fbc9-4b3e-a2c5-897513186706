{"name": "apm-monorepo", "private": true, "scripts": {"dev": "pnpm -C apps/web dev", "dev:ui:watch": "pnpm -C packages/tinyflow/packages/ui build --watch", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .eslint<PERSON>ore", "build": "pnpm -C apps/web build", "preview": "pnpm -C apps/web preview", "commitlint": "commitlint --config ./configs/commitlint/index.cjs -e -V"}}