require("@rushstack/eslint-patch/modern-module-resolution");

module.exports = {
  env: { browser: true, es2023: true },
  extends: [
    "plugin:vue/vue3-essential",
    "eslint:recommended",
    "@vue/eslint-config-typescript",
    "@vue/eslint-config-prettier/skip-formatting",
    "plugin:prettier/recommended"
  ],
  rules: {
    semi: ["warn", "always"],
    "no-unused-vars": 0,
    "no-shadow": "off",
    "no-delete-var": "off",
    "no-duplicate-case": "warn",
    "no-unreachable": "warn",
    "no-empty-function": "warn",
    "no-redeclare": "warn",
    "no-multi-spaces": "warn",
    "no-trailing-spaces": "warn",
    "no-mixed-spaces-and-tabs": "warn",
    "space-before-blocks": "warn",
    "space-infix-ops": "warn",
    "arrow-spacing": "warn",
    "brace-style": "warn",
    "array-bracket-spacing": "warn",
    "switch-colon-spacing": "warn",
    "vue/multi-word-component-names": 0,
    "comma-dangle": 0
  }
};


