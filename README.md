# APM-WEB

## Monorepo 工作区

根使用 pnpm workspaces：

```
packages:
  - "apps/*"
  - "packages/*"
  - "packages/tinyflow/packages/vue"
  - "packages/tinyflow/packages/ui"
```

### 开发命令

- 启动 Web：`pnpm dev`
- UI 产物监听（如需改 UI 源码即时刷新）：

```
pnpm -C packages/tinyflow/packages/ui build --watch
```

### 统一配置

- ESLint：`configs/eslint-config/index.cjs`（apps/web 继承）
- Commitlint：`configs/commitlint/index.cjs`（根脚本 `pnpm commitlint`）

### 说明

- `@tinyflow-ai/vue` 以源码直引参与编译，HMR 生效。
- `@tinyflow-ai/ui` 走 dist 产物，避免在 Web 侧引入 Svelte 插件。


