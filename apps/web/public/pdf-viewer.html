<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8" />
  <link rel="icon" href="./src/assets/icons/fengchi.ico" />
  <title>运维可观测平台</title>
  <style>
    html, body {
      margin: 0;
      padding: 0;
      height: 100%;
      overflow: hidden;
      background: #f5f5f5;
    }
    #pdfViewer {
      width: 100vw;
      height: 100vh;
      border: none;
    }
  </style>
</head>
<body>
  <embed id="pdfViewer" src="" type="application/pdf" />
  <script>
    const url = new URL(location.href);
    const pdf = url.searchParams.get("file");
    const viewer = document.getElementById("pdfViewer");

    if (pdf) {
      viewer.src = pdf;
      const fileName = pdf.split("/").pop().replace(/\.pdf$/, "");
      const platform = fileName.split("--")[1];
      if (platform) {
        document.title = `${decodeURIComponent(platform)}_Agent 接入文档`;
      }
    } else {
      document.body.innerHTML = "<p style='text-align:center;padding-top:40px;'>未找到对应文件</p>";
    }
  </script>
</body>
</html>
