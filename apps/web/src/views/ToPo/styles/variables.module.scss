$base-menu-color: #fff;
$base-menu-color-active: #000;
$base-menu-color-hover: #000;
$base-menu-background: #4921c3;
$base-logo-title-color: #fff;

$base-menu-light-color: rgba(0, 0, 0, 0.7);
$base-menu-light-background: #ffffff;
$base-logo-light-title-color: #001529;

$base-sub-menu-background: #fff;
$base-sub-menu-hover: #fff;

$top-bar-bg-color: #4921c3;
$top-bar-height: 45px;

$--color-primary: #409eff;
$--color-success: #67c23a;
$--color-warning: #e6a23c;
$--color-danger: #f56c6c;
$--color-info: #959595;

$base-sidebar-width: 180px;

$tableStripedColor: #f7f8fa; //表格斑马条纹背景色
$tableHeaderBgColor: #f1f4f7; //表格表头背景
$appRadius: 6px;
$appRadiusLg: 8px;

$dragPanelBgColor: #f0f0f0; //拖拽面板背景色
$dragNodeBorderColor: #e6f7ff; //拖拽节点边框色

:export {
  menuColor: $base-menu-color;
  menuLightColor: $base-menu-light-color;
  menuColorActive: $base-menu-color-active;
  menuBackground: $base-menu-background;
  menuLightBackground: $base-menu-light-background;
  subMenuBackground: $base-sub-menu-background;
  subMenuHover: $base-sub-menu-hover;
  sideBarWidth: $base-sidebar-width;
  logoTitleColor: $base-logo-title-color;
  logoLightTitleColor: $base-logo-light-title-color;
  primaryColor: $--color-primary;
  successColor: $--color-success;
  dangerColor: $--color-danger;
  infoColor: $--color-info;
  warningColor: $--color-warning;
  dragPanelBgColor: $dragPanelBgColor;
  dragNodeBorderColor: $dragNodeBorderColor;
}
