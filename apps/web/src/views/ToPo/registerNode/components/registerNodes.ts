import { createApp, h } from "vue";
import {
  getChineseTypeNameByNumber,
  getNameToNumberMapping,
  randomNumber
} from "../../utils/index";
import { delTopo } from "@/api/topo/index";

// 创建节点
const createNode = (lf, nodeName, component) => {
  lf.register(nodeName, ({ HtmlNode, HtmlNodeModel }) => {
    class NodeView extends HtmlNode {
      setHtml(rootEl) {
        const { model } = this.props;
        rootEl.innerHTML = "";
        const el = document.createElement("div");
        const app = createApp({
          render: () => h(component, { properties: model.properties })
        });
        app.mount(el);
        rootEl.appendChild(el);
      }
    }

    class NodeModel extends HtmlNodeModel {
      createId() {
        return randomNumber(); //id用随机数数字
      }

      constructor(data, graphModel) {
        super(data, graphModel);
        this.menu = [
          // { text: "复制", callback: node => lf.cloneNode(node.id) },
          {
            text: "删除",
            callback: async node => {
              lf.deleteNode(node.id);
              // try {
              //   const res = await delTopo(node.id);
              //   console.log(res);
              //   if (res.code === 0) {
              //     ElMessage.success("删除成功");
              //     lf.deleteNode(node.id);
              //   }
              // } catch (error) {
              //   console.error("Error logs:", error);
              // }
            }
          },
          {
            text: "编辑",
            callback: node => graphModel.eventCenter.emit("user:open-edit-dialog", node)
          },
          {
            text: "详情",
            callback: node => graphModel.eventCenter.emit("user:open-details-dialog", node)
          }
        ];
      }

      getDefaultAnchor() {
        const { id, x, y, width, height } = this;
        return [
          { x, y: y - height / 2, id: `${id}`, type: "incomming" },
          { x, y: y + height / 2, id: `${id}`, type: "outgoing" },
          { x: x - width / 2, y, id: `${id}`, type: "incomming" },
          { x: x + width / 2, y, id: `${id}`, type: "outgoing" }
        ];
      }

      initNodeData(data) {
        super.initNodeData(data);
        this.width = 120;
        this.height = 120;
        this.radius = 50;
        this.color = "red";
        this.text = {
          ...this.text,
          x: this.x,
          y: this.y + 45,
          textAlign: "center",
          textBaseline: "middle"
        };
      }
    }

    return {
      view: NodeView,
      model: NodeModel
    };
  });
};

// 注册各个组件节点
import Cdn from "../cdn/cdn.vue";
import Connector from "../connector/connector.vue";
import Dns from "../dns/dns.vue";
import ServerIcon from "../serverIcon/serverIcon.vue";
import Firewall from "../firewall/firewall.vue";
import Gateway from "../gateway/gateway.vue";
import Jwd from "../jwd/jwd.vue";
import Rss from "../rss/rss.vue";
import WEBfw from "../WEBfw/WEBfw.vue";
import Log from "../log/log.vue";
import database from "../database/database.vue";
import machine from "../machine/machine.vue";
import Webpage from "../Webpage/Webpage.vue";
import pw from "../pw/pw.vue";
import loophole from "../loophole/loophole.vue";
import fort from "../fort/fort.vue";
import applet from "../applet/applet.vue";
import appIcon from "../appIcon/appIcon.vue";
import mobileIcon from "../mobileIcon/mobileIcon.vue";
import nginx from "../nginx/nginx.vue";
import redis from "../redis/redis.vue";
import nacos from "../nacos/nacos.vue";

export default function registerConnect(lf) {
  createNode(lf, "cdn", Cdn);
  createNode(lf, "connector", Connector);
  createNode(lf, "dns", Dns);
  createNode(lf, "serverIcon", ServerIcon);
  createNode(lf, "firewall", Firewall);
  createNode(lf, "gateway", Gateway);
  createNode(lf, "jwd", Jwd);
  createNode(lf, "rss", Rss);
  createNode(lf, "WEBfw", WEBfw);
  createNode(lf, "log", Log);
  createNode(lf, "database", database);
  createNode(lf, "machine", machine);
  createNode(lf, "Webpage", Webpage);
  createNode(lf, "pw", pw);
  createNode(lf, "loophole", loophole);
  createNode(lf, "fort", fort);
  createNode(lf, "applet", applet);
  createNode(lf, "appIcon", appIcon);
  createNode(lf, "mobileIcon", mobileIcon);
  createNode(lf, "nginx", nginx);
  createNode(lf, "redis", redis);
  createNode(lf, "nacos", nacos);

  // 悬浮窗的鼠标事件监听
  lf.on("node:mouseenter", ({ data, e }) => {
    if (!data.properties.networkSegment || data.type === "dynamic-group") {
      return;
    }

    const tooltip = document.createElement("div");
    const statusMap = {
      "1": { label: "正常", color: "#409eff66" }, // 蓝色
      "2": { label: "异常", color: "#f56c6c66" }, // 红色
      "3": { label: "关机", color: "#9E9E9E66" } // 灰色
    };
    // console.log("data ===>>>", data);
    tooltip.className = "node-tooltip";
    tooltip.innerHTML = `
    <div style="font-weight: bold; color: #333;margin-bottom:5px">节点信息</div>
    <div style="color: #2c3e50;">节点名称: ${data.properties.createName}</div>
    <div style="color: #2c3e50;">节点类型: ${getChineseTypeNameByNumber(getNameToNumberMapping(data.type))}</div>
    <div style="color: #2c3e50;">节点状态: ${statusMap[data.properties.status]?.label}</div>
    <div style="color: #2c3e50;">网段: ${data.properties.networkSegment}</div>
    <div style="color: #2c3e50;">网关: ********</div>
    <div style="color: #2c3e50;">带宽: 1Gbps</div>
    <div style="color: #2c3e50;">IP: ${data.properties.ip}</div>
    ${
      data.properties.publicIp
        ? `
    <div style="color: #2c3e50;">EIP: ${data.properties.publicIp}</div>
  `
        : ""
    }
  ${
    data.properties.virtualIp
      ? `
    <div style="color: #2c3e50;">VIP: ${data.properties.virtualIp}</div>
  `
      : ""
  }
    `;
    // <div style="color: #2c3e50;">节点ID: ${data.id}</div>
    // <div style="color: #2c3e50;">创建时间: ${data.properties.createTime}</div>
    // <div style="color: #2c3e50;">修改时间:  ${data.properties.updateTime}</div>

    // 动态计算悬浮窗位置
    const { clientX, clientY } = e;
    tooltip.style.left = `${clientX + 10}px`;
    tooltip.style.top = `${clientY + 10}px`;

    // 设置悬浮窗样式
    const tooltipStyle = {
      position: "absolute",
      backgroundColor: "#f9f9f9cc",
      color: "#666666",
      padding: "8px",
      borderRadius: "5px",
      fontSize: "15px",
      pointerEvents: "none",
      zIndex: "9999",
      width: "200px",
      maxHeight: "500px",
      overflowY: "auto",
      border: `2px solid ${statusMap[data.properties.status]?.color}`
    };
    Object.assign(tooltip.style, tooltipStyle);

    document.body.appendChild(tooltip);

    // 鼠标移出时移除悬浮窗
    lf.on("node:mouseleave", () => {
      tooltip.remove();
    });
  });
}
