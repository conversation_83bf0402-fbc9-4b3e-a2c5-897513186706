<template>
  <div class="node-title" ref="myNode">
    <svg
      t="1742112832587"
      class="svg-icon"
      viewBox="0 0 1024 1024"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      p-id="13558"
      width="200"
      height="200"
    >
      <path
        d="M569.088 710.4c17.664 0 32 14.336 32 32l0.0512 128.7168 4.352-0.6656c8.192-0.512 16.4864 2.1504 22.9376 7.7312l3.6352 3.7376a32 32 0 0 1-4.096 45.056l-38.4 32a32 32 0 0 1-52.48-24.576V742.4c0-17.664 14.336-32 32-32zM486.4 716.8v192a32 32 0 1 1-64 0v-124.3136l-4.6592 1.6896a32.0512 32.0512 0 0 1-26.3168-4.608l-4.4544-3.7376a32 32 0 0 1 0-45.2608l8.5504-8.3456 38.4-32A32 32 0 0 1 486.4 716.8z m128-198.4c17.664 0 32 14.336 32 32v32H716.8c17.664 0 32 14.336 32 32v204.8c0 17.664-14.336 32-32 32h-44.8a32 32 0 1 1 0-64h12.8v-140.8h-345.6v140.8h12.8c16.0768 0 29.3888 11.8272 31.6416 27.2896L384 819.2c0 17.664-14.336 32-32 32H307.2A32 32 0 0 1 275.2 819.2v-204.8c0-17.664 14.336-32 32-32h70.4v-32a32 32 0 1 1 64 0v32h140.8v-32c0-17.664 14.336-32 32-32z m-88.2176-408.5248a272.0768 272.0768 0 0 1 254.1056 174.9504l3.2768 9.216 2.816 0.2048a204.6976 204.6976 0 0 1 184.1152 193.4336l0.256 10.24c0 47.104-16.0256 91.8528-44.8512 127.8464a205.4144 205.4144 0 0 1-108.032 70.144 32 32 0 1 1-16.128-61.9008 141.4144 141.4144 0 0 0 74.24-48.2304 139.9296 139.9296 0 0 0 30.7712-87.8592 140.4928 140.4928 0 0 0-139.776-140.6976l-6.656 0.2048a32 32 0 0 1-32-24.5248 208.0256 208.0256 0 0 0-404.9408 2.816 32 32 0 0 1-37.5296 24.32 140.6464 140.6464 0 0 0-63.5904 274.0224 32 32 0 1 1-16.128 61.9008 205.4144 205.4144 0 0 1-108.032-70.144 204.6464 204.6464 0 0 1 159.7952-332.544l11.008 0.256 1.9968-5.5808a272.0768 272.0768 0 0 1 243.3536-177.8176l11.9296-0.256z"
        p-id="13559"
      ></path>
    </svg>
  </div>
</template>

<script setup lang="ts">
import variables from "../../styles/variables.module.scss";
import { ref, onMounted, reactive } from "vue";
const props = defineProps({
  properties: {
    type: Object,
    default: null
  }
});
let myNode = ref(null);
onMounted(() => {
  const colors = {
    1: variables.primaryColor,
    2: variables.dangerColor,
    3: variables.infoColor
  };

  if (props.properties?.status) {
    const color = colors[props.properties.status] || variables.dragNodeBorderColor;
    myNode.value.style.color = color;
    myNode.value.style.borderColor = color;
  }
});
</script>

<style scoped lang="scss">
.node-title {
  height: 80px;
  width: 80px;
  border: 1px solid #e6f7ff;
  box-sizing: border-box;
  padding: 6px;
  border-radius: 8px;
  margin: auto;
  cursor: pointer;
}

.node-icon {
  font-size: 80px;
}

.svg-icon {
  width: 4em;
  height: 4em;
  fill: currentColor;
  color: inherit;
}
</style>
