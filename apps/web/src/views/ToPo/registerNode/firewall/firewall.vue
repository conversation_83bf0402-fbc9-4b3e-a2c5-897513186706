<template>
  <div class="node-title" ref="myNode">
    <svg
      t="1742110511614"
      class="svg-icon"
      viewBox="0 0 1024 1024"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      p-id="4553"
      width="200"
      height="200"
    >
      <path
        d="M128 549.333h256V704H128zM576 320l-448 3.732v150.935h448zM896 256v-90.667C896 146.667 877.333 128 858.667 128h-432v128H896zM128 768v90.667C128 877.333 146.667 896 165.333 896H576V768H128zM384 128H154.134C135.467 128 128 146.667 128 165.333V256h256V128zM643.735 896h218.667C881.068 896 896 881.068 896 866.134V768H640l3.735 128zM643.735 323.732L640 474.667h256V323.732zM426.667 549.333H896V704H426.667z"
        p-id="4554"
      ></path>
    </svg>
  </div>
</template>

<script setup lang="ts">
import variables from "../../styles/variables.module.scss";
import { ref, onMounted, reactive } from "vue";
const props = defineProps({
  properties: {
    type: Object,
    default: null
  }
});
let myNode = ref(null);
onMounted(() => {
  const colors = {
    1: variables.primaryColor,
    2: variables.dangerColor,
    3: variables.infoColor
  };

  if (props.properties?.status) {
    const color = colors[props.properties.status] || variables.dragNodeBorderColor;
    myNode.value.style.color = color;
    myNode.value.style.borderColor = color;
  }
});
</script>

<style scoped lang="scss">
.node-title {
  height: 80px;
  width: 80px;
  border: 1px solid #e6f7ff;
  box-sizing: border-box;
  padding: 6px;
  border-radius: 8px;
  margin: auto;
  cursor: pointer;
}

.node-icon {
  font-size: 80px;
}

.svg-icon {
  width: 4em;
  height: 4em;
  fill: currentColor;
  color: inherit;
}
</style>
