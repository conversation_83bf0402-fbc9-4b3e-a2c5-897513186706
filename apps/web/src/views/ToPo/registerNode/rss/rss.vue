<template>
  <div class="node-title" ref="myNode">
    <svg
      t="1742110177639"
      class="svg-icon"
      viewBox="0 0 1024 1024"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      p-id="14748"
      width="200"
      height="200"
    >
      <path
        d="M828.8 160c19.424 0 35.2 17.92 35.2 40v240c0 22.08-15.776 40-35.2 40H544v64h256a32 32 0 0 1 32 32v96h32a32 32 0 0 1 32 32v128a32 32 0 0 1-32 32h-128a32 32 0 0 1-32-32v-128a32 32 0 0 1 32-32h32v-64h-224v64h32a32 32 0 0 1 32 32v128a32 32 0 0 1-32 32h-128a32 32 0 0 1-32-32v-128a32 32 0 0 1 32-32h32v-64H256v64h32a32 32 0 0 1 32 32v128a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32v-128a32 32 0 0 1 32-32h32v-96a32 32 0 0 1 32-32h256v-64H195.2c-19.424 0-35.2-17.92-35.2-40v-240C160 177.92 175.776 160 195.2 160h633.6zM192 800h64v-64H192v64z m288 0h64v-64h-64v64z m288 0h64v-64h-64v64zM288 352h64V288H288v64z m-64 64h576V224H224v192z m192-64h64V288h-64v64z m128 0h192V288h-192v64z"
        p-id="14749"
      ></path>
    </svg>
  </div>
</template>

<script setup lang="ts">
import variables from "../../styles/variables.module.scss";
import { ref, onMounted, reactive } from "vue";
const props = defineProps({
  properties: {
    type: Object,
    default: null
  }
});
let myNode = ref(null);
onMounted(() => {
  const colors = {
    1: variables.primaryColor,
    2: variables.dangerColor,
    3: variables.infoColor
  };

  if (props.properties?.status) {
    const color = colors[props.properties.status] || variables.dragNodeBorderColor;
    myNode.value.style.color = color;
    myNode.value.style.borderColor = color;
  }
});
</script>

<style scoped lang="scss">
.node-title {
  height: 80px;
  width: 80px;
  border: 1px solid #e6f7ff;
  box-sizing: border-box;
  padding: 6px;
  border-radius: 8px;
  margin: auto;
  cursor: pointer;
}

.node-icon {
  font-size: 80px;
}

.svg-icon {
  width: 4em;
  height: 4em;
  fill: currentColor;
  color: inherit;
}
</style>
