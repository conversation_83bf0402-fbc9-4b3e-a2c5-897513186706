<template>
  <div class="node-title" ref="myNode">
    <svg
      t="1742110993514"
      class="svg-icon"
      viewBox="0 0 1024 1024"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      p-id="2371"
      width="200"
      height="200"
    >
      <path
        d="M511.879586 24.082785C244.271684 24.082785 27.33396 241.020508 27.33396 508.62841s216.937723 484.545626 484.545626 484.545626 484.545626-216.937723 484.545626-484.545626S779.487488 24.082785 511.879586 24.082785z m0 898.841768c-228.810536 0-414.296143-185.485607-414.296143-414.296143S283.06905 94.332267 511.879586 94.332267c109.095014 0 208.279962 42.229163 282.274318 111.166134l-94.392474 94.392474a279.805833 279.805833 0 0 0-187.869803-72.12794c-155.117215 0-280.865475 125.74826-280.865475 280.865475s125.74826 280.865475 280.865475 280.865475c10.789087 0 21.421637-0.674318 31.897649-1.854374a64.08429 64.08429 0 0 0 53.764816 29.212418 64.156538 64.156538 0 0 0 63.831421-70.490311c78.895202-49.73095 131.359548-137.572907 131.359548-237.721166a279.78175 279.78175 0 0 0-70.791345-186.340546l94.464722-94.464723c68.106115 73.825776 109.757291 172.432738 109.757291 280.805269 0 228.798495-185.485607 414.284102-414.296143 414.284101z m85.662465-234.397742a64.156538 64.156538 0 0 0-64.156538 64.156538c0 1.926623 0.120414 3.817121 0.288993 5.70762-7.188711 0.614111-14.437629 0.999436-21.79492 0.999436-138.488053 0-250.761994-112.273942-250.761994-250.761995s112.273942-250.761994 250.761994-250.761994c63.939793 0 122.244214 23.962371 166.544497 63.349765L528.882032 470.758231a41.326058 41.326058 0 0 0-17.002446-3.708748 41.578928 41.578928 0 1 0 41.578928 41.578927c0-5.286171-1.083725-10.29539-2.889935-14.955409l150.04779-150.047789a249.714393 249.714393 0 0 1 62.025211 165.003198c0 87.011101-44.336406 163.654563-111.623706 208.617122a64.096331 64.096331 0 0 0-53.475823-28.718721z"
        p-id="2372"
      ></path>
    </svg>
  </div>
</template>

<script setup lang="ts">
import variables from "../../styles/variables.module.scss";
import { ref, onMounted, reactive } from "vue";
const props = defineProps({
  properties: {
    type: Object,
    default: null
  }
});
let myNode = ref(null);

onMounted(() => {
  let borderColor = variables.dragNodeBorderColor;
  let iconColor;
  switch (props.properties.status) {
    case 1:
      iconColor = variables.primaryColor;
      borderColor = variables.primaryColor;
      break;
    case 2:
      iconColor = variables.dangerColor;
      borderColor = variables.dangerColor;
      break;
    case 3:
      iconColor = variables.infoColor;
      borderColor = variables.infoColor;
      break;
  }
  myNode.value.style.color = iconColor;
  myNode.value.style.border = `1px solid ${borderColor}`;
});
</script>

<style scoped lang="scss">
.node-title {
  height: 80px;
  width: 80px;
  border: 1px solid #e6f7ff;
  box-sizing: border-box;
  padding: 6px;
  border-radius: 8px;
  margin: auto;
  cursor: pointer;
}

.node-icon {
  font-size: 30px;
}
</style>
