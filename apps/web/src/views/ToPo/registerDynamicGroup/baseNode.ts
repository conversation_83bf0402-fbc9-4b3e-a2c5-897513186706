import LogicFlow, { h } from "@logicflow/core";
import { PolygonNode, PolygonNodeModel } from "@logicflow/core";
import { randomNumber } from "../utils/index";

class CustomNodeView extends PolygonNode {
  // 渲染节点标签
  getLabelShape() {
    const { id, x, y, width, height, properties } = this.props.model;
    const style = this.props.model.getNodeStyle();
    return h(
      "svg",
      {
        x: x - width / 2,
        y: y - height / 2,
        width: 100,
        height: 30,
        style: "z-index: 0; background: none; overflow: auto;"
      },
      [
        properties.labelText
          ? h(
              "text",
              {
                x: width / 2,
                y: height / 2,
                fontSize: "16px",
                fill: style.stroke,
                textAnchor: "middle",
                dominantBaseline: "middle"
              },
              properties.labelText
            )
          : ""
      ]
    );
  }

  // 渲染节点的形状（矩形）
  getShape() {
    const { x, y, width, height, id } = this.props.model;
    const style = this.props.model.getNodeStyle();
    return h("g", {}, [
      h("rect", {
        ...style,
        x: x - width / 2,
        y: y - height / 2,
        width,
        height,
        id
      }),
      this.getLabelShape()
    ]);
  }
}

class CustomNodeModel extends PolygonNodeModel {
  // 默认节点的宽度和高度
  initNodeData(data) {
    super.initNodeData(data);
    this.text = {
      value: "",
      x: data.x,
      y: data.y,
      draggable: false,
      editable: true
    };
  }

  // 禁止显示连线
  getAnchors() {
    return []; // 返回空数组，禁止任何连线
  }

  // 获取节点样式
  getNodeStyle() {
    const style = super.getNodeStyle();
    return {
      ...style,
      stroke: "red", // 禁止显示边框
      fill: "#ffffff",
      rx: 10,
      ry: 10
    };
  }
}

export default {
  type: "customNode",
  view: CustomNodeView,
  model: CustomNodeModel
};
