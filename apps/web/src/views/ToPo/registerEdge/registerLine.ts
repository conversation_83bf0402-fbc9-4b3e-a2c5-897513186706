import {
  BezierEdge,
  BezierEdgeModel,
  PolylineEdge,
  PolylineEdgeModel,
  LineEdge,
  LineEdgeModel
} from "@logicflow/core";
// 直线 LineEdge, LineEdgeModel
// 折线 PolylineEdge, PolylineEdgeModel
// 贝塞尔曲线 BezierEdge, BezierEdgeModel
import { randomNumber } from "../utils/index";
class LineModel extends LineEdgeModel {
  // getAdjustPointShape(x, y, edgeModel) {
  //   return h("g", {}, [
  //     h("image", {
  //       x: x - 9,
  //       y: y - 9,
  //       width: 18,
  //       height: 18,
  //       cursor: "move",
  //       href: "data:image/svg+xml;base64,PCFET0NUWVBFIHN2ZyBQVUJMSUMgIi0vL1czQy8vRFREIFNWRyAxLjEvL0VOIiAiaHR0cDovL3d3dy53My5vcmcvR3JhcGhpY3MvU1ZHLzEuMS9EVEQvc3ZnMTEuZHRkIj48c3ZnIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIHdpZHRoPSIyMnB4IiBoZWlnaHQ9IjIycHgiIHZlcnNpb249IjEuMSI+PGNpcmNsZSBjeD0iMTEiIGN5PSIxMSIgcj0iNyIgc3Ryb2tlPSIjZmZmIiBmaWxsPSIjMjliNmYyIi8+PGNpcmNsZSBjeD0iMTEiIGN5PSIxMSIgcj0iMyIgc3Ryb2tlPSIjZmZmIiBmaWxsPSJ0cmFuc3BhcmVudCIvPjwvc3ZnPg=="
  //     })
  //   ]);
  // }
  createId() {
    return randomNumber();
  }
  initEdgeData(data) {
    super.initEdgeData(data);
    if (data.properties.status == "2") {
      this.isAnimation = true;
    }
  }

  getEdgeAnimationStyle() {
    const style = super.getEdgeAnimationStyle();
    style.stroke = "#f56c6c";
    style.strokeDasharray = "5 5";
    // style.animationDuration = "10s";
    return style;
  }
  constructor(data, graphModel) {
    super(data, graphModel);
    this.menu = [
      {
        text: "配置",
        callback: res => {
          graphModel.eventCenter.emit("edge:app-config", res); //发出事件
        }
      },
      {
        text: "删除",
        callback(res) {
          graphModel.deleteEdgeById(res.id);
        }
      }
    ];

    this.style = {
      stroke: data.properties.status == "2" ? "#f56c6c" : "#409eff", // 根据状态设置颜色
      strokeWidth: 2 // 设置线条宽度
      // strokeDasharray: "5,5" // 设置虚线样式
    };
  }
}

export default {
  type: "myLine",
  view: LineEdge,
  model: LineModel
};
