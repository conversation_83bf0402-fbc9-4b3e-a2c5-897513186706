import { PolylineEdge, PolylineEdgeModel } from "@logicflow/core";
import { randomNumber } from "../utils/index";
class PolylineModel extends PolylineEdgeModel {
  createId() {
    return randomNumber();
  }
  initEdgeData(data) {
    super.initEdgeData(data);
    if (data.properties.frontend_status == "2") {
      this.isAnimation = true;
    }
  }

  getEdgeAnimationStyle() {
    const style = super.getEdgeAnimationStyle();
    style.stroke = "#f56c6c";
    style.strokeDasharray = "5 5";
    // style.animationDuration = "10s";
    return style;
  }
  constructor(data, graphModel) {
    super(data, graphModel);
    this.menu = [
      {
        text: "配置",
        callback: res => {
          graphModel.eventCenter.emit("edge:app-config", res); //发出事件
        }
      },
      {
        text: "删除",
        callback(res) {
          graphModel.deleteEdgeById(res.id);
        }
      }
    ];

    this.style = {
      stroke: data.properties.frontend_status == "2" ? "#f56c6c" : "#409eff", // 根据状态设置颜色
      strokeWidth: 2 // 设置线条宽度
      // strokeDasharray: "5,5" // 设置虚线样式
    };
  }
}

export default {
  type: "myPolyline",
  view: PolylineEdge,
  model: PolylineModel
};
