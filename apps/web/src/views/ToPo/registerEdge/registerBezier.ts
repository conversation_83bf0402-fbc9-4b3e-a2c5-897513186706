import { BezierEdge, BezierEdgeModel } from "@logicflow/core";
import { randomNumber } from "../utils/index";
class BezierModel extends BezierEdgeModel {
  createId() {
    return randomNumber();
  }
  // getData() {
  //   const data = super.getData();
  //   console.log(data);

  //   data.sourceAnchorId = this.sourceAnchorId;
  //   data.targetAnchorId = this.targetAnchorId;
  //   return data;
  // }
  setAttributes() {
    // console.log("setAttributes this ===>>>", this);
    if (this.properties.status == 2) {
      this.style.stroke = "#f56c6c";
      this.isAnimation = true;
    } else if (this.properties.status == 3) {
      this.style.stroke = "#9E9E9E";
      this.style.strokeDasharray = "3 3";
    }
  }
  getEdgeStyle() {
    const style = super.getEdgeStyle();
    return {
      ...style,
      // stroke: "#409eff",
      // strokeDasharray: "3 3",
      arrow: this.properties.arrow,
      arrowConfig: this.properties.arrowConfig
    };
  }
  initEdgeData(data) {
    // console.log("initEdgeData ===>>>", this);

    super.initEdgeData(data);
    if (data.properties.status == 2) {
      this.isAnimation = true;
    } else if (data.properties.status == 3) {
      this.style.stroke = "#9E9E9E";
      // this.style.strokeDasharray = "3 3";
    }
  }

  constructor(data, graphModel) {
    // console.log("graphModel ===>>>", graphModel);
    super(data, graphModel);
    this.menu = [
      // {
      //   text: "配置",
      //   callback: res => {
      //     graphModel.eventCenter.emit("edge:app-config", res); //发出事件
      //   }
      // },
      // {
      //   text: "编辑",
      //   callback: res => {
      //     graphModel.eventCenter.emit("edge:app-edit", res); //发出事件
      //   }
      // },
      {
        text: "删除",
        callback(res) {
          graphModel.deleteEdgeById(res.id);
        }
      }
    ];

    this.style = {
      stroke:
        data.properties.status == "2"
          ? "#f56c6c"
          : data.properties.status == "3"
            ? "#9E9E9E"
            : "#409eff", // 根据状态设置颜色
      strokeWidth: 2 // 设置线条宽度
    };
  }
}

export default {
  type: "myBezier",
  view: BezierEdge,
  model: BezierModel
};
