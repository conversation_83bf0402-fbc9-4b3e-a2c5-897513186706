<template>
  <div>
    <el-form
      ref="propertyFormRef"
      :model="propertyForm"
      :inline-message="true"
      :rules="rules"
      label-position="top"
    >
      <el-form-item label="连线类型" prop="id">
        <el-select
          v-model="propertyForm.edgeType"
          style="width: 300px"
          :disabled="edgeTypeDisabled"
        >
          <el-option
            v-for="(x, index) in edgeTypeOption"
            :key="index"
            :disabled="x.disabled"
            :value="x.value"
            :label="x.label"
          />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="显示箭头" prop="showArrow">
        <el-switch v-model="propertyForm.showArrow" active-text="显示" inactive-text="隐藏" />
      </el-form-item> -->
    </el-form>
    <div class="mt15">
      <el-button @click="cancelFunc"> 取消 </el-button>
      <el-button type="primary" @click="confirmFunc"> 确定 </el-button>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted, reactive } from "vue";
const props = defineProps({
  nodeData: Object,
  lf: Object
});
const emit = defineEmits();

let propertyForm = reactive({
  id: "line",
  edgeType: "",
  showArrow: true // 新增箭头显示控制项
});

let rules = reactive({
  id: [{ required: true, message: "连线类型不能为空" }]
});
let edgeTypeOption = ref([
  {
    value: "myLine",
    label: "直线"
  },
  {
    value: "myPolyline",
    label: "直角折线"
  },
  {
    value: "myBezier",
    label: "贝塞尔曲线"
  }
]);
let edgeTypeDisabled = ref(false);
let propertyFormRef = ref(null);

//更新节点属性
const setProperties = () => {
  props.lf.setProperties(props.nodeData.id, {
    dnsId: propertyForm.id,
    edgeType: propertyForm.edgeType,
    ...props.nodeData
  });
};

//确定
const confirmFunc = () => {
  propertyFormRef.value.validate(valid => {
    if (valid) {
      // 设置边属性（网页1的边样式控制方法）
      const edgeConfig = {
        edgeType: propertyForm.edgeType
        // arrowConfig: {
        //   offset: propertyForm.showArrow ? 3 : 0,
        //   verticalLength: propertyForm.showArrow ? 13 : 0
        // }
      };

      props.lf?.changeEdgeType(props.nodeData.id, propertyForm.edgeType);
      setProperties();
      emit("closed");
    }
  });
};

//取消
const cancelFunc = () => {
  emit("closed");
};

onMounted(() => {
  console.log("连线props.nodeData ===>>>", props.nodeData);
  propertyForm.edgeType = props.nodeData.type;
});
</script>
<style scoped></style>
