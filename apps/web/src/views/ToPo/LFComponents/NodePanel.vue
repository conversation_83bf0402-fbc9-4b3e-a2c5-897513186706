<template>
  <div class="node-panel">
    <div class="node-list">
      <div
        v-for="(node, index) in nodeTypes"
        :key="index"
        class="node-item"
        :class="{
          'selection-item': node.type === 'region',
          'active-selection': node.type === 'region' && isSelecting
        }"
        @mousedown="handleNodeAction(node)"
      >
        <SvgIcons :icon-class="node.type" class="node-icon" />
        <span class="node-label">
          {{ node.type === "region" ? (isSelecting ? "取消框选" : "框选") : node.label }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineProps, onUnmounted } from "vue";
import SvgIcons from "./SvgIcons/index.vue";
import { randomNumber } from "../utils/index";

const props = defineProps({
  lf: {
    type: Object,
    required: true
  }
});

// 选区状态跟踪
const isSelecting = ref(false);
let selectionHandler: any = null;

// 节点配置
const nodeTypes = ref([
  {
    type: "region",
    label: "框选"
  },
  { type: "myCustomGroup", label: "分组" },
  { type: "text", label: "文本框" },
  { type: "firewall", label: "防火墙" },
  // { type: "WEBfw", label: "WEB防火墙" },
  { type: "cdn", label: "负载均衡" },
  { type: "gateway", label: "网关" },
  { type: "dns", label: "服务器" },
  { type: "serverIcon", label: "虚拟机" },
  { type: "jwd", label: "NAT网关" },
  { type: "rss", label: "VPN" },
  { type: "log", label: "日志审计" },
  { type: "database", label: "数据库审计" },
  { type: "machine", label: "主机安全" },
  { type: "Webpage", label: "网页防篡改" },
  { type: "pw", label: "密码服务" },
  { type: "loophole", label: "漏洞扫描" },
  { type: "fort", label: "堡垒机" },
  { type: "connector", label: "端口" },
  { type: "applet", label: "小程序" },
  { type: "appIcon", label: "APP" },
  { type: "nginx", label: "Nginx" },
  { type: "mobileIcon", label: "H5" },
  { type: "nacos", label: "Nacos" },
  { type: "redis", label: "Redis" }
]);

// 处理节点操作
const handleNodeAction = (node: any) => {
  console.log(props.lf);

  if (node.type === "region") {
    toggleSelection();
  } else {
    handleNodeDrag(node);
  }
};

// 切换选区状态
const toggleSelection = () => {
  if (!props.lf.extension?.selectionSelect) return;

  const selection = props.lf.extension.selectionSelect;
  isSelecting.value = !isSelecting.value;

  if (isSelecting.value) {
    openSelection(selection);
  } else {
    closeSelection(selection);
  }
};

// 开启选区模式
const openSelection = (selection: any) => {
  // 清除旧监听
  if (selectionHandler) {
    selectionHandler.remove();
  }

  selection.openSelectionSelect();
  selection.setSelectionSense(false, false);

  // 监听选区事件
  selectionHandler = props.lf.on("selection:selected", () => {
    // 保持选区模式开启
  });

  // 监听取消事件
  props.lf.once("selection:cancel", () => {
    isSelecting.value = false;
    selection.closeSelectionSelect();
  });
};

// 关闭选区模式
const closeSelection = (selection: any) => {
  selection.closeSelectionSelect();
  if (selectionHandler) {
    selectionHandler.remove();
    selectionHandler = null;
  }
};

// 处理节点拖拽
const handleNodeDrag = (node: any) => {
  if (isSelecting.value) return;

  const nodeId = randomNumber();
  props.lf.dnd.startDrag({
    type: node.type,
    text: node.label,
    id: nodeId
  });

  props.lf.once("node:add", ({ data }: any) => {
    props.lf.setProperties(data.id, {
      ...node,
      style: {
        fill: node.style?.color || "#fff",
        stroke: node.style?.color || "#000"
      }
    });
  });
};

// 组件卸载时清理
onUnmounted(() => {
  if (props.lf.extension?.selectionSelect && isSelecting.value) {
    closeSelection(props.lf.extension.selectionSelect);
  }
});
</script>

<style lang="scss" scoped>
.node-panel {
  position: absolute;
  left: 5px;
  top: 20px;
  width: 80px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  padding: 12px 0;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 1000;
  max-height: 60vh;
  overflow-y: auto;
  user-select: none;

  &::-webkit-scrollbar {
    width: 0;
  }
}

.node-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.node-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 5px;
  cursor: grab;
  transition: all 0.2s;
  position: relative;

  // margin-bottom: 5px;
  &:hover {
    background: rgba(64, 158, 255, 0.1);

    &::after {
      content: "";
      position: absolute;
      right: -8px;
      background: #409eff;
    }
  }

  &:active {
    cursor: grabbing;
  }

  &.selection-item {
    cursor: pointer;

    &.active-selection {
      &::after {
        animation: pulse 1.5s infinite;
      }

      .node-icon,
      .node-label {
        color: #ff8c00 !important;
      }
    }
  }
}

.node-icon {
  font-size: 10px;
  margin-bottom: 4px;
  color: #606266;
}

.node-label {
  font-size: 12px;
  color: #606266;
  text-align: center;
  line-height: 1.2;
  padding: 0 4px;
  word-break: break-all;
}

@keyframes pulse {
  0% {
    opacity: 0.8;
  }

  50% {
    opacity: 0.4;
  }

  100% {
    opacity: 0.8;
  }
}
</style>
