<template>
  <!-- 新增状态说明 -->
  <div class="status-legend">
    <span class="title-text">状态：</span>
    <span class="status-item">
      <span class="status-square" style="background: #409eff"></span>
      <span class="status-text">正常</span>
    </span>
    <span class="status-item">
      <span class="status-square" style="background: #f56c6c"></span>
      <span class="status-text">异常</span>
    </span>
    <span class="status-item">
      <span class="status-square" style="background: #e6a23c"></span>
      <span class="status-text">警告</span>
    </span>
    <span class="status-item">
      <span class="status-square" style="background: #959595"></span>
      <span class="status-text">关机</span>
    </span>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted, reactive } from "vue";
</script>
<style lang="scss" scoped>
.status-legend {
  display: inline-flex;
  margin-left: 20px;
  gap: 15px;
}
.status-item {
  display: flex;
  align-items: center;
  gap: 5px;
}
.status-square {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 3px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
.status-text {
  font-size: 14px;
  color: #666;
}
.title-text {
  font-size: 14px;
  color: #666;
}
</style>
