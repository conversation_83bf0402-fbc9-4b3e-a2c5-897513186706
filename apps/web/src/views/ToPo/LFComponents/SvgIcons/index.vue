<template>
  <svg :class="svgClass" aria-hidden="true" v-if="iconType == 'svg'">
    <use :xlink:href="iconName" :fill="color" />
  </svg>
</template>

<script lang="ts">
import { computed, defineComponent } from "vue";

export default defineComponent({
  props: {
    iconClass: {
      type: String,
      required: false
    },
    className: {
      type: String,
      default: ""
    },
    color: {
      type: String,
      default: ""
    }
  },
  setup(props) {
    return {
      iconName: computed(() => `#icon-${props.iconClass}`),
      iconType: computed(() => {
        return "svg";
      }),
      svgClass: computed(() => {
        if (props.className) {
          return `svg-icon ${props.className}`;
        }
        return "svg-icon";
      })
    };
  },
  mounted() {
    // console.log(this.iconName);
  }
});
</script>

<style scope lang="scss">
.sub-el-icon,
.nav-icon {
  display: inline-block;
  font-size: 15px;
  margin-right: 12px;
  position: relative;
}

.svg-icon {
  width: 4em;
  height: 4em;
  position: relative;
  fill: currentColor;
}
</style>
