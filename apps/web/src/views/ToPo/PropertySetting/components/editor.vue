<!-- 编辑 -->
<template>
  <div>
    <el-form
      ref="propertyFormRef"
      :model="propertyForm"
      :inline-message="true"
      :rules="rules"
      label-position="top"
      :disabled="flowDetail.status == '2'"
    >
      <el-form-item label="名称" prop="name">
        <el-input v-model="propertyForm.name" clearable> </el-input>
      </el-form-item>
      <el-form-item label="描述" prop="remark">
        <el-input v-model="propertyForm.remark" type="textarea" :rows="2"> </el-input>
      </el-form-item>
    </el-form>
    <div class="mt15" v-if="flowDetail.status != '2'">
      <el-button @click="cancelFunc"> 取消 </el-button>
      <el-button type="primary" @click="confirmFunc"> 确定 </el-button>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted, reactive } from "vue";
import { useRouter, useRoute } from "vue-router";
import { networkView, topoView, editTopo, delTopo } from "@/api/topo/index";
const router = useRouter();
const props = defineProps({
  nodeData: Object,
  lf: Object || String,
  //详情
  flowDetail: {
    type: Object,
    default: () => {
      return {};
    }
  }
});
const emit = defineEmits();
let propertyForm = reactive({
  name: "",
  remark: "",
  type: props.nodeData.type,
  id: props.nodeData.id,
  ip: props.nodeData.properties.ip,
  networkSegment: props.nodeData.properties.networkSegment
});
let rules = reactive({
  name: [
    { required: true, message: "名称不能为空" },
    {
      max: 50,
      message: "最大50字符"
    }
  ],
  remark: [
    {
      max: 50,
      message: "最大50字符"
    }
  ]
});
let showGlobalContext = ref(false);
let globalContextFlag = ref("");
let tableIndex = ref(null);
let propertyFormRef = ref(null);

//更新节点属性
const setProperties = () => {
  props.lf.setProperties(props.nodeData.id, {
    name: propertyForm.name,
    remark: propertyForm.remark
  });
};

//确定
const confirmFunc = () => {
  propertyFormRef.value.validate(valid => {
    if (valid) {
      ElMessage.success("编辑成功");
      setProperties();
      props.lf.updateText(props.nodeData.id, propertyForm.name);
      emit("closed");
    }
  });
};

//取消
const cancelFunc = () => {
  emit("closed");
};

onMounted(() => {
  console.log("props.nodeData ===>>>", props.nodeData);
  console.log("props.flowDetail ===>>>", props.flowDetail);

  propertyForm.name = props.nodeData.properties.name;
  propertyForm.remark = props.nodeData.properties.remark ? props.nodeData.properties.remark : "";
});
</script>
<style scoped>
.service-name {
  cursor: pointer;
  color: #0064c8;
  vertical-align: middle;
}
</style>
