<template>
  <div>
    <el-drawer
      :wrapperClosable="true"
      v-model="showNodeAttribute"
      direction="rtl"
      size="50%"
      :close-on-click-modal="false"
      @closed="closed"
      :show-close="operate !== 'add'"
    >
      <template #header>
        <div v-if="operate === 'detail'">
          {{ `${nodeData.name} 详情` }}
        </div>
        <div v-else-if="operate === 'edit'">
          <!-- {{ getLabelByValue(nodeData.type, pixelOption, "value", "label") }} -->
          {{ "编辑节点" }}
        </div>
        <div v-else-if="operate === 'add'">
          {{ "新增节点" }}
        </div>
        <div v-else-if="operate === 'addText'">
          {{ "新增文本" }}
        </div>
        <div v-else-if="operate === 'edge'">
          {{ "连线配置" }}
        </div>
      </template>
      <div style="padding: 15px 5px">
        <Details
          v-if="operate === 'detail'"
          :title="title"
          :nodeData="nodeData"
          :lf="lf"
          :flowDetail="flowDetail"
          @closed="closed"
        />
        <Editor
          v-if="operate === 'edit'"
          :title="title"
          :nodeData="nodeData"
          :lf="lf"
          :flowDetail="flowDetail"
          @closed="closed"
        />
        <AddNode
          v-if="operate === 'add'"
          :title="title"
          :nodeData="nodeData"
          :lf="lf"
          :flowDetail="flowDetail"
          @closed="closed"
        />
        <AddText
          v-if="operate === 'addText'"
          :title="title"
          :nodeData="nodeData"
          :lf="lf"
          :flowDetail="flowDetail"
          @closed="closed"
        />
        <!-- 连线 -->
        <myBezier
          v-if="operate === 'edge'"
          :title="title"
          :nodeData="nodeData"
          :lf="lf"
          :flowDetail="flowDetail"
          @closed="closed"
        ></myBezier>
      </div>
    </el-drawer>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted, reactive } from "vue";
import { getLabelByValue } from "../utils/index";
import myBezier from "../registerEdge/myBezier.vue";
import Details from "./components/details.vue";
import Editor from "./components/editor.vue";
import AddNode from "./components/addNode.vue";
import AddText from "./components/addText.vue";

const props = defineProps({
  //标题
  title: {
    type: String,
    default: ""
  },
  nodeData: Object,
  lf: Object,
  operate: String,
  //详情
  flowDetail: {
    type: Object,
    default: () => {
      return {};
    }
  }
});
const emit = defineEmits();

let showNodeAttribute = ref(true);

//弹窗关闭
const closed = () => {
  emit("closed", true);
};

onMounted(() => {});
</script>
<style scoped>
:deep(.el-drawer__header) {
  margin-bottom: 5px !important;
}
</style>
