import variables from "../styles/variables.module.scss";
export const BASE_CONFIG = {
  background: {
    backgroundColor: "#ffffff"
  },
  // pluginsOptions: {
  //   label: {
  //     isMultiple: true,
  //     maxCount: 3,
  //     labelWidth: 80,
  //     // textOverflowMode -> 'ellipsis' | 'wrap' | 'clip' | 'nowrap' | 'default'
  //     textOverflowMode: "wrap"
  //   }
  // },
  // 键盘快捷键
  keyboard: {
    enabled: false
  },
  // 背景底色
  grid: {
    size: 10,
    visible: false,
    type: "dot", // dot 点状 mesh 线状
    config: {
      color: "#a0a0a0", // 设置网格的颜色
      thickness: 1 // 设置网格线的宽度
    }
  },
  autoLayout: {
    type: "dagre", // 使用 dagre 自动布局
    options: {
      // rankdir: "TB" // 设置布局方向：'TB' 表示从上到下
    }
  },
  allowResize: true,
  // adjustEdge: true, //允许调整边
  adjustEdgeStartAndEnd: true, //是否允许拖动边的端点来调整连线
  edgeSelectedOutline: true, //鼠标 hover 的时候显示边的外框
  // edgeTextDraggable: true,
  hoverOutline: true, // hover 的时候是否显示节点的外框
  nodeTextEdit: false, //节点是否可编辑。false不可编辑
  edgeTextEdit: false, //边是否可编辑。false不可编辑
  autoExpand: true, //点拖动靠近画布边缘时是否自动扩充画布
  textEdit: false, //是否开启文本编辑
  snapline: true, //启用磁性对齐线
  // adjustNodePosition: true, // 自动调整节点位置
  multipleSelectKey: "alt"
};

// ...静默模式配置
export const SILENT_CONFIG = {
  stopZoomGraph: false,
  stopScrollGraph: false,
  stopMoveGraph: false,
  adjustEdge: false,
  adjustEdgeStartAndEnd: false,
  adjustNodePosition: false,
  hideAnchors: true,
  nodeSelectedOutline: true,
  nodeTextEdit: false,
  edgeTextEdit: false,
  nodeTextDraggable: false,
  edgeTextDraggable: false,
  allowResize: false,
  autoExpand: false
};

export const configuration = {
  // 箭头样式
  // arrow: false,
  arrow: {
    offset: 0, // 箭头长度
    verticalLength: 0, // 箭头垂直于边的距离
    fill: "none",
    stroke: "#00796b"
  },
  // 节点样式
  baseNode: {
    fill: "#FFFFFF",
    stroke: "#000000",
    strokeWidth: 1
  },
  // circle节点样式
  circle: {
    stroke: "#000000",
    strokeWidth: 1
  },
  // rect节点样式
  rect: {
    fill: "#FFFFFF",
    stroke: "#000000",
    outlineColor: "#88f",
    strokeWidth: 1
  },
  // polygon节点样式
  polygon: {
    strokeWidth: 1
  },
  // 连线样式
  polyline: {
    stroke: "#000000",
    hoverStroke: "#000000",
    selectedStroke: "#000000",
    strokeWidth: 1
  },
  // 节点文本样式
  nodeText: {
    color: "#000000",
    // overflowMode: "autoWrap", //超出换行显示
    overflowMode: "default", //超出不处理
    // overflowMode: "ellipsis", //超出隐藏，显示省略符号
    fontSize: 14
  },
  // 连线文本样式
  edgeText: {
    // color: "#000000",
    // textWidth: 100,
    // overflowMode: "autoWrap",
    // fontSize: 12
    // background: {
    //   fill: variables.dragPanelBgColor
    // }
  },
  // 对齐线样式
  snapline: {
    stroke: "#000",
    strokeWidth: 1
  },
  group: {
    allowResize: true,
    controlPoint: {
      width: 8,
      height: 8,
      fill: "#fff",
      stroke: "#1890ff"
    },
    text: {
      textPosition: "top",
      textOffset: [0, -10]
    }
  }
};
