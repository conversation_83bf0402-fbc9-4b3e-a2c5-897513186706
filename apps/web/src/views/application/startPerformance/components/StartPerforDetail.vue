<template>
  <div>
    <div class="flex justify-between indicator-wrapper mt-10px mb-10px">
      <BaseEcharts
        v-loading="statLaunchSlowCountLoading"
        :options="statErrorRateData"
        height="250px"
      />
      <BaseEcharts
        v-loading="statLaunchCrashCountLoading"
        :options="statSlowRateData"
        height="250px"
      />
      <BaseEcharts
        :options="averageStartTimeChartData"
        v-loading="avgDurationLoading"
        height="250px"
      />
    </div>
    <div class="log-container">
      <div class="search-container">
        <el-form>
          <el-form-item class="input-group">
            <label>设备网络：</label>
            <el-select placeholder="请选择设备网络" v-model="pageParams.netDevice" clearable>
              <el-option v-for="item in netDeviceData" :key="item" :value="item" />
            </el-select>
          </el-form-item>
          <el-form-item class="input-group">
            <label>APP版本：</label>
            <el-select placeholder="请选择APP版本" v-model="pageParams.appVersion" clearable>
              <el-option v-for="item in appVersionData" :key="item" :value="item" />
            </el-select>
          </el-form-item>
          <el-form-item style="display: flex; margin-top: 20px">
            <el-popconfirm
              @confirm="resetSearch"
              title="确定清空吗？"
              confirm-button-text="确定"
              cancel-button-text="取消"
              icon="el-icon-warning"
              :hide-after="0"
            >
              <template #reference>
                <el-button type="danger" plain style="flex: 1; margin-right: 10px">
                  清空
                </el-button>
              </template>
            </el-popconfirm>
            <el-button type="primary" style="flex: 1" @click="search"> 查询 </el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="tabel-container">
        <MyTable
          :data="launchStatList"
          :total="launchStatTotal"
          v-loading="loading"
          style="width: 100%"
          @sizeChange="handleSizeChange"
          @currentChange="handleCurrentChange"
          @sort-change="handleSortChange"
          :default-sort="{
            prop: 't0 && t2 && t4 && time',
            order: 'descending'
          }"
        >
          <my-column property="deviceName" label="设备名称" />
          <my-column property="type" label="启动类型" />
          <my-column property="crf" label="启动结果" />
          <my-column property="t0" label="启动总耗时"> </my-column>
          <my-column property="t2" label="APP耗时"> </my-column>
          <my-column property="t4" label="首屏耗时"></my-column>
          <my-column property="time" label="时间"> </my-column>
        </MyTable>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  getStatLaunchSlowCount,
  getStatLaunchCrashCount,
  getLaunchDetailList,
  ILaunchDetailListResponse,
  ILaunchDetailItem,
  getLaunchDevice,
  getLaunchAppVersion
} from "@/api/application/startPerformance";
import { getChartOptions } from "@/components/baseEcharts/chartsOptions";
import { breadcrumbStore } from "@/store/modules/breadcurmb";
import { getAverageLaunchDurationChart } from "@/api/application/index";
import { applicationStore } from "@/store/modules/application";

const useBreadcrumbStore = breadcrumbStore();
interface IIndicatordata {
  indicator?: string;
  unit?: string;
  value?: number | string;
  color?: string;
  errorInfo?: string;
}
const loading = ref(false);
const useApplicationStore = applicationStore();
const statLaunchSlowCountLoading = ref(false);
const statLaunchCrashCountLoading = ref(false);
const avgDurationLoading = ref(false);

const launchStatList = ref<ILaunchDetailItem[]>([]);
const launchStatTotal = ref(0);
const netDeviceData = ref<string[]>([]);
const appVersionData = ref<string[]>([]);

const statErrorRateData = ref({});
const statSlowRateData = ref({});
const averageStartTimeChartData = ref({});
onMounted(() => {
  getLaunchStatListData();
  getStatSlowCountData();
  getStatCtashCountData();
  getAvgDurationData();
  selectAllData();
});

//列表参数
const pageParams = ref({
  page: 1,
  rows: 10,
  netDevice: "",
  appVersion: "",
  sort: "",
  order: ""
});
async function getLaunchStatListData() {
  loading.value = true;
  try {
    const res: ILaunchDetailListResponse = await getLaunchDetailList({
      net: pageParams.value.netDevice,
      appVersion: pageParams.value.appVersion,
      device: useBreadcrumbStore.breadcrumbTitle,
      page: pageParams.value.page,
      rows: pageParams.value.rows
    });
    if (res.code === 0) {
      launchStatList.value = res.records.map(item => {
        return {
          ...item,
          type: item.type === "cold" ? "冷启动" : "热启动",
          crf: item.crf === 0 ? "未崩溃" : "发生崩溃",
          t0: item.t0 + "ms",
          t2: item.t2 + "ms",
          t4: item.t4 + "ms"
        };
      });
      launchStatTotal.value = Number(res.total);
    }
    loading.value = false;
  } catch (error) {
    loading.value = false;
    console.log(error);
  }
}
// 获取错误率统计数据
const getStatSlowCountData = async () => {
  statLaunchSlowCountLoading.value = true;
  try {
    const res = await getStatLaunchSlowCount({
      device: useBreadcrumbStore.breadcrumbTitle
    });
    if (res.code === 0 && res.entity) {
      const originalTimes = res.entity.datas.map((data: { time: string }) => data.time);
      const seriesData = [res.entity.datas.map((data: { value: number }) => data.value)];
      const color = ["#445fde"];
      const params = {
        typ: res.entity.granularity,
        color: color,
        titleType: "慢启动统计",
        originalTimes: originalTimes,
        seriesData: seriesData
      };
      statErrorRateData.value = getChartOptions(params);
    }
    statLaunchSlowCountLoading.value = false;
  } catch (error) {
    statLaunchSlowCountLoading.value = false;
    console.error("Error logs:", error);
  }
};
// 获取慢请求统计数据的函数
const getStatCtashCountData = async () => {
  try {
    statLaunchCrashCountLoading.value = true;
    const res = await getStatLaunchCrashCount({
      device: useBreadcrumbStore.breadcrumbTitle
    });
    if (res.code === 0 && res.entity) {
      const originalTimes = res.entity.datas.map((data: { time: string }) => data.time);
      const seriesData = [res.entity.datas.map((data: { value: number }) => data.value)];
      const color = ["#445fde"];
      const params = {
        typ: res.entity.granularity,
        color: color,
        titleType: "启动崩溃统计",
        originalTimes: originalTimes,
        seriesData: seriesData
      };
      statSlowRateData.value = getChartOptions(params);
    }
    statLaunchCrashCountLoading.value = false;
  } catch (error) {
    statLaunchCrashCountLoading.value = false;
    console.error("Error logs:", error);
  }
};

const getAvgDurationData = async () => {
  const queue = {
    appName: useBreadcrumbStore.appOption,
    appid: useApplicationStore.appId,
    device: useBreadcrumbStore.breadcrumbTitle
  };
  try {
    avgDurationLoading.value = true;
    const res = await getAverageLaunchDurationChart(queue);
    if (res.code === 0 && res.entity) {
      const originalTimes = res.entity.datas.map((data: { time: string }) => data.time);
      const seriesData = [res.entity.datas.map((data: { value: number }) => data.value)];
      const color = ["#445fde"];
      const params = {
        typ: res.entity.granularity,
        color: color,
        titleType: "启动平均耗时",
        name: " ms",
        originalTimes: originalTimes,
        seriesData: seriesData,
        type: "line"
      };
      averageStartTimeChartData.value = getChartOptions(params);
    }
    avgDurationLoading.value = false;
  } catch (error) {
    avgDurationLoading.value = false;
    console.error("Error logs:", error);
  }
};
const selectAllData = async function () {
  try {
    const [netDevice, appVersion] = await Promise.all([
      getLaunchDevice({
        device: useBreadcrumbStore.breadcrumbTitle
      }),
      getLaunchAppVersion({
        device: useBreadcrumbStore.breadcrumbTitle
      })
    ]);

    netDeviceData.value = netDevice.records as string[];
    appVersionData.value = appVersion.records as string[];
  } catch (error) {
    console.log(error);
  }
};

const search = async () => {
  getLaunchStatListData();
};
const resetSearch = () => {
  pageParams.value.netDevice = "";
  pageParams.value.appVersion = "";
  getLaunchStatListData();
};
const handleSortChange = (val: any) => {
  const order = val.order;
  const sort = val.prop;
  if (order === "ascending") {
    pageParams.value.order = "0";
  } else {
    pageParams.value.order = "1";
  }
  pageParams.value.sort = sort;
  getLaunchStatListData();
};

const handleSizeChange = (val: number) => {
  pageParams.value.rows = val;
  getLaunchStatListData();
};
//分页
const handleCurrentChange = (val: number) => {
  pageParams.value.page = val;
  getLaunchStatListData();
};
</script>

<style lang="scss" scoped>
.indicator-wrapper > *:not(:last-child) {
  margin-right: 8px;
  box-sizing: border-box;
}
.url-name {
  cursor: pointer;
  color: #0064c8;
  vertical-align: middle;
}
.log-container {
  display: flex;
  overflow-y: auto;
  overflow-x: hidden;
  // min-height: 400px;

  .search-container {
    width: 260px;
    height: 250px;
    border: 1px solid #eee;
    background: #ffffff;
    padding: 15px 15px 25px 15px;
    margin-right: 10px;
    z-index: 99;
    top: 186px;
    left: 20px;
  }

  .tabel-container {
    flex: 1;
    width: calc(100% - 260px);
  }
}
.input-group {
  margin-bottom: 10px;
}
</style>
