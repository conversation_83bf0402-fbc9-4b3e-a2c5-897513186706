<template>
  <div v-loading="loading" class="flex justify-between indicator-wrapper">
    <Indicator :value="requestCountData.value" :unit="'请求总数'"></Indicator>
    <Indicator :value="errorRate.value" :unit="'错误率（%）'" :color="'#f56c6c'"></Indicator>
    <Indicator :value="netSlowRate.value" :unit="'慢请求率（%）'"></Indicator>
    <Indicator :value="netAvtDutation.value" :unit="'平均耗时（ms）'"></Indicator>
  </div>
  <div class="flex justify-between indicator-wrapper mt-10px">
    <BaseEcharts v-loading="statErrorRateLoading" :options="statErrorRateData" height="250px" />
    <BaseEcharts v-loading="statSlowRateLoading" :options="statSlowRateData" height="250px" />
    <BaseEcharts v-loading="statAvgDurationLoading" :options="statAvgDurationData" height="250px" />
  </div>

  <MyTable
    class="mt-10px"
    :data="netWorkRequestList"
    :total="netWorkRequestTotal"
    v-loading="tableLoading"
    style="width: 100%"
    @sizeChange="handleSizeChange"
    @currentChange="handleCurrentChange"
    :default-sort="{
      prop: 'count && slowCount && errorCount && avgDuration && slowRate && errorRate',
      order: 'descending'
    }"
    @sort-change="handleSortChange"
  >
    <my-column property="summary" label="请求地址" width="350">
      <template #default="scope">
        <span class="url-name" @click="viewDetail(scope)">
          {{ scope.row.url }}
        </span>
      </template>
    </my-column>
    <my-column property="method" label="请求方法"></my-column>
    <my-column property="count" label="请求总数" sortable="custom" />
    <my-column property="slowCount" label="慢请求数" sortable="custom"> </my-column>
    <my-column property="slowRate" label="慢请求率"> </my-column>
    <my-column property="errorCount" label="错误数" sortable="custom"> </my-column>
    <my-column property="errorRate" label="错误率"> </my-column>
    <my-column property="avgDuration" label="平均耗时" sortable="custom"> </my-column>
  </MyTable>
</template>

<script setup lang="ts">
import { useRouter } from "vue-router";
import { breadcrumbStore } from "@/store/modules/breadcurmb";
import {
  getNetCount,
  getNetAvgDuration,
  getNetErrorRate,
  getNetSlowRate,
  getNetStartList,
  INetStartlist,
  INetItem,
  getStatErrorRate,
  getStatAvgDurationRate,
  getStatSlowRate
} from "@/api/application/netWorkRequest";
import { getChartOptions } from "@/components/baseEcharts/chartsOptions";
const useBreadcrumbStore = breadcrumbStore();
const router = useRouter();
interface IIndicatordata {
  indicator?: string;
  unit?: string;
  value?: number | string;
  color?: string;
  errorInfo?: string;
}
const loading = ref(false);
const tableLoading = ref(false);
const statErrorRateLoading = ref(false);
const statSlowRateLoading = ref(false);
const statAvgDurationLoading = ref(false);
const requestCountData = ref<IIndicatordata>({});
const netAvtDutation = ref<IIndicatordata>({});
const errorRate = ref<IIndicatordata>({});
const netSlowRate = ref<IIndicatordata>({});
const netWorkRequestList = ref<INetItem[]>([]);
const netWorkRequestTotal = ref(0);
const statErrorRateData = ref({});
const statSlowRateData = ref({});
const statAvgDurationData = ref({});
onMounted(() => {
  getNetStartListData();
  getStatErrorRateData();
  getStatAvgDurationData();
  getStatSlowRateData();
  getNetCountData();
});
// 上面的请求数，平均耗时，错误率，慢请求率
async function getNetCountData() {
  try {
    loading.value = true;
    const [netCountdata, netAvtDutationData, errorRateData, netSlowRateData] = await Promise.all([
      getNetCount({}),
      getNetAvgDuration({}),
      getNetErrorRate({}),
      getNetSlowRate({})
    ]);

    requestCountData.value = {
      unit: "请求总数",
      value: netCountdata.value,
      errorInfo: "error"
    };
    netAvtDutation.value = {
      unit: "平均耗时",
      value: netAvtDutationData.value,
      errorInfo: "error"
    };
    errorRate.value = {
      unit: "错误率",
      value: errorRateData.value,
      errorInfo: "error"
    };
    netSlowRate.value = {
      unit: "慢请求率",
      value: netSlowRateData.value,
      errorInfo: "error"
    };
    loading.value = false;
  } catch (error) {
    loading.value = false;
    console.log(error);
  }
}
//列表参数
const pageParams = ref({
  page: 1,
  rows: 10,
  sort: "",
  order: ""
});
async function getNetStartListData() {
  tableLoading.value = true;
  try {
    const res: INetStartlist = await getNetStartList(pageParams.value);
    if (res.code === 0) {
      netWorkRequestList.value = res.records.map(item => {
        return {
          ...item,
          slowRate: item.slowRate + "%",
          errorRate: item.errorRate + "%",
          avgDuration: item.avgDuration + "ms"
        };
      });
      netWorkRequestTotal.value = Number(res.total);
      tableLoading.value = false;
    }
  } catch (error) {
    tableLoading.value = false;
    console.log(error);
  }
}
// 获取错误率统计数据
const getStatErrorRateData = async () => {
  statErrorRateLoading.value = true;
  try {
    const res = await getStatErrorRate({});
    if (res.code === 0 && res.entity) {
      const originalTimes = res.entity.datas.map((data: { time: string }) => data.time);
      const seriesData = [res.entity.datas.map((data: { value: number }) => data.value)];
      const params = {
        typ: res.entity.granularity,
        color: ["#f56c6c"],
        titleType: "错误率统计",
        name: " %",
        originalTimes: originalTimes,
        seriesData: seriesData
      };
      statErrorRateData.value = getChartOptions(params);
    }
    statErrorRateLoading.value = false;
  } catch (error) {
    statErrorRateLoading.value = false;
    console.error("Error logs:", error);
  }
};
// 获取慢请求统计数据的函数
const getStatSlowRateData = async () => {
  try {
    statSlowRateLoading.value = true;
    const res = await getStatSlowRate({});
    if (res.code === 0 && res.entity) {
      const originalTimes = res.entity.datas.map((data: { time: string }) => data.time);
      const seriesData = [res.entity.datas.map((data: { value: number }) => data.value)];
      const color = ["#445fde"];
      const params = {
        typ: res.entity.granularity,
        color: color,
        titleType: "慢请求统计",
        name: " %",
        originalTimes: originalTimes,
        seriesData: seriesData
      };
      statSlowRateData.value = getChartOptions(params);
    }
    statSlowRateLoading.value = false;
  } catch (error) {
    statSlowRateLoading.value = false;
    console.error("Error logs:", error);
  }
};
// 获取统计平均耗时数据
const getStatAvgDurationData = async () => {
  try {
    statAvgDurationLoading.value = true;
    const res = await getStatAvgDurationRate({});
    if (res.code === 0 && res.entity) {
      const originalTimes = res.entity.datas.map((data: { time: string }) => data.time);
      const seriesData = [res.entity.datas.map((data: { value: number }) => data.value)];
      const color = ["#445fde"];
      const params = {
        typ: res.entity.granularity,
        color: color,
        titleType: "平均耗时统计",
        name: " ms",
        originalTimes: originalTimes,
        seriesData: seriesData,
        type: "line"
      };
      statAvgDurationData.value = getChartOptions(params);
    }
    statAvgDurationLoading.value = false;
  } catch (error) {
    statAvgDurationLoading.value = false;
    console.error("Error logs:", error);
  }
};
const handleSortChange = (val: any) => {
  const order = val.order;
  const sort = val.prop;
  if (order === "ascending") {
    pageParams.value.order = "0";
  } else {
    pageParams.value.order = "1";
  }
  pageParams.value.sort = sort;
  getNetStartListData();
  // loadData();
};

const handleSizeChange = (val: number) => {
  pageParams.value.rows = val;
  getNetStartListData();
};
//分页
const handleCurrentChange = (val: number) => {
  pageParams.value.page = val;
  getNetStartListData();
};
// 查看详情
const viewDetail = (scope: any) => {
  router.push("/appmonitor/network-request/detail");
  useBreadcrumbStore.setBreadcrumb(scope.row.url);
  useBreadcrumbStore.setMethod(scope.row.method);
};
</script>

<style lang="scss" scoped>
.indicator-wrapper > *:not(:last-child) {
  margin-right: 8px;
  box-sizing: border-box;
}
.url-name {
  cursor: pointer;
  color: #0064c8;
  vertical-align: middle;
}
</style>
