<template>
  <div class="flex justify-between indicator-container" v-loading="loading">
    <Indicator :value="activeDeviceCount.value" :unit="activeDeviceCount.unit"></Indicator>
    <Indicator
      :value="crashCount.value"
      :unit="crashCount.unit"
      :color="crashCount.color"
    ></Indicator>
    <Indicator
      :value="networkErrorRate.value"
      :unit="networkErrorRate.unit"
      :color="networkErrorRate.color"
    ></Indicator>
    <Indicator
      :value="stutterCount.value"
      :unit="stutterCount.unit"
      :color="stutterCount.color"
    ></Indicator>
    <Indicator
      :value="errorCount.value"
      :unit="errorCount.unit"
      :color="errorCount.color"
    ></Indicator>
    <Indicator :value="averageStartTime.value" :unit="averageStartTime.unit"></Indicator>
  </div>
  <div class="flex justify-between mt-10px indicator-container">
    <BaseEcharts :options="requestCountChartData" height="275px" v-loading="chartLoading" />
    <BaseEcharts :options="networkErrorRateChartData" height="275px" v-loading="chartLoading" />
    <BaseEcharts :options="averageStartTimeChartData" height="275px" v-loading="chartLoading" />
  </div>
  <div class="flex justify-between mt-10px indicator-container">
    <BaseEcharts :options="crashCountChartData" height="275px" v-loading="chartLoading" />
    <BaseEcharts :options="errorCountChartData" height="275px" v-loading="chartLoading" />
    <BaseEcharts :options="stutterCountChartData" height="275px" v-loading="chartLoading" />
  </div>
</template>

<script setup lang="ts">
import BaseEcharts from "@/components/baseEcharts/index.vue";
import { getChartOptions } from "@/components/baseEcharts/chartsOptions";
import { ref, onMounted } from "vue";
import {
  getDeviceCount,
  getAverageDuration,
  getCrashCount,
  getAnrCount,
  getErrorCount,
  getNetworkErrorRate,
  getActiveDeviceCountChart,
  getNetworkErrorStatisticsChart,
  getAverageLaunchDurationChart,
  getCrashCountChart,
  getANRCountChart,
  getErrorCountChart
} from "@/api/application/index";
import { applicationStore } from "@/store/modules/application";
import { breadcrumbStore } from "@/store/modules/breadcurmb";

interface IIndicatorData {
  indicator?: string;
  unit?: string;
  value?: number | string;
  color?: string;
  errorInfo?: string;
}
const useApplicationStore = applicationStore();
const usebreadcrumbStore = breadcrumbStore();
const loading = ref(false);
const chartLoading = ref(false);
const requestCountChartData = ref({});
const errorCountChartData = ref({});
const averageStartTimeChartData = ref({});
const crashCountChartData = ref({});
const networkErrorRateChartData = ref({});
const stutterCountChartData = ref({});

const activeDeviceCount = ref<IIndicatorData>({});

const averageStartTime = ref<IIndicatorData>({});

const crashCount = ref<IIndicatorData>({});

const networkErrorRate = ref<IIndicatorData>({});

const stutterCount = ref<IIndicatorData>({});

const errorCount = ref<IIndicatorData>({});
const handleServiceData = async () => {
  loading.value = true;
  const appId = useApplicationStore.appId;
  const appName = usebreadcrumbStore.appOption;

  type FetchDataFunction = (data: { appid: string }) => Promise<{ value: number }>;
  type FetchDataResult = { unit: string; value: number; errorInfo: string };

  const fetchData = async (
    getDataFunction: FetchDataFunction,
    unit: string
  ): Promise<FetchDataResult> => {
    try {
      const res = await getDataFunction({ appid: appId, appName: appName });
      loading.value = false;
      return { unit, value: res.value, errorInfo: "error" };
    } catch (error) {
      loading.value = false;
      console.error(error);
      return { unit, value: 0, errorInfo: "error" };
    }
  };

  try {
    const [
      activeDeviceCountData,
      averageStartTimeData,
      crashCountData,
      stutterCountData,
      networkErrorRateData,
      errorCountData
    ] = await Promise.all([
      fetchData(getDeviceCount, "活跃设备数"),
      fetchData(getAverageDuration, "启动平均耗时（ms）"),
      fetchData(getCrashCount, "崩溃次数"),
      fetchData(getAnrCount, "卡顿次数"),
      fetchData(getNetworkErrorRate, "网络错误率（%）"),
      fetchData(getErrorCount, "错误次数")
    ]);

    activeDeviceCount.value = activeDeviceCountData;
    averageStartTime.value = averageStartTimeData;

    const setCountValue = (data: any) => ({
      ...data,
      ...(Number(data.value) > 0 ? { color: "#f56c6c" } : {})
    });

    crashCount.value = setCountValue(crashCountData);
    stutterCount.value = setCountValue(stutterCountData);
    networkErrorRate.value = setCountValue(networkErrorRateData);
    errorCount.value = setCountValue(errorCountData);
    loading.value = false;
  } catch (error) {
    loading.value = false;
    console.error(error);
  }
};

const initRequestCountChart = async () => {
  const queue = {
    appName: usebreadcrumbStore.appOption,
    appid: useApplicationStore.appId
  };
  try {
    const res = await getActiveDeviceCountChart(queue);
    if (res.code === 0 && res.entity) {
      const originalTimes = res.entity.datas.map((data: { time: any }) => data.time);
      const seriesData = [res.entity.datas.map((data: { value: any }) => data.value)];
      const color = ["#78bf75"];
      const params = {
        typ: res.entity.granularity,
        color: color,
        name: " ",
        titleType: "请求数",
        originalTimes: originalTimes,
        seriesData: seriesData
      };
      requestCountChartData.value = getChartOptions(params);
    }
  } catch (error) {
    console.error("Error logs:", error);
  }
};
const initErrorCountChart = async () => {
  const queue = {
    appName: usebreadcrumbStore.appOption,
    appid: useApplicationStore.appId
  };
  try {
    const res = await getErrorCountChart(queue);
    if (res.code === 0 && res.entity) {
      const originalTimes = res.entity.datas.map((data: { time: any }) => data.time);
      const seriesData = [res.entity.datas.map((data: { value: any }) => data.value)];
      const color = ["#f56c6c"];
      const params = {
        typ: res.entity.granularity,
        color: color,
        titleType: "错误统计",
        originalTimes: originalTimes,
        seriesData: seriesData
      };
      errorCountChartData.value = getChartOptions(params);
    }
  } catch (error) {
    console.error("Error logs:", error);
  }
};
const initAverageStartTimeChart = async () => {
  const queue = {
    appName: usebreadcrumbStore.appOption,
    appid: useApplicationStore.appId
  };
  try {
    const res = await getAverageLaunchDurationChart(queue);
    if (res.code === 0 && res.entity) {
      const originalTimes = res.entity.datas.map((data: { time: any }) => data.time);
      const seriesData = [res.entity.datas.map((data: { value: any }) => data.value)];
      const color = ["#ffa940"];
      const params = {
        typ: res.entity.granularity,
        color: color,
        name: " ms",
        titleType: "启动平均耗时",
        originalTimes: originalTimes,
        seriesData: seriesData,
        type: "line"
      };
      averageStartTimeChartData.value = getChartOptions(params);
    }
  } catch (error) {
    console.error("Error logs:", error);
  }
};
const initCrashCountChart = async () => {
  const queue = {
    appName: usebreadcrumbStore.appOption,
    appid: useApplicationStore.appId
  };
  try {
    const res = await getCrashCountChart(queue);
    if (res.code === 0 && res.entity) {
      const originalTimes = res.entity.datas.map((data: { time: any }) => data.time);
      const seriesData = [res.entity.datas.map((data: { value: any }) => data.value)];
      const color = ["#f56c6c"];
      const params = {
        typ: res.entity.granularity,
        color: color,
        titleType: "崩溃次数",
        originalTimes: originalTimes,
        seriesData: seriesData
      };
      crashCountChartData.value = getChartOptions(params);
    }
  } catch (error) {
    console.error("Error logs:", error);
  }
};
const initNetworkErrorRateChart = async () => {
  const queue = {
    appName: usebreadcrumbStore.appOption,
    appid: useApplicationStore.appId
  };
  try {
    const res = await getNetworkErrorStatisticsChart(queue);
    if (res.code === 0 && res.entity) {
      const originalTimes = res.entity.datas.map((data: { time: any }) => data.time);
      const seriesData = [res.entity.datas.map((data: { value: any }) => data.value)];
      const color = ["#f56c6c"];
      const params = {
        typ: res.entity.granularity,
        color: color,
        name: " %",
        titleType: "网络错误率",
        originalTimes: originalTimes,
        seriesData: seriesData,
        type: "line"
      };
      networkErrorRateChartData.value = getChartOptions(params);
    }
  } catch (error) {
    console.error("Error logs:", error);
  }
};
const initStutterCountChart = async () => {
  const queue = {
    appName: usebreadcrumbStore.appOption,
    appid: useApplicationStore.appId
  };
  try {
    const res = await getANRCountChart(queue);
    if (res.code === 0 && res.entity) {
      const originalTimes = res.entity.datas.map((data: { time: any }) => data.time);
      const seriesData = [res.entity.datas.map((data: { value: any }) => data.value)];
      const color = ["#f56c6c"];
      const params = {
        typ: res.entity.granularity,
        color: color,
        titleType: "卡顿次数",
        originalTimes: originalTimes,
        seriesData: seriesData
      };
      stutterCountChartData.value = getChartOptions(params);
    }
  } catch (error) {
    console.error("Error logs:", error);
  }
};

onMounted(async () => {
  handleServiceData();
  chartLoading.value = true;
  try {
    await Promise.all([
      initRequestCountChart(),
      initErrorCountChart(),
      initAverageStartTimeChart(),
      initCrashCountChart(),
      initNetworkErrorRateChart(),
      initStutterCountChart()
    ]);
  } finally {
    chartLoading.value = false;
  }
});
</script>

<style lang="scss" scoped>
.indicator-container > *:not(:last-child) {
  margin-right: 8px;
  box-sizing: border-box;
}
</style>
