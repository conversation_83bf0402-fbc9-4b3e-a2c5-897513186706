<template>
  <div v-loading="loading">
    <div class="flex justify-between indicator-container">
      <Indicator
        :value="totalCrashCount.value"
        :unit="totalCrashCount.unit"
        :color="totalCrashCount.color"
      ></Indicator>
      <Indicator
        :value="affectedDeviceCount.value"
        :unit="affectedDeviceCount.unit"
        :color="affectedDeviceCount.color"
      ></Indicator>
      <Indicator
        :value="affectedVersionCount.value"
        :unit="affectedVersionCount.unit"
        :color="affectedVersionCount.color"
      ></Indicator>
    </div>
  </div>
  <div class="flex justify-between mt-10px mb-10px indicator-container">
    <BaseEcharts :options="crashCountChartData" height="250px" v-loading="chartLoading" />
    <BaseEcharts :options="AppVersionChartData" height="250px" v-loading="chartLoading" />
    <BaseEcharts :options="AppDeviceChartData" height="250px" v-loading="chartLoading" />
  </div>
  <MyTable
    :data="list.records"
    :total="list.total"
    v-loading="tableLoading"
    style="width: 100%"
    @sizeChange="handleSizeChange"
    @currentChange="handleCurrentChange"
    :default-sort="{ prop: 'count && deviceCount && versionCount ', order: 'descending' }"
    @sort-change="handleSortChange"
  >
    <MyColumn property="summary" label="崩溃摘要" width="350">
      <template #default="{ row }">
        <span class="service-name" @click="toDetail(row)">
          {{ row.summary }}
        </span>
      </template>
    </MyColumn>
    <MyColumn property="type" label="崩溃类型" />
    <MyColumn property="count" label="崩溃次数" sortable="custom" />
    <MyColumn property="deviceCount" label="影响设备数" sortable="custom" />
    <MyColumn property="versionCount" label="影响版本数" sortable="custom" />
    <MyColumn property="startTime" label="首次发生时间" />
    <MyColumn property="endTime" label="最后发生时间" />
  </MyTable>
</template>

<script setup lang="ts">
import BaseEcharts from "@/components/baseEcharts/index.vue";
import { getChartOptions } from "@/components/baseEcharts/chartsOptions";
import {
  getDeviceCount,
  getCrashCount,
  getVersionsCount,
  getCrashCountChart,
  getAppVersionTop5,
  getAppDeviceTop5,
  getCollapseStatList
} from "@/api/application/collapse";
import { breadcrumbStore } from "@/store/modules/breadcurmb";
import { ref, onMounted } from "vue";
import { applicationStore } from "@/store/modules/application";
import { useRouter } from "vue-router";
const useBreadcrumbStore = breadcrumbStore();
const router = useRouter();

const list = reactive({
  records: [],
  total: 0
});
//列表参数
const pageParams = reactive({
  appid: "",
  appName: useBreadcrumbStore.appOption,
  page: 1,
  rows: 10,
  sort: "",
  order: ""
});
const useApplicationStore = applicationStore();
interface IIndicatorData {
  indicator?: string;
  unit?: string;
  value?: number | string;
  color?: string;
  errorInfo?: string;
}

const loading = ref(false); //顶部卡片loading
const chartLoading = ref(false); //图表loading
const tableLoading = ref(false); //表格loading
// 图表数据
const crashCountChartData = ref({});
const AppVersionChartData = ref({});
const AppDeviceChartData = ref({});

// 总崩溃次数
const totalCrashCount = ref<IIndicatorData>({});

// 影响设备数
const affectedDeviceCount = ref<IIndicatorData>({});

// 影响版本数
const affectedVersionCount = ref<IIndicatorData>({});
const handleServiceData = async () => {
  loading.value = true;
  const appId = useApplicationStore.appId;
  // const appName = useApplicationStore.appName;
  type FetchDataFunction = (data: { appid: string }) => Promise<{ value: number }>;
  type FetchDataResult = { unit: string; value: number; errorInfo: string };
  const fetchData = async (
    getDataFunction: FetchDataFunction,
    unit: string
  ): Promise<FetchDataResult> => {
    try {
      const res = await getDataFunction({ appid: appId, appName: useBreadcrumbStore.appOption });
      return { unit, value: res.value, errorInfo: "error" };
    } catch (error) {
      console.error(error);
      return { unit, value: 0, errorInfo: "error" };
    }
  };

  try {
    const [totalCrashCountData, affectedDeviceCountData, affectedVersionCountData] =
      await Promise.all([
        fetchData(getCrashCount, "总崩溃次数"),
        fetchData(getDeviceCount, "影响设备数"),
        fetchData(getVersionsCount, "影响版本数")
      ]);

    const setCountValue = (data: any) => ({
      ...data,
      ...(Number(data.value) > 0 ? { color: "#f56c6c" } : {})
    });
    totalCrashCount.value = setCountValue(totalCrashCountData);
    affectedDeviceCount.value = setCountValue(affectedDeviceCountData);
    affectedVersionCount.value = setCountValue(affectedVersionCountData);
    loading.value = false;
  } catch (error) {
    loading.value = false;
    console.error("Error fetching service data:", error);
  }
};

// 总崩溃次数图表
const initCrashCountChart = async () => {
  const queue = {
    appName: useBreadcrumbStore.appOption,
    appid: useApplicationStore.appId
  };
  try {
    const res = await getCrashCountChart(queue);
    if (res.code === 0 && res.entity) {
      const originalTimes = res.entity.datas.map((data: { time: any }) => data.time);
      const seriesData = [res.entity.datas.map((data: { value: any }) => data.value)];
      const color = ["#445fde"];
      const params = {
        typ: res.entity.granularity,
        color: color,
        titleType: "崩溃次数",
        originalTimes: originalTimes,
        seriesData: seriesData,
        type: "line"
      };
      crashCountChartData.value = getChartOptions(params);
    }
  } catch (error) {
    console.error("Error logs:", error);
  }
};
// TOP5版本崩溃
const handleAppVersionTop5 = () => {
  getAppVersionTop5({ appid: useApplicationStore.appId, appName: useBreadcrumbStore.appOption })
    .then(res => {
      if (res.code === 0) {
        const originalTimes = res.records.map((data: { appVersion: any }) => data.appVersion);
        const seriesData = [res.records.map((data: { count: any }) => data.count)];
        const color = ["#445fde"];
        const params = {
          color: color,
          titleType: "TOP5版本崩溃",
          originalTimes: originalTimes,
          seriesData: seriesData
        };
        AppVersionChartData.value = getChartOptions(params);
      }
    })
    .catch(err => {
      console.error(err);
    });
};

//TOP5设备崩溃
async function handleAppDeviceTop5() {
  const queue = {
    appName: useBreadcrumbStore.appOption,
    appid: useApplicationStore.appId
  };
  try {
    const res = await getAppDeviceTop5(queue);
    if (res.code === 0) {
      const originalTimes = res.records.map((data: { device: any }) => data.device);
      const seriesData = [res.records.map((data: { count: any }) => data.count)];
      const color = ["#445fde"];

      const params = {
        color: color,
        titleType: "TOP5设备崩溃",
        originalTimes: originalTimes,
        seriesData: seriesData
      };
      AppDeviceChartData.value = getChartOptions(params);
    }
  } catch (error) {
    console.error(error);
  }
}
//排序
const handleSortChange = (val: any) => {
  const order = val.order;
  const sort = val.prop;
  if (order === "ascending") {
    pageParams.order = "0";
  } else {
    pageParams.order = "1";
  }
  pageParams.sort = sort;
  loadData();
};
//修改每页条数
const handleSizeChange = (val: number) => {
  pageParams.rows = val;
  loadData();
};
//分页
const handleCurrentChange = (val: number) => {
  pageParams.page = val;
  loadData();
};

//列表数据
function loadData() {
  tableLoading.value = true;
  pageParams.appid = useApplicationStore.appId;
  getCollapseStatList(pageParams)
    .then(response => {
      if (response.code === 0) {
        list.records = response.records;
        list.total = Number(response.total);
        tableLoading.value = false;
      }
    })
    .catch(error => {
      tableLoading.value = false;
      console.log(error);
    });
}
//跳转详情
const toDetail = (row: any) => {
  router.push("/appmonitor/collapse/detail");
  useBreadcrumbStore.setBreadcrumb(row.summary);
  useBreadcrumbStore.setMethod(row.type);
};
// 组件加载时初始化图表
onMounted(async () => {
  handleServiceData();
  chartLoading.value = true;
  try {
    await Promise.all([
      initCrashCountChart(),
      handleAppVersionTop5(),
      handleAppDeviceTop5(),
      loadData()
    ]);
  } finally {
    chartLoading.value = false;
  }
});
</script>

<style lang="scss" scoped>
.indicator-container > *:not(:last-child) {
  margin-right: 8px;
  box-sizing: border-box;
}
.service-name {
  cursor: pointer;
  color: #0064c8;
  vertical-align: middle;
}
</style>
