<template>
  <div v-loading="loading">
    <div class="flex justify-between indicator-wrapper">
      <Indicator
        :value="stutterCount.value"
        :unit="'卡顿次数'"
        :color="stutterCount.color"
      ></Indicator>
      <Indicator
        :value="DeviceCount.value"
        :unit="'影响设备数'"
        :color="DeviceCount.color"
      ></Indicator>
      <Indicator
        :value="VersionCount.value"
        :unit="'影响版本数'"
        :color="VersionCount.color"
      ></Indicator>
    </div>
  </div>
  <div class="flex justify-between indicator-wrapper mt-10px mb-10px">
    <BaseEcharts :options="stutterCountChartData" height="250px" v-loading="stutterCountLoading" />
    <BaseEcharts :options="appVersionTop5Data" height="250px" v-loading="appVersionTop5Loading" />
    <BaseEcharts :options="appDeviceTop5Data" height="250px" v-loading="appDeviceTop5Loading" />
  </div>
  <MyTable
    :data="list.records"
    :total="list.total"
    v-loading="tableLoading"
    style="width: 100%"
    @sizeChange="handleSizeChange"
    @currentChange="handleCurrentChange"
    :default-sort="{
      prop: 'count && deviceCount && versionCount',
      order: 'descending'
    }"
    @sort-change="handleSortChange"
  >
    <my-column property="summary" label="卡顿摘要" width="350">
      <template #default="scope">
        <span class="service-name" @click="toDetail(scope)">
          {{ scope.row.summary }}
        </span>
      </template>
    </my-column>
    <my-column property="type" label="卡顿类型"> </my-column>
    <my-column property="count" label="卡顿次数" sortable="custom" />
    <my-column property="deviceCount" label="影响设备数" sortable="custom"> </my-column>
    <my-column property="versionCount" label="影响版本数" sortable="custom"> </my-column>
    <my-column property="startTime" label="首次发生时间"> </my-column>
    <my-column property="endTime" label="最后发生时间"> </my-column>
  </MyTable>
</template>

<script setup lang="ts">
import { getAnrCount, getANRCountChart } from "@/api/application/index";
import {
  getDeviceCount,
  getVersionCount,
  getAnrStatList,
  getAnrAppVersionTop5,
  getAnrAppDeviceTop5
} from "@/api/application/anr";
import MyTable from "@/components/table/my-table.vue";
import MyColumn from "@/components/table/my-column.vue";
import { useRouter } from "vue-router";
import { breadcrumbStore } from "@/store/modules/breadcurmb";
import { applicationStore } from "@/store/modules/application";
import { getChartOptions } from "@/components/baseEcharts/chartsOptions";
const useBreadcrumbStore = breadcrumbStore();
const router = useRouter();
const useApplicationStore = applicationStore();
interface IIndicatorData {
  indicator?: string;
  unit?: string;
  value?: number | string;
  color?: string;
  errorInfo?: string;
}
const loading = ref(false); //顶部卡片loading
const stutterCountLoading = ref(false); //卡顿次数loading
const appVersionTop5Loading = ref(false); //TOP5版本卡顿loading
const appDeviceTop5Loading = ref(false); //TOP5设备卡顿load
const tableLoading = ref(false); //表格loading
const stutterCount = ref<IIndicatorData>({}); //卡顿次数
const DeviceCount = ref<IIndicatorData>({}); //影响设备数
const VersionCount = ref<IIndicatorData>({}); //影响版本数
const stutterCountChartData = ref({}); //卡顿图表
//卡片数据
async function handleServiceData() {
  loading.value = true;
  const appId = useApplicationStore.appId;
  type FetchDataFunction = (data: { appid: string }) => Promise<{ value: number }>;
  type FetchDataResult = { unit: string; value: number; errorInfo: string };
  const fetchData = async (
    getDataFunction: FetchDataFunction,
    unit: string
  ): Promise<FetchDataResult> => {
    try {
      const res = await getDataFunction({ appid: appId, appName: useBreadcrumbStore.appOption });
      return { unit, value: res.value, errorInfo: "error" };
    } catch (error) {
      console.error(error);
      return { unit, value: 0, errorInfo: "error" };
    }
  };

  try {
    const [stutterCountData, DeviceCountData, VersionCountData] = await Promise.all([
      fetchData(getAnrCount, ""),
      fetchData(getDeviceCount, ""),
      fetchData(getVersionCount, "")
    ]);
    const setCountValue = (data: any) => ({
      ...data,
      ...(Number(data.value) >= 1 ? { color: "#f56c6c" } : {})
    });
    stutterCount.value = setCountValue(stutterCountData);
    DeviceCount.value = setCountValue(DeviceCountData);
    VersionCount.value = setCountValue(VersionCountData);
    loading.value = false;
  } catch (error) {
    loading.value = false;
    console.error("Error fetching service data:", error);
  }
}
//列表参数
const pageParams = reactive({
  appid: "",
  appName: "",
  page: 1,
  rows: 10,
  sort: "",
  order: ""
});
//排序
const handleSortChange = (val: any) => {
  const order = val.order;
  const sort = val.prop;
  if (order === "ascending") {
    pageParams.order = "0";
  } else {
    pageParams.order = "1";
  }
  pageParams.sort = sort;
  loadData();
};
//修改每页条数
const handleSizeChange = (val: number) => {
  pageParams.rows = val;
  loadData();
};
//分页
const handleCurrentChange = (val: number) => {
  pageParams.page = val;
  loadData();
};
const list = reactive({
  records: [],
  total: 0
});
//列表数据
function loadData() {
  tableLoading.value = true;
  pageParams.appid = useApplicationStore.appId;
  pageParams.appName = useBreadcrumbStore.appOption;
  getAnrStatList(pageParams)
    .then(response => {
      if (response.code === 0) {
        list.records = response.records;
        list.total = Number(response.total);
        tableLoading.value = false;
      }
    })
    .catch(error => {
      tableLoading.value = false;
      console.log(error);
    });
}
//卡顿图表
const initStutterCountChart = async () => {
  stutterCountLoading.value = true;
  const queue = {
    appName: useBreadcrumbStore.appOption,
    appid: useApplicationStore.appId
  };
  try {
    const res = await getANRCountChart(queue);
    if (res.code === 0 && res.entity) {
      const originalTimes = res.entity.datas.map((data: { time: any }) => data.time);
      const seriesData = [res.entity.datas.map((data: { value: any }) => data.value)];
      const color = ["#445fde"];
      const params = {
        typ: res.entity.granularity,
        color: color,
        titleType: "卡顿次数",
        originalTimes: originalTimes,
        seriesData: seriesData,
        type: "line"
      };
      stutterCountChartData.value = getChartOptions(params);
      stutterCountLoading.value = false;
    }
  } catch (error) {
    stutterCountLoading.value = false;
    console.error("Error logs:", error);
  }
};
//TOP5版本卡顿
const appVersionTop5Data = ref({});
const handleAppVersionTop5 = () => {
  appVersionTop5Loading.value = true;
  getAnrAppVersionTop5({ appid: useApplicationStore.appId, appName: useBreadcrumbStore.appOption })
    .then(res => {
      if (res.code === 0) {
        const originalTimes = res.records.map((data: { appVersion: any }) => data.appVersion);
        const seriesData = [res.records.map((data: { count: any }) => data.count)];
        const color = ["#445fde"];
        const params = {
          color: color,
          titleType: "TOP5版本卡顿",
          originalTimes: originalTimes,
          seriesData: seriesData
        };
        appVersionTop5Data.value = getChartOptions(params);
        appVersionTop5Loading.value = false;
      }
    })
    .catch(error => {
      appVersionTop5Loading.value = false;
      console.log(error);
    });
};

//TOP5设备卡顿
const appDeviceTop5Data = ref({});
const handleAppDeviceTop5 = () => {
  appDeviceTop5Loading.value = true;
  getAnrAppDeviceTop5({ appid: useApplicationStore.appId, appName: useBreadcrumbStore.appOption })
    .then(res => {
      if (res.code === 0) {
        const originalTimes = res.records.map((data: { device: any }) => data.device);
        const seriesData = [res.records.map((data: { count: any }) => data.count)];
        const color = ["#445fde"];
        const params = {
          color: color,
          titleType: "TOP5设备卡顿",
          originalTimes: originalTimes,
          seriesData: seriesData
        };
        appDeviceTop5Data.value = getChartOptions(params);
        appDeviceTop5Loading.value = false;
      }
    })
    .catch(error => {
      appDeviceTop5Loading.value = false;
      console.log(error);
    });
};
//跳转详情
const toDetail = (scope: any) => {
  router.push("/appmonitor/anr/detail");
  useBreadcrumbStore.setBreadcrumb(scope.row.summary);
  useBreadcrumbStore.setMethod(scope.row.type);
};
onMounted(() => {
  handleServiceData();
  loadData();
  initStutterCountChart();
  handleAppVersionTop5();
  handleAppDeviceTop5();
});
</script>

<style lang="scss" scoped>
.indicator-wrapper > *:not(:last-child) {
  margin-right: 8px;
  box-sizing: border-box;
}
.service-name {
  cursor: pointer;
  color: #0064c8;
  vertical-align: middle;
}
</style>
