<template>
  <div class="flex justify-between indicator-wrapper mt-10px mb-10px">
    <BaseEcharts :options="stutterCountChartData" height="250px" v-loading="stutterCountLoading" />
    <BaseEcharts :options="appVersionTop5Data" height="250px" v-loading="appVersionTop5Loading" />
    <BaseEcharts :options="appDeviceTop5Data" height="250px" v-loading="appDeviceTop5Loading" />
  </div>
  <div class="log-container">
    <div class="search-container">
      <el-form>
        <el-form-item class="input-group">
          <label for="net">网络：</label>
          <el-select placeholder="请选择网络" v-model="pageParams.net" clearable filterable>
            <el-option v-for="net in nets" :key="net" :label="net" :value="net" />
          </el-select>
        </el-form-item>
        <el-form-item class="input-group">
          <label for="appVersion">app版本：</label>
          <el-select
            placeholder="请选择app版本"
            v-model="pageParams.appVersion"
            clearable
            filterable
          >
            <el-option
              v-for="appVersion in appVersions"
              :key="appVersion"
              :label="appVersion"
              :value="appVersion"
            />
          </el-select>
        </el-form-item>
        <el-form-item style="display: flex; margin-top: 20px">
          <el-popconfirm
            @confirm="resetSearch"
            title="确定清空吗？"
            confirm-button-text="确定"
            cancel-button-text="取消"
            icon="el-icon-warning"
            :hide-after="0"
          >
            <template #reference>
              <el-button type="danger" plain style="flex: 1; margin-right: 10px"> 清空 </el-button>
            </template>
          </el-popconfirm>
          <el-button type="primary" :icon="Search" style="flex: 1" @click="search">
            查询
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="tabel-container">
      <MyTable
        :data="list.records"
        :total="list.total"
        style="width: 100%"
        v-loading="tableLoading"
        @sizeChange="handleSizeChange"
        @currentChange="handleCurrentChange"
      >
        <my-column property="deviceId" label="设备ID" width="350">
          <template #default="scope">
            <span class="service-name" @click="shouDetail(scope.row)">
              {{ scope.row.deviceId }}
            </span>
          </template>
        </my-column>
        <my-column property="deviceName" label="设备名称" />
        <my-column property="type" label="卡顿类型" />
        <my-column property="net" label="设备网络" />
        <my-column property="cpu" label="CPU架构"> </my-column>
        <my-column property="appVersion" label="APP版本"> </my-column>
        <my-column property="os" label="操作系统">
          <template #default="scope">
            <span> {{ scope.row.osName }} {{ scope.row.osVersion }} </span>
          </template>
        </my-column>
        <my-column property="time" label="发生时间"> </my-column>
      </MyTable>
    </div>
  </div>
  <div>
    <el-drawer v-model="logVisible" :title="title" size="50%">
      <TitlecCom :title="collapseDetailtitle"></TitlecCom>
      <div class="attr-panel">
        <div class="tab-content">
          <table class="detail-table">
            <tbody>
              <tr>
                <td class="label">发生时间</td>
                <td width="35%">{{ detailList.timestamp }}</td>
                <td class="label">设备型号</td>
                <td width="35%">{{ detailList.device }}</td>
              </tr>
              <tr>
                <td class="label">内存占用</td>
                <td width="35%">{{ detailList.memoryUse }}MB</td>
                <td class="label">内存空闲</td>
                <td width="35%">{{ detailList.memoryFree }}MB</td>
              </tr>
              <tr>
                <td class="label">可用磁盘</td>
                <td width="35%">{{ detailList.diskFree }}MB</td>
                <td class="label">已用磁盘</td>
                <td width="35%">{{ detailList.diskUse }}MB</td>
              </tr>
              <tr>
                <td class="label">CPU使用</td>
                <td width="35%">{{ detailList.cpuUse }}%</td>
                <td class="label">操作系统</td>
                <td width="35%">{{ detailList.osName + " " + detailList.osVersion }}</td>
              </tr>
              <tr>
                <td class="label">设备网络</td>
                <td width="35%">{{ detailList.net }}</td>
                <td class="label">应用包名</td>
                <td width="35%">{{ detailList.appPackage }}</td>
              </tr>
              <tr>
                <td class="label">APP版本</td>
                <td width="35%">{{ detailList.appVersion }}</td>
                <td class="label">CPU架构</td>
                <td width="35%">{{ detailList.cpu }}</td>
              </tr>
              <tr>
                <td class="label">摘要</td>
                <td colspan="3">{{ detailList.summary }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <div class="event-detail">
        <TitlecCom :title="collapsetitle"></TitlecCom>
        <div style="margin-top: 10px">
          <div style="white-space: pre-wrap; font-size: 14px">
            {{ detailList.stack ? detailList.stack : "暂无数据" }}
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import {
  getAnrList,
  getAnrDetail,
  getAnrNets,
  getAnrAppVersions,
  getAnrAppVersionTop5,
  getAnrAppDeviceTop5
} from "@/api/application/anr";
import { getANRCountChart } from "@/api/application/index";
import { Search } from "@element-plus/icons-vue";
import MyTable from "@/components/table/my-table.vue";
import MyColumn from "@/components/table/my-column.vue";
import { applicationStore } from "@/store/modules/application";
import { breadcrumbStore } from "@/store/modules/breadcurmb";
import { getChartOptions } from "@/components/baseEcharts/chartsOptions";
import TitlecCom from "@/components/TitleCom/index.vue";
const useApplicationStore = applicationStore();
const usebreadcrumbStore = breadcrumbStore();
const stutterCountLoading = ref(false); //卡顿次数loading
const appVersionTop5Loading = ref(false); //TOP5版本卡顿loading
const appDeviceTop5Loading = ref(false); //TOP5设备卡顿load
const tableLoading = ref(false); //表格loading
const logVisible = ref(false); //抽屉是否显示
const stutterCountChartData = ref({}); //卡顿图表
const title = ref(""); //抽屉标题
const collapseDetailtitle = ref("基础信息"); //抽屉标题
const collapsetitle = ref("异常堆栈"); //抽屉标题
//列表参数
const pageParams = reactive({
  appid: "",
  summary: "",
  appName: "",
  page: 1,
  rows: 10,
  net: "",
  appVersion: "",
  type: ""
});
//修改每页条数
const handleSizeChange = (val: number) => {
  pageParams.rows = val;
  loadData();
};
//分页
const handleCurrentChange = (val: number) => {
  pageParams.page = val;
  loadData();
};
function search() {
  pageParams.page = 1;
  loadData();
}
//清空
function resetSearch() {
  pageParams.net = "";
  pageParams.appVersion = "";
}
const list = reactive({
  records: [],
  total: 0
});
//列表数据
function loadData() {
  tableLoading.value = true;
  pageParams.appid = useApplicationStore.appId;
  pageParams.summary = usebreadcrumbStore.breadcrumbTitle;
  pageParams.type = usebreadcrumbStore.method;
  pageParams.appName = usebreadcrumbStore.appOption;
  getAnrList(pageParams)
    .then(response => {
      if (response.code === 0) {
        list.records = response.records;
        list.total = Number(response.total);
        tableLoading.value = false;
      }
    })
    .catch(error => {
      tableLoading.value = false;
      console.log(error);
    });
}
//网络下拉框
const nets = ref([]);
const dataToSend = reactive({
  appid: "",
  appName: "",
  summary: "",
  type: ""
});
function getAnrNet() {
  dataToSend.appid = useApplicationStore.appId;
  dataToSend.summary = usebreadcrumbStore.breadcrumbTitle;
  dataToSend.type = usebreadcrumbStore.method;
  dataToSend.appName = usebreadcrumbStore.appOption;
  getAnrNets(dataToSend).then(response => {
    nets.value = response.records;
  });
}
//App版本
const appVersions = ref([]);
const params = reactive({
  appid: "",
  appName: "",
  summary: "",
  type: ""
});
function getAnrAppVersion() {
  params.appid = useApplicationStore.appId;
  params.summary = usebreadcrumbStore.breadcrumbTitle;
  params.type = usebreadcrumbStore.method;
  params.appName = usebreadcrumbStore.appOption;
  getAnrAppVersions(params).then(respone => {
    appVersions.value = respone.records;
  });
}
//详情字段
const detailList = ref({
  timestamp: "",
  summary: "",
  device: "",
  osVersion: "",
  osName: "",
  appVersion: "",
  net: "",
  cpuUse: "",
  memoryFree: "",
  diskFree: "",
  cpu: "",
  appPackage: "",
  memoryUse: "",
  stack: "",
  diskUse: ""
});
//详情
const shouDetail = (row: any) => {
  logVisible.value = true;
  title.value = "卡顿详情 【" + row.deviceId + "】";
  getAnrDetail(row.id).then(response => {
    if (response.code === 0) {
      detailList.value = response.entity;
    }
  });
};
//卡顿图表
const initStutterCountChart = async () => {
  stutterCountLoading.value = true;
  const queue = {
    appName: usebreadcrumbStore.appOption,
    appid: useApplicationStore.appId,
    summary: usebreadcrumbStore.breadcrumbTitle,
    type: usebreadcrumbStore.method
  };
  try {
    const res = await getANRCountChart(queue);
    if (res.code === 0 && res.entity) {
      const originalTimes = res.entity.datas.map((data: { time: any }) => data.time);
      const seriesData = [res.entity.datas.map((data: { value: any }) => data.value)];
      const color = ["#445fde"];
      const params = {
        typ: res.entity.granularity,
        color: color,
        titleType: "卡顿次数",
        originalTimes: originalTimes,
        seriesData: seriesData,
        type: "line"
      };
      stutterCountChartData.value = getChartOptions(params);
      stutterCountLoading.value = false;
    }
  } catch (error) {
    stutterCountLoading.value = false;
    console.error("Error logs:", error);
  }
};
//TOP5版本卡顿
const appVersionTop5Data = ref({});
const handleAppVersionTop5 = () => {
  appVersionTop5Loading.value = true;
  getAnrAppVersionTop5({
    appid: useApplicationStore.appId,
    appName: usebreadcrumbStore.appOption,
    summary: usebreadcrumbStore.breadcrumbTitle,
    type: usebreadcrumbStore.method
  })
    .then(res => {
      if (res.code === 0) {
        const originalTimes = res.records.map((data: { appVersion: any }) => data.appVersion);
        const seriesData = [res.records.map((data: { count: any }) => data.count)];
        const color = ["#445fde"];
        const params = {
          color: color,
          titleType: "TOP5版本卡顿",
          originalTimes: originalTimes,
          seriesData: seriesData
        };
        appVersionTop5Data.value = getChartOptions(params);
        appVersionTop5Loading.value = false;
      }
    })
    .catch(error => {
      appVersionTop5Loading.value = false;
      console.log(error);
    });
};

//TOP5设备卡顿
const appDeviceTop5Data = ref({});
const handleAppDeviceTop5 = () => {
  appDeviceTop5Loading.value = true;
  getAnrAppDeviceTop5({
    appid: useApplicationStore.appId,
    appName: usebreadcrumbStore.appOption,
    summary: usebreadcrumbStore.breadcrumbTitle,
    type: usebreadcrumbStore.method
  })
    .then(res => {
      if (res.code === 0) {
        const originalTimes = res.records.map((data: { device: any }) => data.device);
        const seriesData = [res.records.map((data: { count: any }) => data.count)];
        const color = ["#445fde"];
        const params = {
          color: color,
          titleType: "TOP5设备卡顿",
          originalTimes: originalTimes,
          seriesData: seriesData
        };
        appDeviceTop5Data.value = getChartOptions(params);
        appDeviceTop5Loading.value = false;
      }
    })
    .catch(error => {
      appDeviceTop5Loading.value = false;
      console.log(error);
    });
};
onMounted(() => {
  loadData();
  getAnrNet();
  getAnrAppVersion();
  initStutterCountChart();
  handleAppVersionTop5();
  handleAppDeviceTop5();
});
</script>

<style lang="scss" scoped>
.indicator-wrapper > *:not(:last-child) {
  margin-right: 8px;
  box-sizing: border-box;
}
.log-container {
  display: flex;
  overflow-y: auto;
  overflow-x: hidden;

  .search-container {
    width: 260px;
    height: 250px;
    border: 1px solid #eee;
    background: #ffffff;
    padding: 15px 15px 25px 15px;
    margin-right: 10px;
    z-index: 99;
    top: 186px;
    left: 20px;
  }

  .tabel-container {
    flex: 1;
    width: calc(100% - 260px);
  }
}
.input-group {
  margin-bottom: 10px;
}
.service-name {
  cursor: pointer;
  color: #0064c8;
  vertical-align: middle;
}
.event-detail {
  padding: 16px 8px 20px 8px;
  // border-bottom: 1px solid #dedede;
  // border-top: 1px solid #dedede;
  margin-top: 10px;
}
.attr-panel {
  flex: 1;
  margin-left: 10px;

  .span-panel {
    width: 750px;
    height: 875px;
    overflow-x: hidden;
    overflow-y: auto;
    table {
      border: 1px solid #ebeef5;
      border-collapse: collapse;

      tbody {
        tr {
          transition: background 0.5s;

          &:hover {
            background: #f5f7fa;
          }
        }
      }

      td {
        padding: 12px;
        position: relative;

        .duration {
          position: absolute;
          align-items: center;
          padding-left: 15px;
          display: flex;
          left: 5px;
          right: 5px;
          top: 5px;
          bottom: 5px;

          &.success {
            background: #c1e488;
          }

          &.error {
            background: #ff9393;
          }
        }

        .bar {
          width: 3px;
          height: 18px;
          margin-right: 10px;
          display: inline-block;
          vertical-align: middle;

          &.success {
            background: #009431;
          }

          &.error {
            background: #e00000;
          }
        }

        .name {
          vertical-align: middle;
          display: inline-block;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          max-width: 280px;
        }
      }

      thead {
        tr {
          td {
            background: #f9f9f9;
            border-bottom: 1px solid #ebeef5;
          }
        }
      }
    }
  }
}

.detail-table {
  width: 100%;
  color: #606266;
  font-size: 14px;
  border: 1px solid #ebeef5;
  border-collapse: collapse;

  tr {
    td {
      border: 1px solid #ebeef5;
      word-break: break-all;
      padding: 15px;
    }

    td.label {
      background: #f5f7fa;
      width: 120px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}
:deep(.el-drawer__header) {
  margin: 5px !important;
}
:deep(.attr-panel) {
  margin: 0 !important;
}
</style>
