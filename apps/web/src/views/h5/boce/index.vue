<template>
  <div>
    <div class="tb-header">
      {{ "当前拨测任务：" }}
      <el-select
        v-model="taskId"
        @change="handleTaskChange"
        placeholder="请选择应用"
        style="width: 200px"
      >
        <el-option
          v-for="item in boceList"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </div>
    <div class="flex justify-between">
      <div class="w-50%">
        <!-- <TitlecCom :title="`详情`"></TitlecCom> -->
        <BaseEcharts
          :options="chartOptions"
          height="410px"
          v-loading="chartOptionsLoading"
          v-if="!chartOptionsLoading"
        />
      </div>
      <div class="w-48%">
        <!-- <TitlecCom :title="`概览`"></TitlecCom> -->
        <MyTable
          :data="list.overview"
          pagination-layout=""
          height="450px"
          style="width: 100%"
          v-loading="overViewLoading"
        >
          <my-column property="type" align="center" header-align="center" label="区域/运营商" />

          <my-column property="fastValue" align="center" header-align="center" label="响应最快">
            <template #default="scope">
              <span> {{ scope.row.fastValue + " ms" }} </span>
            </template>
          </my-column>
          <my-column property="slowValue" align="center" header-align="center" label="响应最慢">
            <template #default="scope">
              <span> {{ scope.row.slowValue + " ms" }} </span>
            </template>
          </my-column>
          <my-column property="avgValue" align="center" header-align="center" label="平均响应">
            <template #default="scope">
              <span> {{ scope.row.avgValue + " ms" }} </span>
            </template>
          </my-column>
        </MyTable>
      </div>
    </div>
    <MyTable
      v-loading="tableLoading"
      :data="list.records"
      :total="list.total"
      style="width: 100%"
      height="30vh"
      @sizeChange="handleSizeChange"
      @currentChange="handleCurrentChange"
    >
      <!-- <my-column property="ProbeTime" label="拨测时间" /> -->
      <my-column property="task_id" label="任务id" />
      <my-column property="task_url" label="拨测url" />
      <my-column property="client_id" label="客户端id" />
      <my-column property="client_operator" label="拨测主机运营商" />
      <my-column property="client_area" label="拨测主机省份" />
      <my-column property="client_city" label="拨测主机城市" />
      <my-column property="is_success" label="是否拨测成功">
        <template #default="scope">
          <el-tag :type="scope.row.is_success ? 'success' : 'danger'" effect="light">{{
            scope.row.is_success ? "成功" : "失败"
          }}</el-tag>
        </template>
      </my-column>
      <my-column property="dial_time" label="拨测延时">
        <template #default="scope">
          <span>{{ scope.row.dial_time + " ms" }}</span>
        </template>
      </my-column>
      <my-column property="error_message" label="报错信息">
        <template #default="scope">
          <span>{{ scope.row.error_message.length > 0 ? scope.row.error_message : "-" }}</span>
        </template>
      </my-column>
      <my-column property="start_time" label="拨测开始时间">
        <template #default="scope">
          <span>{{ formatTime(scope.row.start_time) }}</span>
        </template>
      </my-column>
      <my-column property="reported_at" label="客户端应答时间">
        <template #default="scope">
          <span>{{ formatTime(scope.row.reported_at) }}</span>
        </template>
      </my-column>
      <!-- <my-column label="操作" align="center" header-align="center" fixed="right" width="100">
        <template #default="scope">
          <span class="operate" @click="fetchDetail(scope.row)">查看详情</span>
        </template>
      </my-column> -->
    </MyTable>

    <el-drawer
      v-model="hostVisible1"
      :title="hostTitle"
      size="60%"
      @close="clearDrawerData"
      v-loading="drawerLoading"
    >
      <TitlecCom :title="`基本信息`"></TitlecCom>
      <el-descriptions>
        <el-descriptions-item label="任务ID：">{{
          currentRecord.baseInfo.Task || ""
        }}</el-descriptions-item>
        <el-descriptions-item label="任务域名：">{{
          currentRecord.baseInfo.taskIP || ""
        }}</el-descriptions-item>
        <el-descriptions-item label="任务类型：">{{
          currentRecord.baseInfo.taskType
            ? TaskTypeMap[Number(currentRecord.baseInfo.taskType) - 1].label
            : ""
        }}</el-descriptions-item>
        <el-descriptions-item label="执行时间：">{{
          currentRecord.baseInfo.ProbeTime || ""
        }}</el-descriptions-item>
        <el-descriptions-item label="拨测点IP：">{{
          currentRecord.baseInfo.ProbeIP || ""
        }}</el-descriptions-item>
        <el-descriptions-item label="运营商：">{{
          currentRecord.baseInfo.Operator || ""
        }}</el-descriptions-item>
        <el-descriptions-item label="地理位置：">{{
          currentRecord.baseInfo.City || ""
        }}</el-descriptions-item>
        <el-descriptions-item label="状态：">{{
          currentRecord.baseInfo.StatusType || ""
        }}</el-descriptions-item>
        <el-descriptions-item label="错误代码：" v-if="currentRecord.baseInfo.ErrorCode">{{
          currentRecord.baseInfo.ErrorCode
        }}</el-descriptions-item>
      </el-descriptions>
      <hr />
      <TitlecCom :title="`DNS请求分析`" style="margin-top: 15px"></TitlecCom>
      <el-descriptions>
        <el-descriptions-item label="A地址：">{{
          currentRecord.detailInfo.AAddress || ""
        }}</el-descriptions-item>
        <el-descriptions-item label="CNAME地址：">{{
          currentRecord.detailInfo.CnameAddress || ""
        }}</el-descriptions-item>
        <el-descriptions-item label="DNS用时：">{{
          currentRecord.detailInfo.DNSQueryTime || ""
        }}</el-descriptions-item>
        <el-descriptions-item label="DNS跟踪：">
          <el-input
            type="textarea"
            :rows="10"
            v-model="currentRecord.detailInfo.DNSDetail"
            disabled
          />
        </el-descriptions-item>
      </el-descriptions>
      <hr />
      <TitlecCom style="margin-top: 15px" :title="`PING监测分析`"></TitlecCom>
      <el-input
        type="textarea"
        :rows="10"
        v-model="currentRecord.detailInfo.PingServerIp"
        style="margin-bottom: 10px"
        disabled
      ></el-input>
      <hr />
      <TitlecCom style="margin-top: 15px" :title="`TRACERT监测分析`"></TitlecCom>
      <el-descriptions
        v-for="(item, index) in currentRecord.detailInfo.TracertAnalysis"
        :key="index"
        style="margin-bottom: 5px"
        border
      >
        <el-descriptions-item label="TracertIP">
          <div style="min-width: 300px">{{ item.TracertIP || "" }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="Tracert时间">
          <div style="min-width: 150px">{{ item.TracertTime || "" }}</div>
        </el-descriptions-item>
      </el-descriptions>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import chaina from "@/components/china/china.json";
import TitlecCom from "@/components/TitleCom/index.vue";
import MyTable from "@/components/table/my-table.vue";
import MyColumn from "@/components/table/my-column.vue";
import { formatTime } from "@/utils/dateStr";
import { listTasks, previewTasks, chartData, detailData, detailTask } from "@/api/boce/index";
import { TaskTypeMap } from "./components/index";
import * as echarts from "echarts";
// import myData from "./components/template.json";
// import myChartData from "./components/chartTemplate.json";
import { applicationStore } from "@/store/modules/application";
const useApplicationStore = applicationStore();
import { breadcrumbStore } from "@/store/modules/breadcurmb";
const usebreadcrumbStore = breadcrumbStore();

const setting = {
  title: {
    // text: "",
    x: "10px",
    y: "1px",
    textStyle: {
      fontSize: 16,
      fontWeight: "400",
      color: "#000000"
    }
  },

  grid: {
    left: "1px",
    right: "1%",
    bottom: "1px",
    top: "60px",
    containLabel: true
  }
};
echarts.registerMap("china", chaina as any);
const chartOptions = ref({
  ...setting,

  tooltip: {
    trigger: "item",
    formatter: (params: any) => {
      const { name, data } = params;
      if (!data) return `${name}<br/>无数据`;
      const formatVal = (val: number | undefined) =>
        !val || val === 0 ? "<span style='color:#f00'>失败</span>" : `${val}ms`;
      return `<div style="line-height:1.6">
    <strong>${name}</strong><br/>
    地区：${data.location || "--"}<br/>
    平均响应：${formatVal(data.avgTime)}<br/>
    <span style="color:#ff9900">[联通]</span> ${formatVal(data.unicom)}<br/>
    <span style="color:#32cd32">[电信]</span> ${formatVal(data.telecom)}<br/>
    <span style="color:#00c0ff">[移动]</span> ${formatVal(data.cmcc)}
  </div>`;
    }
  },
  visualMap: {
    type: "piecewise",
    left: "left",
    top: "top",

    pieces: [
      { min: -1, max: 0, label: "失败", color: "#999999" },
      { min: 1, max: 9, label: "<10ms", color: "#00ff00" },
      { min: 10, max: 79, label: "10ms - 80ms", color: "#adff2f" },
      { min: 80, max: 149, label: "80ms - 150ms", color: "#fcd763" },
      { min: 150, max: 249, label: "150ms - 250ms", color: "#ff9661" },
      { min: 250, label: ">250ms", color: "#ff3300" }
    ],

    calculable: true
  },
  series: [
    {
      name: "拨测结果",
      type: "map",
      map: "china",
      roam: false,
      emphasis: {
        label: {
          show: true
        }
      },
      data: []
    }
  ]
});
// 分页和搜索参数
const pageParams = reactive({
  appid: "",
  ip: "",
  page: 1,
  rows: 10
});
const list = reactive({
  records: [] as any[],
  total: 0,
  overview: [] as any[]
});
const tableLoading = ref(false);
const overViewLoading = ref(false);
const chartOptionsLoading = ref(false);
const handleSizeChange = (val: number) => {
  pageParams.rows = val;
  showHost();
};
//分页
const handleCurrentChange = (val: number) => {
  pageParams.page = val;
  showHost();
};
// 详情抽屉相关
const hostVisible = ref(false);
const hostVisible1 = ref(false);
const hostTitle = ref("");
interface TracertAnalysisItem {
  TracertIP?: string;
  TracertTime?: string;
  [key: string]: any;
}

const currentRecord = ref<{
  baseInfo: {
    Task: string;
    taskIP: string;
    taskType: string;
    ProbeTime: string;
    ProbeIP: string;
    Operator: string;
    City: string;
    StatusType: string;
    ErrorCode: string;
  };
  detailInfo: {
    PingTime: string;
    PingPackageLossRate: string;
    DNSQueryTime: string;
    TracertAvgTime: string;
    TracertErrorInfo: string;
    CnameAddress: string;
    AAddress: string;
    DNSDetail: string;
    PingServerIp: string;
    TracertAnalysis: TracertAnalysisItem[];
  };
  pingOutput: string;
  dnsOutput: string;
  httpOutput: string;
}>({
  baseInfo: {
    Task: "",
    taskIP: "",
    taskType: "",
    ProbeTime: "",
    ProbeIP: "",
    Operator: "",
    City: "",
    StatusType: "",
    ErrorCode: ""
  },
  detailInfo: {
    PingTime: "",
    PingPackageLossRate: "",
    DNSQueryTime: "",
    TracertAvgTime: "",
    TracertErrorInfo: "",
    CnameAddress: "",
    AAddress: "",
    DNSDetail: "",
    PingServerIp: "",
    TracertAnalysis: []
  },
  pingOutput: "",
  dnsOutput: "",
  httpOutput: ""
});

// 点击查看详情，打开抽屉显示详细信息
const showHost = async () => {
  tableLoading.value = true;
  try {
    const res = await detailData({
      taskId: boceValue.value.id,
      pageNum: pageParams.page,
      pageSize: pageParams.rows
    });
    if (res.code === 0 && res.entity.data.length > 0) {
      list.records = res.entity.data;
      list.total = Number(res.entity.total);
    } else if (res.code === 0 && res.entity.data.length === 0) {
      list.records = [];
      list.total = 0;
    }
    tableLoading.value = false;
  } catch (e) {
    tableLoading.value = false;
    ElMessage.error("获取拨测数据失败");
    list.records = [];
    list.total = 0;
  }
};

const fetchDetail = async (row: any) => {
  getDetail(row);
  hostTitle.value = `详情`;
  hostVisible1.value = true;
};
const drawerLoading = ref(false);
const getDetail = async (row: any) => {
  drawerLoading.value = true;
  try {
    const res = await detailTask({
      taskId: taskId.value
    });

    if (res.code === 0) {
      const entity = res.entity || {};
      currentRecord.value.baseInfo = {
        ...currentRecord.value.baseInfo,
        taskIP: taskIP.value,
        taskType: taskType.value,
        ...(entity.detailInfo.baseInfo || {})
      };
      currentRecord.value.detailInfo = {
        ...currentRecord.value.detailInfo,
        ...(entity.detailInfo.detailInfo || {})
      };
      currentRecord.value.pingOutput = entity.detailInfo.pingOutput || "";
      currentRecord.value.dnsOutput = entity.detailInfo.dnsOutput || "";
      currentRecord.value.httpOutput = entity.detailInfo.httpOutput || "";

      console.log(currentRecord.value);
    }
  } catch (e) {
    ElMessage.error("获取拨测详情数据失败");
  } finally {
    drawerLoading.value = false;
  }
};

// 获取拨测任务列表
const boceList = ref([]);
const taskId = ref("");
const boceData = ref([]);
const taskIP = ref("");
const taskType = ref("");
const boceValue = ref<any>({});
const getBoceMes = async () => {
  try {
    const res = await listTasks({
      appid: useApplicationStore.appId,
      appName: usebreadcrumbStore.appOption,
      pageNum: 1,
      pageSize: 10
    });
    if (res.code === 0 && res.entity.data.length > 0) {
      boceList.value = res.entity.data.map(item => ({
        label: item.name,
        value: item.id
      }));
      taskId.value = res.entity.data[0].id;
      taskIP.value = res.entity.data[0].targetAddress;
      taskType.value = res.entity.data[0].taskType;
      boceData.value = res.entity.data;
      boceValue.value = res.entity.data[0];
      getChartData();
      getOverviewData();
      showHost();
    }
    tableLoading.value = false;
  } catch (e) {
    ElMessage.error("获取拨测TOP5数据失败");
  }
};

// 处理任务选择变化
const handleTaskChange = (val: any) => {
  const selectedTask = boceData.value.find(item => item.id == val);
  if (selectedTask) {
    boceValue.value = selectedTask;

    getChartData();
    getOverviewData();
    showHost();
  }
};

// 查询图表数据
const getChartData = async () => {
  chartOptionsLoading.value = true;
  try {
    const res = await chartData({
      taskId: boceValue.value.id
      // dateSize: 5,
      // dateUnit: "m",
      // url: boceValue.value.targetAddress
    });
    if (res.code === 0) {
      // chartOptions.value.series[0].data = [...myChartData];
      chartOptions.value.series[0].data = res.entity.list.map((item: any) => {
        return {
          name: item.name,
          value: Number(item.avgTime),
          avgTime: Number(item.avgTime),
          telecom: Number(item.telecom),
          unicom: Number(item.unicom),
          cmcc: Number(item.cmcc),
          location: item.location
        };
      });
    }
    chartOptionsLoading.value = false;
  } catch (e) {
    ElMessage.error("获取图表数据失败");
    chartOptionsLoading.value = false;
  }
};

// 查询概览数据
const getOverviewData = async () => {
  overViewLoading.value = true;
  try {
    const res = await previewTasks({
      taskId: boceValue.value.id
      // dateSize: 5,
      // dateUnit: "m",
      // url: boceValue.value.targetAddress
    });
    if (res.code === 0) {
      list.overview = res.entity.list;
    }
    overViewLoading.value = false;
  } catch (e) {
    overViewLoading.value = false;
    ElMessage.error("获取拨测概览数据失败");
  }
};
const clearDrawerData = () => {
  currentRecord.value = {
    baseInfo: {
      Task: "",
      taskIP: "",
      taskType: "",
      ProbeTime: "",
      ProbeIP: "",
      Operator: "",
      City: "",
      StatusType: "",
      ErrorCode: ""
    },
    detailInfo: {
      PingTime: "",
      PingPackageLossRate: "",
      DNSQueryTime: "",
      TracertAvgTime: "",
      TracertErrorInfo: "",
      CnameAddress: "",
      AAddress: "",
      DNSDetail: "",
      PingServerIp: "",
      TracertAnalysis: []
    },
    pingOutput: "",
    dnsOutput: "",
    httpOutput: ""
  };
  hostTitle.value = "";
};
onMounted(() => {
  getBoceMes();

  // 强制重新渲染
  nextTick(() => {
    chartOptions.value = { ...chartOptions.value }; // 触发响应式
  });
});
</script>

<style lang="scss" scoped>
.tb-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.operate {
  color: #0064c8;
  cursor: pointer;
}
.detail-content {
  padding: 10px;
  font-size: 14px;
  line-height: 1.6;
}
:deep(.el-drawer__header) {
  margin: 5px !important;
}
.status-code {
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.status-dot {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
}
.tb-header {
  margin-bottom: 10px;
  display: flex;
  justify-content: end;
}
</style>
