[{"hostName": "青海/联通", "ip": "*************", "sent": 10, "recv": 10, "packetLoss": "0%", "statusCode": 200, "minTime": 28.6, "maxTime": 34.5, "avgTime": 31.2, "pingOutput": "正在 Ping ************ 具有 32 字节的数据:\n来自 ************ 的回复: 字节=32 时间<1ms TTL=128\n来自 ************ 的回复: 字节=32 时间<1ms TTL=128\n来自 ************ 的回复: 字节=32 时间<1ms TTL=128\n来自 ************ 的回复: 字节=32 时间<1ms TTL=128\n\n************ 的 Ping 统计信息:\n    数据包: 已发送 = 4，已接收 = 4，丢失 = 0 (0% 丢失)，\n往返行程的估计时间(以毫秒为单位):\n    最短 = 0ms，最长 = 0ms，平均 = 0ms", "httpOutput": " HTTP/1.1 302 Moved Temporarily\n Server: bfe/********\n Date: Tue, 08 Apr 2025 06:01:59 GMT\n Content-Type: text/html\n Content-Length: 161\n Connection: Keep-Alive\n Location: https://www.baidu.com/\n Expires: Wed, 09 Apr 2025 06:01:59 GMT\n Cache-Control: max-age=86400\n Cache-Control: privae\n \n HTTP/1.1 200 OK\n Bdpagetype: 1\n Bdqid: 0xbc7d35890006da4f\n Connection: keep-alive\n Content-Encoding: gzip\n Content-Type: text/html; charset=utf-8\n Date: Tue, 08 Apr 2025 06:01:59 GMT\n P3p: CP=\" OTI DSP COR IVA OUR IND COM \"\n P3p: CP=\" OTI DSP COR IVA OUR IND COM \"\n Server: BWS/1.1\n Set-Cookie: BAIDUID=6AFC1FF7AABEBB88C733FDEAA52946F1:FG=1; expires=Thu, 31-Dec-37 23:55:55 GMT; max-age=2147483647; path=/; domain=.baidu.com\n Set-Cookie: BIDUPSID=6AFC1FF7AABEBB88C733FDEAA52946F1; expires=Thu, 31-Dec-37 23:55:55 GMT; max-age=2147483647; path=/; domain=.baidu.com\n Set-Cookie: PSTM=1744092119; expires=Thu, 31-Dec-37 23:55:55 GMT; max-age=2147483647; path=/; domain=.baidu.com\n Set-Cookie: BAIDUID=6AFC1FF7AABEBB88D86A3C4F10977D5F:FG=1; max-age=31536000; expires=Wed, 08-Apr-26 06:01:59 GMT; domain=.baidu.com; path=/; version=1; comment=bd\n Set-Cookie: BDSVRTM=2; path=/\n Set-Cookie: BD_HOME=1; path=/\n Set-Cookie: BAIDUID_BFESS=6AFC1FF7AABEBB88C733FDEAA52946F1:FG=1; Path=/; Domain=baidu.com; Expires=Thu, 31 Dec 2037 23:55:55 GMT; Max-Age=2147483647; Secure; SameSite=None\n Strict-Transport-Security: max-age=172800\n Traceid: 1744092119261455130613582070913746524751\n X-Ua-Compatible: IE=Edge,chrome=1\n X-Xss-Protection: 1;mode=block\n Transfer-Encoding: chunked", "dnsOutput": "baidu.com.\t\t0\tIN\tA\t39.156.66.10\nbaidu.com.\t\t0\tIN\tA\t110.242.68.66\n;; Query time: 1 msec\n;; SERVER: **************#53(**************) (UDP)\n;; WHEN: Tue Apr 08 14:03:06 CST 2025\n;; MSG SIZE  rcvd: 59"}, {"hostName": "青海/电信", "ip": "*************", "sent": 10, "recv": 10, "packetLoss": "0%", "statusCode": 200, "minTime": 27.4, "maxTime": 33.1, "avgTime": 30.7, "pingOutput": "正在 Ping ************ 具有 32 字节的数据:\n来自 ************ 的回复: 字节=32 时间<1ms TTL=128\n来自 ************ 的回复: 字节=32 时间<1ms TTL=128\n来自 ************ 的回复: 字节=32 时间<1ms TTL=128\n来自 ************ 的回复: 字节=32 时间<1ms TTL=128\n\n************ 的 Ping 统计信息:\n    数据包: 已发送 = 4，已接收 = 4，丢失 = 0 (0% 丢失)，\n往返行程的估计时间(以毫秒为单位):\n    最短 = 0ms，最长 = 0ms，平均 = 0ms", "httpOutput": " HTTP/1.1 302 Moved Temporarily\n Server: bfe/********\n Date: Tue, 08 Apr 2025 06:01:59 GMT\n Content-Type: text/html\n Content-Length: 161\n Connection: Keep-Alive\n Location: https://www.baidu.com/\n Expires: Wed, 09 Apr 2025 06:01:59 GMT\n Cache-Control: max-age=86400\n Cache-Control: privae\n \n HTTP/1.1 200 OK\n Bdpagetype: 1\n Bdqid: 0xbc7d35890006da4f\n Connection: keep-alive\n Content-Encoding: gzip\n Content-Type: text/html; charset=utf-8\n Date: Tue, 08 Apr 2025 06:01:59 GMT\n P3p: CP=\" OTI DSP COR IVA OUR IND COM \"\n P3p: CP=\" OTI DSP COR IVA OUR IND COM \"\n Server: BWS/1.1\n Set-Cookie: BAIDUID=6AFC1FF7AABEBB88C733FDEAA52946F1:FG=1; expires=Thu, 31-Dec-37 23:55:55 GMT; max-age=2147483647; path=/; domain=.baidu.com\n Set-Cookie: BIDUPSID=6AFC1FF7AABEBB88C733FDEAA52946F1; expires=Thu, 31-Dec-37 23:55:55 GMT; max-age=2147483647; path=/; domain=.baidu.com\n Set-Cookie: PSTM=1744092119; expires=Thu, 31-Dec-37 23:55:55 GMT; max-age=2147483647; path=/; domain=.baidu.com\n Set-Cookie: BAIDUID=6AFC1FF7AABEBB88D86A3C4F10977D5F:FG=1; max-age=31536000; expires=Wed, 08-Apr-26 06:01:59 GMT; domain=.baidu.com; path=/; version=1; comment=bd\n Set-Cookie: BDSVRTM=2; path=/\n Set-Cookie: BD_HOME=1; path=/\n Set-Cookie: BAIDUID_BFESS=6AFC1FF7AABEBB88C733FDEAA52946F1:FG=1; Path=/; Domain=baidu.com; Expires=Thu, 31 Dec 2037 23:55:55 GMT; Max-Age=2147483647; Secure; SameSite=None\n Strict-Transport-Security: max-age=172800\n Traceid: 1744092119261455130613582070913746524751\n X-Ua-Compatible: IE=Edge,chrome=1\n X-Xss-Protection: 1;mode=block\n Transfer-Encoding: chunked", "dnsOutput": "baidu.com.\t\t0\tIN\tA\t39.156.66.10\nbaidu.com.\t\t0\tIN\tA\t110.242.68.66\n;; Query time: 1 msec\n;; SERVER: **************#53(**************) (UDP)\n;; WHEN: Tue Apr 08 14:03:06 CST 2025\n;; MSG SIZE  rcvd: 59"}, {"hostName": "青海/移动", "ip": "*************", "sent": 10, "recv": 9, "packetLoss": "10%", "statusCode": 504, "minTime": 38.2, "maxTime": 42.1, "avgTime": 39.7, "pingOutput": "正在 Ping ************ 具有 32 字节的数据:\n来自 ************ 的回复: 字节=32 时间<1ms TTL=128\n来自 ************ 的回复: 字节=32 时间<1ms TTL=128\n来自 ************ 的回复: 字节=32 时间<1ms TTL=128\n来自 ************ 的回复: 字节=32 时间<1ms TTL=128\n\n************ 的 Ping 统计信息:\n    数据包: 已发送 = 4，已接收 = 4，丢失 = 0 (0% 丢失)，\n往返行程的估计时间(以毫秒为单位):\n    最短 = 0ms，最长 = 0ms，平均 = 0ms", "httpOutput": " HTTP/1.1 302 Moved Temporarily\n Server: bfe/********\n Date: Tue, 08 Apr 2025 06:01:59 GMT\n Content-Type: text/html\n Content-Length: 161\n Connection: Keep-Alive\n Location: https://www.baidu.com/\n Expires: Wed, 09 Apr 2025 06:01:59 GMT\n Cache-Control: max-age=86400\n Cache-Control: privae\n \n HTTP/1.1 200 OK\n Bdpagetype: 1\n Bdqid: 0xbc7d35890006da4f\n Connection: keep-alive\n Content-Encoding: gzip\n Content-Type: text/html; charset=utf-8\n Date: Tue, 08 Apr 2025 06:01:59 GMT\n P3p: CP=\" OTI DSP COR IVA OUR IND COM \"\n P3p: CP=\" OTI DSP COR IVA OUR IND COM \"\n Server: BWS/1.1\n Set-Cookie: BAIDUID=6AFC1FF7AABEBB88C733FDEAA52946F1:FG=1; expires=Thu, 31-Dec-37 23:55:55 GMT; max-age=2147483647; path=/; domain=.baidu.com\n Set-Cookie: BIDUPSID=6AFC1FF7AABEBB88C733FDEAA52946F1; expires=Thu, 31-Dec-37 23:55:55 GMT; max-age=2147483647; path=/; domain=.baidu.com\n Set-Cookie: PSTM=1744092119; expires=Thu, 31-Dec-37 23:55:55 GMT; max-age=2147483647; path=/; domain=.baidu.com\n Set-Cookie: BAIDUID=6AFC1FF7AABEBB88D86A3C4F10977D5F:FG=1; max-age=31536000; expires=Wed, 08-Apr-26 06:01:59 GMT; domain=.baidu.com; path=/; version=1; comment=bd\n Set-Cookie: BDSVRTM=2; path=/\n Set-Cookie: BD_HOME=1; path=/\n Set-Cookie: BAIDUID_BFESS=6AFC1FF7AABEBB88C733FDEAA52946F1:FG=1; Path=/; Domain=baidu.com; Expires=Thu, 31 Dec 2037 23:55:55 GMT; Max-Age=2147483647; Secure; SameSite=None\n Strict-Transport-Security: max-age=172800\n Traceid: 1744092119261455130613582070913746524751\n X-Ua-Compatible: IE=Edge,chrome=1\n X-Xss-Protection: 1;mode=block\n Transfer-Encoding: chunked", "dnsOutput": "baidu.com.\t\t0\tIN\tA\t39.156.66.10\nbaidu.com.\t\t0\tIN\tA\t110.242.68.66\n;; Query time: 1 msec\n;; SERVER: **************#53(**************) (UDP)\n;; WHEN: Tue Apr 08 14:03:06 CST 2025\n;; MSG SIZE  rcvd: 59"}, {"hostName": "北京/联通", "ip": "*************", "sent": 10, "recv": 10, "packetLoss": "0%", "statusCode": 302, "minTime": 8.4, "maxTime": 11.2, "avgTime": 9.7, "pingOutput": "正在 Ping ************ 具有 32 字节的数据:\n来自 ************ 的回复: 字节=32 时间<1ms TTL=128\n来自 ************ 的回复: 字节=32 时间<1ms TTL=128\n来自 ************ 的回复: 字节=32 时间<1ms TTL=128\n来自 ************ 的回复: 字节=32 时间<1ms TTL=128\n\n************ 的 Ping 统计信息:\n    数据包: 已发送 = 4，已接收 = 4，丢失 = 0 (0% 丢失)，\n往返行程的估计时间(以毫秒为单位):\n    最短 = 0ms，最长 = 0ms，平均 = 0ms", "httpOutput": " HTTP/1.1 302 Moved Temporarily\n Server: bfe/********\n Date: Tue, 08 Apr 2025 06:01:59 GMT\n Content-Type: text/html\n Content-Length: 161\n Connection: Keep-Alive\n Location: https://www.baidu.com/\n Expires: Wed, 09 Apr 2025 06:01:59 GMT\n Cache-Control: max-age=86400\n Cache-Control: privae\n \n HTTP/1.1 200 OK\n Bdpagetype: 1\n Bdqid: 0xbc7d35890006da4f\n Connection: keep-alive\n Content-Encoding: gzip\n Content-Type: text/html; charset=utf-8\n Date: Tue, 08 Apr 2025 06:01:59 GMT\n P3p: CP=\" OTI DSP COR IVA OUR IND COM \"\n P3p: CP=\" OTI DSP COR IVA OUR IND COM \"\n Server: BWS/1.1\n Set-Cookie: BAIDUID=6AFC1FF7AABEBB88C733FDEAA52946F1:FG=1; expires=Thu, 31-Dec-37 23:55:55 GMT; max-age=2147483647; path=/; domain=.baidu.com\n Set-Cookie: BIDUPSID=6AFC1FF7AABEBB88C733FDEAA52946F1; expires=Thu, 31-Dec-37 23:55:55 GMT; max-age=2147483647; path=/; domain=.baidu.com\n Set-Cookie: PSTM=1744092119; expires=Thu, 31-Dec-37 23:55:55 GMT; max-age=2147483647; path=/; domain=.baidu.com\n Set-Cookie: BAIDUID=6AFC1FF7AABEBB88D86A3C4F10977D5F:FG=1; max-age=31536000; expires=Wed, 08-Apr-26 06:01:59 GMT; domain=.baidu.com; path=/; version=1; comment=bd\n Set-Cookie: BDSVRTM=2; path=/\n Set-Cookie: BD_HOME=1; path=/\n Set-Cookie: BAIDUID_BFESS=6AFC1FF7AABEBB88C733FDEAA52946F1:FG=1; Path=/; Domain=baidu.com; Expires=Thu, 31 Dec 2037 23:55:55 GMT; Max-Age=2147483647; Secure; SameSite=None\n Strict-Transport-Security: max-age=172800\n Traceid: 1744092119261455130613582070913746524751\n X-Ua-Compatible: IE=Edge,chrome=1\n X-Xss-Protection: 1;mode=block\n Transfer-Encoding: chunked", "dnsOutput": "baidu.com.\t\t0\tIN\tA\t39.156.66.10\nbaidu.com.\t\t0\tIN\tA\t110.242.68.66\n;; Query time: 1 msec\n;; SERVER: **************#53(**************) (UDP)\n;; WHEN: Tue Apr 08 14:03:06 CST 2025\n;; MSG SIZE  rcvd: 59"}]