<template>
  <div v-if="workflowDetail" class="workflow-detail-container">
    <div class="workflow-chart-container">
      <div id="workflow-detail-container" class="workflow-canvas"></div>
    </div>

    <!-- 右侧节点详情面板 -->
    <div v-if="selectedNode" class="node-detail-panel">
      <div class="panel-header">
        <span class="panel-title">节点详情</span>
        <el-button link type="primary" class="close-btn" @click="selectedNode = null">
          关闭
        </el-button>
      </div>
      <div class="panel-content">
        <div class="node-info">
          <div class="info-item">
            <label>节点ID:</label>
            <span>{{ selectedNode.id }}</span>
          </div>
          <div class="info-item">
            <label>节点类型:</label>
            <el-tag :type="selectedNode.type === 'datasource' ? 'success' : 'warning'">
              {{ selectedNode.type === "datasource" ? "数据源" : "AI分析" }}
            </el-tag>
          </div>

          <!-- 数据源节点配置 -->
          <div v-if="selectedNode.type === 'datasource'" class="config-section">
            <h4>数据源配置</h4>
            <div class="info-item">
              <label>数据源ID:</label>
              <span>{{ selectedNode.config?.source_id || "-" }}</span>
            </div>
          </div>

          <!-- LLM节点配置 -->
          <div v-if="selectedNode.type === 'llm'" class="config-section">
            <h4>AI分析配置</h4>
            <div class="info-item">
              <label>系统模板:</label>
              <span>{{ selectedNode.config?.system_template_name || "-" }}</span>
            </div>
            <div class="info-item">
              <label>用户模板:</label>
              <span>{{ selectedNode.config?.user_template_name || "-" }}</span>
            </div>
            <div class="info-item">
              <label>临时指令:</label>
              <span>{{ selectedNode.config?.adhoc_instruction || "-" }}</span>
            </div>
          </div>

          <!-- 参数映射 -->
          <div class="config-section">
            <h4>参数映射</h4>
            <div
              v-if="Object.keys(selectedNode.param_mappings || {}).length > 0"
              class="mapping-list"
            >
              <div v-for="(value, key) in selectedNode.param_mappings" :key="key">
                <div class="mapping-key">{{ key }}:</div>
                <div class="mapping-value">{{ value }}</div>
              </div>
            </div>
            <div v-else class="no-mapping">暂无参数映射</div>
          </div>

          <!-- 位置信息 -->
          <div class="config-section">
            <h4>位置信息</h4>
            <div class="info-item">
              <label>X坐标:</label>
              <span>{{ selectedNode.position?.x || 0 }}</span>
            </div>
            <div class="info-item">
              <label>Y坐标:</label>
              <span>{{ selectedNode.position?.y || 0 }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, nextTick } from "vue";
import LogicFlow from "@logicflow/core";
import { Menu, Snapshot, MiniMap, SelectionSelect } from "@logicflow/extension";
import "@logicflow/core/lib/style/index.css";
import "@logicflow/extension/lib/style/index.css";

interface WorkflowDetail {
  id: string;
  name: string;
  graph: {
    inputs: any[];
    nodes: any[];
    edges: any[];
    output_source: string;
    workflow_type: string;
  };
  dify_app_id: string;
  created_at: string;
  updated_at: string;
}

const props = defineProps<{
  workflowDetail: WorkflowDetail | null;
}>();

let lf: any = null;
const selectedNode = ref<any>(null);

// 初始化LogicFlow
const initLogicFlow = () => {
  if (!props.workflowDetail) return;

  nextTick(() => {
    const container = document.querySelector("#workflow-detail-container") as HTMLElement;
    if (!container) return;

    // 清理之前的实例
    if (lf) {
      lf.destroy();
    }

    lf = new LogicFlow({
      container: container,
      grid: {
        size: 10,
        visible: true,
        type: "dot",
        config: {
          color: "#a0a0a0",
          thickness: 1
        }
      },
      background: {
        backgroundColor: "#fafafa"
      },
      keyboard: {
        enabled: false // 禁用键盘操作
      },
      nodeTextEdit: false, // 禁用节点文本编辑
      edgeTextEdit: false, // 禁用边文本编辑
      plugins: [Menu, Snapshot, MiniMap, SelectionSelect],
      isSilentMode: false, // 允许点击交互
      enableWheel: false, // 禁用滚轮缩放
      enableDrag: false, // 禁用拖拽
      enableZoom: false // 禁用缩放
    });

    // 绑定节点点击事件
    bindNodeEvents();

    // 渲染工作流
    renderWorkflow();
  });
};

// 绑定节点事件
const bindNodeEvents = () => {
  if (!lf) return;

  // 节点点击事件
  lf.on("node:click", ({ data }: { data: any }) => {
    const nodeId = data.id;
    const node = props.workflowDetail?.graph.nodes.find(n => n.id === nodeId);
    if (node) {
      selectedNode.value = node;
    }
  });

  // 画布点击事件，取消选中
  lf.on("blank:click", () => {
    selectedNode.value = null;
  });
};

// 渲染工作流
const renderWorkflow = () => {
  if (!props.workflowDetail || !lf) return;

  // 计算节点位置，让图表居中显示
  const containerWidth = lf.container.clientWidth;
  const containerHeight = lf.container.clientHeight;
  const nodes = props.workflowDetail.graph.nodes;

  // 计算所有节点的边界
  let minX = Infinity,
    minY = Infinity,
    maxX = -Infinity,
    maxY = -Infinity;
  nodes.forEach(node => {
    const x = node.position?.x || 100;
    const y = node.position?.y || 100;
    minX = Math.min(minX, x);
    minY = Math.min(minY, y);
    maxX = Math.max(maxX, x);
    maxY = Math.max(maxY, y);
  });

  // 计算图表的中心偏移
  const graphWidth = maxX - minX;
  const graphHeight = maxY - minY;
  const offsetX = (containerWidth - graphWidth) / 2 - minX;
  const offsetY = (containerHeight - graphHeight) / 2 - minY;

  const flowData = {
    nodes: nodes.map(node => ({
      id: node.id,
      type: node.type === "datasource" ? "rect" : "circle",
      x: (node.position?.x || 100) + offsetX,
      y: (node.position?.y || 100) + offsetY,
      text: node.type === "datasource" ? "数据源" : "AI分析",
      properties: {
        width: node.type === "datasource" ? 120 : 100,
        height: node.type === "datasource" ? 60 : 100,
        fill: node.type === "datasource" ? "#f0f9ff" : "#fff7e6",
        stroke: node.type === "datasource" ? "#67c23a" : "#e6a23c",
        strokeWidth: 2,
        textColor: "#303133",
        fontSize: 14
      }
    })),
    edges: props.workflowDetail.graph.edges.map(edge => ({
      id: `${edge.source_node_id}-${edge.target_node_id}`,
      sourceNodeId: edge.source_node_id,
      targetNodeId: edge.target_node_id,
      type: "polyline",
      text: `${edge.source_output} → ${edge.target_input}`
    }))
  };

  lf.render(flowData);
};

// 监听工作流详情变化
watch(
  () => props.workflowDetail,
  () => {
    if (props.workflowDetail) {
      selectedNode.value = null; // 清空选中状态
      initLogicFlow();
    }
  },
  { immediate: true }
);

onMounted(() => {
  if (props.workflowDetail) {
    initLogicFlow();
  }
});
</script>

<style scoped>
.workflow-detail-container {
  height: 100%;
  display: flex;
  gap: 16px;
}

.workflow-chart-container {
  flex: 1;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: #fff;
  overflow: hidden;
}

.workflow-canvas {
  width: 100%;
  height: 100%;
  position: relative;
}

.node-detail-panel {
  width: 320px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: #fff;
  display: flex;
  flex-direction: column;
}

.panel-header {
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.close-btn {
  padding: 0;
  font-size: 14px;
}

.panel-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.node-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 0;
}

.info-item label {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
}

.info-item span {
  color: #303133;
  word-break: break-all;
}

.config-section {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 12px;
  background: #fafafa;
}

.config-section h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.instruction-text {
  max-height: 100px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-break: break-word;
  background: #fff;
  padding: 8px;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
}

.mapping-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.mapping-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 6px 8px;
  background: #fff;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
}

.mapping-key {
  font-weight: 500;
  color: #606266;
  min-width: 60px;
}

.mapping-value {
  color: #303133;
  word-break: break-all;
  flex: 1;
}

.no-mapping {
  color: #909399;
  font-style: italic;
  text-align: center;
  padding: 12px;
}

:deep(.lf-mini-map) {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

:deep(.lf-selection-select) {
  border: 2px solid #409eff;
}

:deep(.lf-node) {
  cursor: pointer;
}

:deep(.lf-node:hover) {
  opacity: 0.8;
}

:deep(.lf-edge) {
  cursor: default;
}
</style>
