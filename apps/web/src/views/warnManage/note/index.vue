<template>
  <div class="tb-header">
    <div>
      <el-input
        style="width: 240px"
        placeholder="请输入关键字"
        v-model="keyWords"
        clearable
      ></el-input>
      <el-button type="primary" style="margin-left: 15px" :icon="Search">搜索</el-button>
    </div>
  </div>
  <MyTable
    class="mt-20px"
    :data="list.records"
    :total="list.total"
    v-loading="tableLoading"
    @sizeChange="handleSizeChange"
    @currentChange="handleCurrentChange"
    style="width: 100%"
  >
    <my-column property="content" label="告警信息" />
    <my-column property="value" label="告警级别" />
    <my-column property="method" label="通知方式" />
    <my-column property="notifier" label="通知人" />
    <my-column property="status" label="状态" />
    <my-column property="duration" label="持续时间" />
    <my-column property="time" label="告警时间" />
  </MyTable>
</template>
<script setup lang="ts">
import { Search } from "@element-plus/icons-vue";
const tableLoading = ref(false);
const list = reactive({
  records: [
    {
      content: "主机test-server的内存使用率当前为82.57%",
      value: "严重告警",
      method: "邮件",
      notifier: "陈傲迪",
      status: "已解决",
      duration: "3天23小时53分27秒",
      time: "2025-5-17 14:29:31"
    },
    {
      content: "主机test-server的CPU使用率当前为85%",
      value: "严重告警",
      method: "短信",
      notifier: "周琦",
      status: "未解决",
      duration: "3天23小时23分23秒",
      time: "2025-5-17 14:29:35"
    },
    {
      content: "服务wlisuat-bpm错误数超过一万",
      value: "一般告警",
      method: "短信",
      notifier: "陈卓禹",
      status: "未解决",
      duration: "1天6小时6分6秒",
      time: "2025-5-17 14:34:07"
    }
  ],
  total: 3
});
//修改每页条数
const handleSizeChange = (val: number) => {
  console.log(val);
};
//分页
const handleCurrentChange = (val: number) => {
  console.log(val);
};
const keyWords = ref("");
</script>
<style lang="scss" scoped>
.tb-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
</style>
