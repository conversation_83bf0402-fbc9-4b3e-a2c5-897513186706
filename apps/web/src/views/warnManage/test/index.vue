<template>
  <div class="tb-header">
    <div>
      <el-input
        style="width: 240px; margin-left: 20px"
        placeholder="请输入关键字"
        v-model="keyWords"
        clearable
      ></el-input>
      <el-select
        style="width: 240px; margin-left: 15px"
        placeholder="请选择类别"
        clearable
        v-model="category"
      >
        <el-option label="应用" value="应用"></el-option>
        <el-option label="系统" value="系统"></el-option>
      </el-select>
      <el-button type="primary" style="margin-left: 15px" :icon="Search">搜索</el-button>
    </div>
    <div>
      <el-button type="primary" @click="addAlgorithm" style="margin-right: 25px">新增</el-button>
    </div>
  </div>
  <div class="mcp-container">
    <el-row :gutter="20">
      <el-col
        v-for="(item, index) in alertAlgorithms"
        :key="index"
        :xs="24"
        :sm="12"
        :md="8"
        :lg="6"
      >
        <el-card class="mcp-card">
          <div class="card-header">
            <div class="left">
              <div>
                <h3 class="card-title">{{ item.title }}</h3>
                <div class="author">使用次数：{{ item.number }}</div>
              </div>
            </div>
            <div class="right">
              <el-tag :type="item.type as any" size="small" class="tag">
                {{ item.category }}
              </el-tag>
              <div class="date">{{ item.date }}</div>
            </div>
          </div>

          <div class="card-content">
            {{ item.description }}
            <pre class="code-preview">{{ item.code }}</pre>
          </div>
          <div class="card-actions">
            <el-button type="primary" link @click="editAlgorithm(item)">编辑</el-button>
            <el-button type="danger" link @click="deleteVisible = true">删除</el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
  <div class="pagination">
    <el-pagination
      v-model:current-page="currentPage4"
      v-model:page-size="pageSize4"
      :page-sizes="[10, 20, 30]"
      :background="background"
      layout="total, sizes, prev, pager, next, jumper"
      :total="5"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
  <el-dialog
    :align-center="true"
    v-model="dialogFormVisible"
    :title="title"
    width="600"
    :close-on-click-modal="false"
  >
    <el-form :model="state.form" label-width="auto">
      <el-form-item label="标题：">
        <el-input v-model="state.form.title" placeholder="请输入标题" clearable></el-input>
      </el-form-item>
      <el-form-item label="类型：">
        <el-input v-model="state.form.category" placeholder="请输入类型" clearable></el-input>
      </el-form-item>
      <el-form-item label="描述：">
        <el-input
          v-model="state.form.description"
          type="textarea"
          :autosize="{ minRows: 3, maxRows: 5 }"
        ></el-input>
      </el-form-item>
      <el-form-item label="代码：">
        <el-input
          v-model="state.form.code"
          type="textarea"
          :autosize="{ minRows: 3, maxRows: 5 }"
        ></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="dialogFormVisible = false">确定</el-button>
      </div>
    </template>
  </el-dialog>
  <el-dialog
    :align-center="true"
    v-model="deleteVisible"
    title="删除"
    width="500"
    :close-on-click-modal="false"
  >
    <span>确认要删除吗？</span>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="deleteVisible = false">取消</el-button>
        <el-button type="primary" @click="deleteVisible = false"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { Search } from "@element-plus/icons-vue";
interface AlertAlgorithm {
  title: string;
  number: number;
  description: string;
  category: string;
  type: "success" | "warning" | "danger" | "info";
  date: string;
  code: string;
}
const currentPage4 = ref(1);
const pageSize4 = ref(10);
const background = ref(false);
const handleSizeChange = (val: number) => {
  console.log(`${val} items per page`);
};
const handleCurrentChange = (val: number) => {
  console.log(`current page: ${val}`);
};
const dialogFormVisible = ref(false);
const deleteVisible = ref(false);
const title = ref("");

const addAlgorithm = () => {
  dialogFormVisible.value = true;
  title.value = "新增算法";
  state.form = { title: "", description: "", category: "", code: "" };
};
const editAlgorithm = (item: AlertAlgorithm) => {
  dialogFormVisible.value = true;
  title.value = "编辑算法";
  state.form = { ...item };
};

const state = reactive({
  form: {
    title: "",
    description: "",
    category: "",
    code: ""
  }
});
const keyWords = ref("");
const category = ref("");
const alertAlgorithms: AlertAlgorithm[] = [
  {
    title: "实时异常检测",
    number: 15,
    description: "基于时间序列分析的实时异常检测算法，支持多维度指标监控",
    code: "import warnings \nimport sys \nif sys.argv[1] == '-w': \nwarnings.warn('This is the warning message you wanted!') \nelse: \nprint ('No warnings!')",
    category: "实时监控",
    type: "danger",
    date: "2025.04.25"
  },
  {
    title: "预测性维护",
    number: 10,
    description: "使用机器学习模型预测设备故障概率，提前生成维护预警...",
    code: "import warnings \nimport sys \nif sys.argv[1] == '-w': \nwarnings.warn('This is the warning message you wanted!') \nelse: \nprint ('No warnings!')",
    category: "预测分析",
    type: "warning",
    date: "2025.04.20"
  },
  {
    title: "日志模式识别",
    number: 10,
    description: "通过NLP技术解析日志文本，识别异常模式并生成告警事件...",
    code: "import warnings \nimport sys \nif sys.argv[1] == '-w': \nwarnings.warn('This is the warning message you wanted!') \nelse: \nprint ('No warnings!')",
    category: "日志分析",
    type: "info",
    date: "2025.04.22"
  },
  {
    title: "网络流量基线",
    number: 8,
    description: "建立网络流量动态基线，检测偏离基线的异常流量模式...",
    code: "import warnings \nimport sys \nif sys.argv[1] == '-w': \nwarnings.warn('This is the warning message you wanted!') \nelse: \nprint ('No warnings!')",
    category: "网络安全",
    type: "success",
    date: "2025.04.18"
  },
  {
    title: "网络流量基线",
    number: 0,
    description: "建立网络流量动态基线，检测偏离基线的异常流量模式...",
    code: "import warnings \nimport sys \nif sys.argv[1] == '-w': \nwarnings.warn('This is the warning message you wanted!') \nelse: \nprint ('No warnings!')",
    category: "网络安全",
    type: "success",
    date: "2025.04.18"
  }
];
</script>

<style lang="scss" scoped>
.mcp-container {
  max-width: 1800px;
  margin: 20px auto;
  padding: 0 20px;
}

.header {
  text-align: left;
  margin-bottom: 30px;
}

.header h1 {
  font-size: 24px;
  margin-bottom: 8px;
}

.sub-header {
  color: #666;
  font-size: 14px;
}

.mcp-card {
  height: 280px;
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.card-icon {
  font-size: 20px;
  color: #409eff;
}

.card-title {
  margin: 0;
  font-size: 16px;
}

.author {
  font-size: 12px;
  color: #999;
}

.right {
  text-align: right;
}

.tag {
  margin-bottom: 4px;
}

.date {
  font-size: 12px;
  color: #999;
}

.card-content {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
  margin-top: 10px;
}
.pagination {
  margin-top: 15px;
  margin-right: 20px;
  display: flex;
  align-items: center;
}
.tb-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.code-preview {
  max-height: 120px;
  overflow-y: auto;
  margin-top: 6px;
  padding: 4px;
  font-size: 14px;
  white-space: pre-wrap;
  word-break: break-word;
  font-family: inherit;
  color: #666;
}
.card-actions {
  margin-top: 15px;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
