<template>
  <div class="property-panel">
    <div class="panel-header">
      <span class="panel-title">节点配置</span>
      <el-button link type="primary" class="close-btn" @click="$emit('close')"> 关闭 </el-button>
    </div>
    <div class="property-content">
      <el-form :model="localNode" label-position="top" size="small">
        <el-form-item label="节点ID">
          <el-input v-model="localNode.id" disabled></el-input>
        </el-form-item>
        <el-form-item label="节点类型">
          <el-input v-model="localNode.nodeType" disabled></el-input>
        </el-form-item>

        <!-- 数据源节点配置 -->
        <template v-if="isDataSourceNode">
          <el-form-item label="数据源类型" required>
            <el-select
              v-model="nodeConfig.source_id"
              placeholder="请选择数据源类型"
              @change="updateNode"
            >
              <el-option-group
                v-for="source in dataSources"
                :key="source.name"
                :label="source.name"
              >
                <el-option
                  v-for="dataType in source.data_types"
                  :key="dataType.id"
                  :label="`${dataType.description} (${dataType.id})`"
                  :value="dataType.id"
                >
                  <div class="option-content">
                    <div class="option-title">{{ dataType.description }}</div>
                    <div class="option-id">{{ dataType.id }}</div>
                    <div class="option-params">
                      必需参数: {{ dataType.required_params.join(", ") }}
                    </div>
                  </div>
                </el-option>
              </el-option-group>
            </el-select>
          </el-form-item>
          <el-form-item label="参数映射">
            <div v-for="(value, key) in nodeParamMappings" :key="key" class="param-mapping">
              <span class="param-key">{{ key }}:</span>
              <el-input
                v-model="nodeParamMappings[key]"
                placeholder="表达式"
                @input="updateNode"
              ></el-input>
            </div>
            <el-button size="small" @click="addParamMapping">添加参数映射</el-button>
          </el-form-item>
        </template>

        <!-- AI分析节点配置 -->
        <template v-if="isLLMNode">
          <el-form-item label="系统模板名称" required>
            <el-select
              v-model="nodeConfig.system_template_name"
              placeholder="请选择系统模板"
              @change="updateNode"
            >
              <el-option
                v-for="template in promptTemplates"
                :key="template.id"
                :label="template.name"
                :value="template.name"
              >
                <div class="option-content">
                  <div class="option-title">{{ template.name }}</div>
                  <div class="option-id">ID: {{ template.id }}</div>
                  <div class="option-scope">范围: {{ template.scope }}</div>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="用户模板名称">
            <el-input
              v-model="nodeConfig.user_template_name"
              placeholder="用户模板名称"
              @input="updateNode"
            ></el-input>
          </el-form-item>
          <el-form-item label="临时指令">
            <el-input
              type="textarea"
              v-model="nodeConfig.adhoc_instruction"
              :rows="3"
              placeholder="临时指令"
              @input="updateNode"
            ></el-input>
          </el-form-item>
          <el-form-item label="参数映射">
            <div v-for="(value, key) in nodeParamMappings" :key="key" class="param-mapping">
              <span class="param-key">{{ key }}:</span>
              <el-input
                v-model="nodeParamMappings[key]"
                placeholder="表达式"
                @input="updateNode"
              ></el-input>
            </div>
            <el-button size="small" @click="addParamMapping">添加参数映射</el-button>
          </el-form-item>
        </template>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, computed, ref, watch } from "vue";

interface DataType {
  id: string;
  description: string;
  required_params: string[];
}

interface DataSource {
  name: string;
  data_types: DataType[];
}

interface PromptTemplate {
  id: number;
  template_group_id: string;
  name: string;
  content: string;
  version: number;
  scope: string;
  owner_id: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

const props = defineProps({
  node: {
    type: Object,
    required: true
  },
  dataSources: {
    type: Array as () => DataSource[],
    default: () => []
  },
  promptTemplates: {
    type: Array as () => PromptTemplate[],
    default: () => []
  }
});

const emit = defineEmits(["update-node", "close"]);

// 创建本地副本避免直接修改props
const localNode = ref({ ...props.node });

// 监听props变化
watch(
  () => props.node,
  newNode => {
    localNode.value = { ...newNode };
  },
  { deep: true }
);

// 判断节点类型
const isDataSourceNode = computed(() => localNode.value.nodeType === "datasource");
const isLLMNode = computed(() => localNode.value.nodeType === "llm");

// 确保config对象存在
const nodeConfig = computed(() => {
  return localNode.value.config || {};
});

// 确保param_mappings对象存在
const nodeParamMappings = computed(() => {
  return localNode.value.param_mappings || {};
});

// 添加参数映射
const addParamMapping = () => {
  const key = `param_${Object.keys(nodeParamMappings.value).length + 1}`;
  nodeParamMappings.value[key] = "";
  updateNode();
};

// 更新节点
const updateNode = () => {
  emit("update-node", localNode.value);
};
</script>

<style lang="scss" scoped>
.property-panel {
  position: absolute;
  right: 5px;
  top: 20px;
  width: 350px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 1000;
  max-height: 80vh;
  display: flex;
  flex-direction: column;

  .panel-header {
    height: 50px;
    border-bottom: 1px solid #e4e7ed;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    background: #fafafa;
    border-radius: 8px 8px 0 0;

    .panel-title {
      font-size: 16px;
      font-weight: 500;
      color: #303133;
    }

    .close-btn {
      padding: 0;
      font-size: 16px;
    }
  }

  .property-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;

    .param-mapping {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 10px;

      .param-key {
        min-width: 80px;
        font-size: 12px;
        color: #606266;
      }
    }

    .option-content {
      .option-title {
        font-weight: 500;
        margin-bottom: 4px;
      }

      .option-id {
        font-size: 12px;
        color: #909399;
        margin-bottom: 2px;
      }

      .option-params,
      .option-scope {
        font-size: 11px;
        color: #c0c4cc;
      }
    }
  }
}
</style>
