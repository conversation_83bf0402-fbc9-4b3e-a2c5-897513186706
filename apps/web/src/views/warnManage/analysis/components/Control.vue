<template>
  <div>
    <!-- 画布右上角几个按钮 -->
    <div class="panelLayout" style="background: #fff; top: -4px; right: 40px; height: 300px">
      <el-tooltip content="放大" title="放大" placement="left">
        <el-icon @click="$_zoomIn">
          <ZoomIn />
        </el-icon>
      </el-tooltip>
      <el-tooltip content="缩小" title="缩小" placement="left">
        <el-icon @click="$_zoomOut">
          <ZoomOut />
        </el-icon>
      </el-tooltip>
      <el-tooltip content="大小适应" title="大小适应" placement="left">
        <el-icon @click="$_zoomReset">
          <Aim />
        </el-icon>
      </el-tooltip>
      <el-tooltip content="定位还原(大小&定位)" title="定位还原(大小&定位)" placement="left">
        <el-icon @click="$_reset">
          <Position />
        </el-icon>
      </el-tooltip>
      <el-tooltip content="上一步" title="上一步" placement="left">
        <el-icon :disabled="undoDisable" @click="$_undo">
          <DArrowLeft />
        </el-icon>
      </el-tooltip>
      <el-tooltip content="下一步" title="下一步" placement="left">
        <el-icon :disabled="redoDisable" @click="$_redo">
          <DArrowRight />
        </el-icon>
      </el-tooltip>
      <el-tooltip content="小地图" title="小地图" placement="left">
        <el-icon @click="$_showMiniMap">
          <MapLocation />
        </el-icon>
      </el-tooltip>
      <el-tooltip content="下载图片" title="下载图片" placement="left">
        <el-icon @click="$_download">
          <Download />
        </el-icon>
      </el-tooltip>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";

const props = defineProps({
  lf: Object
});

let undoDisable = ref(true);
let redoDisable = ref(true);

let $_zoomIn = () => {
  if (props.lf && props.lf.zoom) {
    props.lf.zoom(true);
  }
};

let $_zoomOut = () => {
  if (props.lf && props.lf.zoom) {
    props.lf.zoom(false);
  }
};

let $_zoomReset = () => {
  if (props.lf && props.lf.resetZoom) {
    props.lf.resetZoom();
  }
};

let $_reset = () => {
  if (props.lf && props.lf.resetZoom && props.lf.resetTranslate) {
    props.lf.resetZoom();
    props.lf.resetTranslate();
  }
};

let $_undo = () => {
  if (props.lf && props.lf.undo) {
    props.lf.undo();
  }
};

let $_redo = () => {
  if (props.lf && props.lf.redo) {
    props.lf.redo();
  }
};

let $_download = () => {
  if (props.lf && props.lf.getSnapshot) {
    props.lf.getSnapshot();
  }
};

let $_showMiniMap = () => {
  if (props.lf && props.lf.extension?.miniMap && props.lf.graphModel) {
    props.lf.extension.miniMap.show(props.lf.graphModel.width - 200, 520);
  }
};

onMounted(() => {
  if (props.lf && props.lf.on) {
    props.lf.on("history:change", ({ data: { undoAble, redoAble } }) => {
      undoDisable.value = !undoAble;
      redoDisable.value = !redoAble;
    });
  }
});
</script>

<style lang="scss" scoped>
// 自动布局按钮位置控制
.panelLayout {
  height: 130px;
  width: 50px;
  background: #f3f5f8;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  align-items: center;
  position: absolute;
  top: 6px;
  right: 100px;
  z-index: 999;
  padding: 4px 0;
  box-shadow: -3px 0 10px 1px rgb(228, 224, 219);

  i {
    cursor: pointer;
  }
}
</style>
