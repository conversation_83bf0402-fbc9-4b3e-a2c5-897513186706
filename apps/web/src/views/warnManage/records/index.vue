<template>
  <div v-loading="loading">
    <div class="flex justify-between indicator-wrapper">
      <Indicator
        :value="processedData.value"
        :unit="'已处理数量'"
        :color="processedData.color"
        class="cursor-pointer"
        @click="handleIndicatorClick(1)"
      ></Indicator>
      <Indicator
        :value="reviewedData.value"
        :unit="'已审核数量'"
        :color="reviewedData.color"
        class="cursor-pointer"
        @click="handleIndicatorClick(3)"
      ></Indicator>
      <Indicator
        :value="ignoredData.value"
        :unit="'已忽略数量'"
        :color="ignoredData.color"
        class="cursor-pointer"
        @click="handleIndicatorClick(2)"
      ></Indicator>
    </div>
  </div>
  <div class="tb-header mt-10px">
    <div>
      <el-input
        style="width: 300px"
        placeholder="请输入错误信息"
        v-model="pageParam.context"
        clearable
      ></el-input>
      <el-button type="primary" style="margin-left: 15px" :icon="Search" @click="handleSearch"
        >搜索</el-button
      >
    </div>
  </div>
  <MyTable
    :data="lists.records"
    :total="lists.total"
    v-loading="handleLoading"
    @sizeChange="handleSizechange"
    @currentChange="handleCurrentchange"
  >
    <el-table-column type="expand">
      <template #default="props">
        <div class="expand-container">
          <div class="info-card">
            <div class="info-item">
              <span class="info-label">错误信息：</span>
              <span class="info-content">
                <span style="white-space: pre-wrap; word-break: break-word">{{
                  props.row.context
                }}</span>
                <span style="margin-left: 8px">
                  <el-tag
                    :type="
                      props.row.processType === 1
                        ? 'success'
                        : props.row.processType === 2
                          ? 'info'
                          : 'primary'
                    "
                  >
                    {{
                      props.row.processType === 1
                        ? "已处理"
                        : props.row.processType === 2
                          ? "已忽略"
                          : "已审核"
                    }}
                  </el-tag></span
                ></span
              >
            </div>
            <div class="info-item">
              <span class="info-label">处理时间：</span>
              <div class="info-right">
                <span class="info-content">{{ props.row.createdAt }}</span>
              </div>
            </div>
            <div class="info-item">
              <span class="info-label">处理人：</span>
              <div class="info-right">
                <span class="info-content">{{ props.row.operatorName }}</span>
              </div>
            </div>
          </div>
          <div class="info-card" v-if="props.row.processType === 1 || props.row.processType === 3">
            <div class="info-item">
              <span class="info-label">处理方法：</span>
              <pre class="info-content info-pre">{{ props.row.solution }}</pre>
            </div>
          </div>
          <div class="info-card" v-if="props.row.advice">
            <div class="info-item">
              <span class="info-label">审核建议：</span>
              <pre class="info-content info-pre">{{ props.row.advice }}</pre>
            </div>
          </div>
        </div>
      </template>
    </el-table-column>
    <my-column property="context" label="错误信息" :show-overflow-tooltip="false">
      <template #default="scope">
        <div class="ip-cell">
          <el-tooltip placement="top" popper-class="custom-tooltip">
            <template #content>
              <div style="white-space: pre-wrap; word-break: break-word">
                {{ scope.row.context }}
              </div>
            </template>
            <span class="ip-text">{{ scope.row.context }}</span>
          </el-tooltip>
        </div>
      </template>
    </my-column>
    <my-column property="solution" label="处理方法" :show-overflow-tooltip="false">
      <template #default="scope">
        <div class="ip-cell">
          <el-tooltip placement="top" popper-class="custom-tooltip">
            <template #content>
              <div style="white-space: pre-wrap; word-break: break-word">
                {{ scope.row.solution }}
              </div>
            </template>
            <span class="ip-text">{{ scope.row.solution }}</span>
          </el-tooltip>
        </div>
      </template>
    </my-column>
    <my-column property="operatorName" label="处理人" width="150" />
    <my-column property="createdAt" label="处理时间" width="180" />
    <my-column property="processType" label="状态" width="100">
      <template #default="scope">
        <el-tag
          :type="
            scope.row.processType === 1
              ? 'success'
              : scope.row.processType === 2
                ? 'info'
                : 'primary'
          "
        >
          {{
            scope.row.processType === 1
              ? "已处理"
              : scope.row.processType === 2
                ? "已忽略"
                : "已审核"
          }}
        </el-tag>
      </template>
    </my-column>
    <my-column label="操作" align="center" header-align="center" fixed="right" width="100">
      <template #default="scope">
        <template v-if="scope.row.processType !== 2">
          <span class="operate" @click="handleEdit(scope.row)">审核</span>
          <span class="divider"> / </span>
        </template>
        <span class="operate" @click="handleOpenDeleteDialog(scope.row.id)">删除 </span>
      </template>
    </my-column>
  </MyTable>
  <el-dialog
    :align-center="true"
    v-model="dialogFormVisible"
    title="审核处理记录"
    width="750"
    :close-on-click-modal="false"
  >
    <el-form :model="state.form" label-width="auto">
      <el-form-item label="错误信息：">
        <el-input
          disabled
          type="textarea"
          v-model="state.form.context"
          :autosize="{ minRows: 1, maxRows: 3 }"
        ></el-input>
      </el-form-item>
      <el-form-item label="处理方法：">
        <el-input
          disabled
          type="textarea"
          v-model="state.form.solution"
          :autosize="{ minRows: 1, maxRows: 10 }"
        ></el-input>
      </el-form-item>
      <el-form-item label="审核建议：">
        <el-input
          v-model="state.form.advice"
          type="textarea"
          :autosize="{ minRows: 1, maxRows: 10 }"
        ></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmEdit">确定</el-button>
      </div>
    </template>
  </el-dialog>
  <el-dialog
    :align-center="true"
    v-model="deleteVisible"
    title="温馨提示"
    width="500"
    :close-on-click-modal="false"
  >
    <span>确认要删除吗？</span>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="deleteVisible = false">取消</el-button>
        <el-button type="primary" @click="handleDelete"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import {
  handleErrorList,
  getKnowledgeDetail,
  editKnowledge,
  deleteKnowledge,
  statisticsCount
} from "@/api/warnManage/error";
import { ref, reactive } from "vue";
import { Search } from "@element-plus/icons-vue";
import { useRoute } from "vue-router";
const dialogFormVisible = ref(false); //编辑对话框是否显示
const deleteVisible = ref(false); //删除对话框是否显示
const loading = ref(false);
interface IIndicatordata {
  indicator?: string;
  unit?: string;
  value?: number | string;
  color?: string;
  errorInfo?: string;
}
//编辑数据
const state = reactive({
  form: {
    context: "",
    solution: "",
    id: "",
    appid: "",
    characteristic: "",
    createdAt: "",
    operator: "",
    operatorName: "",
    updatedAt: "",
    advice: "",
    processType: ""
  }
});
const route = useRoute();
const handleLoading = ref(false);
//知识库列表参数
const pageParam = reactive({
  context: (route.query.context as string) || "",
  page: 1,
  rows: 10,
  processType: undefined as number | undefined
});
//知识库修改每页条数
const handleSizechange = (val: number) => {
  pageParam.rows = val;
  handleList();
};
//知识库分页
const handleCurrentchange = (val: number) => {
  pageParam.page = val;
  handleList();
};
const lists = reactive({
  records: [],
  total: 0
});
//知识库列表数据
function handleList() {
  handleLoading.value = true;
  pageParam.context = pageParam.context.trim();
  handleErrorList(pageParam)
    .then(response => {
      if (response.code === 0) {
        lists.records = response.records;
        lists.total = Number(response.total);
        handleLoading.value = false;
      }
    })
    .catch(error => {
      handleLoading.value = false;
      console.log(error);
    });
}
// 添加搜索按钮点击事件处理函数
const handleSearch = () => {
  pageParam.page = 1;
  handleList();
};
//获取详情
const handleEdit = async (row: any) => {
  try {
    const res = await getKnowledgeDetail(row.id);
    if (res.code === 0) {
      state.form = res.entity;
      dialogFormVisible.value = true;
    }
  } catch (error) {
    console.error("获取详情失败:", error);
  }
};
//编辑
const confirmEdit = async () => {
  try {
    const res = await editKnowledge({
      ...state.form,
      processType: 3
    });
    if (res.code === 0) {
      ElMessage.success("修改成功");
      dialogFormVisible.value = false;
      handleList();
      StatisticsCount();
    } else {
      ElMessage.error(res.desc);
    }
  } catch (error) {
    console.log(error);
  }
};
//点击删除按钮
const handleOpenDeleteDialog = (id: string) => {
  currentDeleteId.value = id;
  deleteVisible.value = true;
};
//删除id
const currentDeleteId = ref<string>("");
//删除
const handleDelete = async () => {
  try {
    const res = await deleteKnowledge(currentDeleteId.value);
    if (res.code === 0) {
      deleteVisible.value = false;
      ElMessage.success("删除成功");
      handleList();
      StatisticsCount();
    } else {
      ElMessage.error(res.desc);
    }
  } catch (error) {
    console.log(error);
  }
};
// 指标数据
const processedData = ref<IIndicatordata>({
  unit: "已处理数量",
  value: "0"
});
const reviewedData = ref<IIndicatordata>({
  unit: "已审核数量",
  value: "0"
});
const ignoredData = ref<IIndicatordata>({
  unit: "已忽略数量",
  value: "0"
});

function StatisticsCount() {
  loading.value = true;
  statisticsCount()
    .then(response => {
      if (response.code === 0) {
        processedData.value.value = response.entity.processed || "0";
        reviewedData.value.value = response.entity.reviewed || "0";
        ignoredData.value.value = response.entity.ignored || "0";
      }
    })
    .finally(() => {
      loading.value = false;
    });
}
// 新增处理指标点击的方法
const handleIndicatorClick = (type: number) => {
  pageParam.processType = type;
  pageParam.page = 1;
  handleList();
};
onMounted(() => {
  handleList();
  StatisticsCount();
});
</script>
<style lang="scss" scoped>
.indicator-wrapper > *:not(:last-child) {
  margin-right: 8px;
  box-sizing: border-box;
}
.tb-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.operate {
  color: #0064c8;
  cursor: pointer;
}
.expand-container {
  padding: 12px 24px;
  background: #f8f9fa;
}

.info-card {
  background: white;
  border-radius: 8px;
  border: 1px solid #ebeef5;
  padding: 16px;
  margin-bottom: 10px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

  &:last-child {
    margin-bottom: 0;
  }
}

.info-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;

  &:last-child {
    margin-bottom: 0;
  }
}

.info-label {
  flex: 0 0 80px;
  color: #606266;
  font-weight: 500;
  font-size: 16px;
  font-weight: bold;
}

.info-content {
  flex: 1;
  color: #303133;
  font-size: 15px;
  // line-height: 1.5;
}

.info-pre {
  white-space: pre-wrap;
  word-break: break-word;
  font-family: inherit;
  margin: 0;
}

.info-right {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.ip-cell {
  position: relative;
  width: 100%;
  overflow: hidden;
}
.ip-text {
  display: inline-block;
  width: 100%;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.custom-tooltip {
  max-width: 300px;
  white-space: pre-wrap;
  word-break: break-word;
  line-height: 1.5;
}
</style>
