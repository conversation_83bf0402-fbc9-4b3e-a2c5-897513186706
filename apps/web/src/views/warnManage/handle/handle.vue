<template>
  <div class="container">
    <div class="left-section">
      <div class="ai-analysis-section">
        <div class="section-title">
          <span class="title-text">AI 错误分析</span>
        </div>
        <div class="analysis-content">
          <div class="input-area">
            <div class="model-select">
              <el-select
                v-model="aiModel"
                placeholder="选择AI模型"
                class="model-dropdown"
                clearable
                filterable
              >
                <el-option value="deepseek" label="DeepSeek"> </el-option>
              </el-select>
            </div>
            <div class="input-section">
              <el-input
                type="textarea"
                v-model="messageValue"
                :rows="4"
                placeholder="请输入需要分析的错误信息..."
                class="message-input"
              />
              <el-button
                type="primary"
                class="analyze-btn"
                @click="handleAnalyze"
                :loading="analyzing"
              >
                <el-icon><Search /></el-icon>
                {{ analyzing ? "分析中..." : "开始分析" }}
              </el-button>
            </div>
          </div>
          <div class="result-area" v-loading="analyzing">
            <div class="result-title">
              <el-icon><Lightning /></el-icon>
              <span
                >AI 分析结果
                <el-button type="primary" style="margin-left: 20px" @click="handleDblClick"
                  >采纳</el-button
                ></span
              >
            </div>
            <div class="result-content">
              <template v-if="analysisResult">
                <div class="analysis-item">
                  <div class="item-content" @dblclick="handleDblClick">
                    {{ analysisResult.suggestion }}
                  </div>
                </div>
              </template>
              <div v-else class="empty-state">
                <el-icon><ChatDotRound /></el-icon>
                <span>请选择AI模型并输入错误信息进行分析</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="right-section">
      <div class="alert-panel">
        <div class="panel-title">
          <el-icon><Warning /></el-icon>
          <span>原始错误信息</span>
        </div>
        <div class="mb-15px">
          <el-input
            style="width: 300px"
            placeholder="请输入错误信息"
            clearable
            v-model="pageParams.message"
          ></el-input>
          <el-button type="primary" style="margin-left: 15px" :icon="Search" @click="search"
            >搜索</el-button
          >
          <el-button
            type="primary"
            style="margin-left: 15px"
            :disabled="selectedRows.length === 0"
            @click="visible = true"
            >忽略</el-button
          >
        </div>
        <MyTable
          height="300"
          :data="list.records"
          :total="list.total"
          v-loading="tableLoading"
          @selection-change="handleSelectionChange"
          @sizeChange="handleSizeChange"
          @currentChange="handleCurrentChange"
        >
          <el-table-column type="selection" width="55" />
          <MyColumn property="sourceIp" label="IP地址" />
          <my-column property="timestamp" label="时间">
            <template #default="{ row }">
              <span>{{ dayjs(row.timestamp).format("YYYY-MM-DD HH:mm:ss") }}</span>
            </template>
          </my-column>
          <my-column property="message" label="错误信息" :show-overflow-tooltip="false">
            <template #default="scope">
              <div class="ip-cell">
                <el-tooltip placement="top" popper-class="custom-tooltip">
                  <template #content>
                    <div style="white-space: pre-wrap; word-break: break-word">
                      {{ scope.row.message }}
                    </div>
                  </template>
                  <span class="ip-text">{{ scope.row.message }}</span>
                </el-tooltip>
              </div>
            </template>
          </my-column>
          <my-column
            property="status"
            label="状态"
            align="center"
            header-align="center"
            fixed="right"
            width="100"
          >
            <template #default="{ row }">
              <div>
                <el-tag
                  :type="row.status === 1 ? 'success' : row.status === 2 ? 'primary' : 'danger'"
                >
                  {{ row.status === 1 ? "已处理" : row.status === 2 ? "已忽略" : "未处理" }}
                </el-tag>
              </div>
            </template>
          </my-column>
          <my-column label="操作" align="center" header-align="center" fixed="right" width="100">
            <template #default="{ row }">
              <template v-if="row.status === 1 || row.status === 2">
                <span class="operate" @click="handleHistoryClick(row)">查看</span>
              </template>
              <template v-else>
                <span class="operate" @click="handleAIAnalysisClick(row)">AI分析</span>
              </template>
            </template>
          </my-column>
        </MyTable>
        <div class="submit-section">
          <div class="title">
            <div class="mark"></div>
            <div style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis">
              {{ title }}
            </div>
          </div>
          <el-form>
            <el-form-item label="处理方法：">
              <el-input
                type="textarea"
                v-model="form.submitContent"
                :rows="6"
                placeholder="请输入处理方法..."
                class="submit-input"
              />
            </el-form-item>
          </el-form>
          <el-button type="primary" class="submit-btn" @click="handleSubmit">提交</el-button>
        </div>
      </div>
    </div>
  </div>
  <el-dialog
    :align-center="true"
    v-model="visible"
    title="温馨提示"
    width="500"
    :close-on-click-modal="false"
  >
    <span>确认要忽略所选数据吗？</span>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" @click="handleIgnoreConfirm"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import { deepseekChat, errorSava, getLogList, errorIgnore } from "@/api/warnManage/error";
import { ref, reactive } from "vue";
import { Search, Warning, Lightning, ChatDotRound } from "@element-plus/icons-vue";
import { applicationStore } from "@/store/modules/application";
import { useRouter } from "vue-router";
const useApplicationStore = applicationStore();
const router = useRouter();
const title = ref("");
const form = reactive({
  submitContent: "",
  resolve: "",
  resolver: "",
  noticeWayIdList: []
});
interface AnalysisResult {
  cause: string;
  impact: string;
  suggestion: string;
}
const visible = ref(false);
const selectedRows = ref<any[]>([]);
const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection;
};
const handleIgnoreConfirm = async () => {
  try {
    if (selectedRows.value.length === 0) return;

    const params = {
      contentList: selectedRows.value.map(row => row.message)
    };

    const res = await errorIgnore(params);
    if (res.code === 0) {
      ElMessage.success("忽略成功");
      visible.value = false;
      loadData(); // 刷新数据
    } else {
      ElMessage.error(res.desc);
    }
  } catch (error) {
    console.error(error);
  }
};
const messageValue = ref("");
const aiModel = ref("deepseek");
const tableLoading = ref(false);
const analyzing = ref(false); //分析按钮loading
const analysisResult = ref<AnalysisResult | null>(null);
//调用AI分析
const handleAnalyze = async () => {
  if (!aiModel.value || !messageValue.value) {
    ElMessage.warning("请选择AI模型并输入错误信息");
    return;
  }

  analyzing.value = true;
  try {
    const res = await deepseekChat({
      model: "deepseek-chat",
      messages: [
        {
          role: "user",
          content: messageValue.value
        },
        {
          role: "system",
          content: "请分析以下错误日志，指出引发错误的原因并提供解决方案，使用简洁的技术语言"
        }
      ]
    });

    if (res.code === 0 && res.entity?.choices?.[0]?.message?.content) {
      analysisResult.value = {
        cause: "",
        impact: "",
        suggestion: res.entity.choices[0].message.content
      };
    } else {
      ElMessage.error("分析失败，请重试");
    }
  } catch (error) {
    console.error(error);
    ElMessage.error("分析失败，请重试");
  } finally {
    analyzing.value = false;
  }
};
//搜索
function search() {
  pageParams.page = 1;
  loadData();
}
//原始信息列表参数
const pageParams = reactive({
  message: "",
  appid: "",
  severityText: "ERROR",
  page: 1,
  rows: 10
});
//原始信息修改每页条数
const handleSizeChange = (val: number) => {
  pageParams.rows = val;
  loadData();
};
//原始信息分页
const handleCurrentChange = (val: number) => {
  pageParams.page = val;
  loadData();
};
const list = reactive({
  records: [],
  total: 0
});
//原始信息列表数据
function loadData() {
  tableLoading.value = true;
  pageParams.message = pageParams.message.trim();
  pageParams.appid = useApplicationStore.appId;
  getLogList(pageParams)
    .then(response => {
      if (response.code === 0) {
        list.records = response.records;
        list.total = Number(response.total);
        tableLoading.value = false;
      }
    })
    .catch(error => {
      tableLoading.value = false;
      console.log(error);
    });
}

const handleAIAnalysisClick = (row: any) => {
  messageValue.value = row.message;
  title.value = row.message;
};

const handleHistoryClick = (row: any) => {
  router.push({
    path: "/logMonitoring/records",
    query: {
      context: row.message
    }
  });
};

const handleDblClick = () => {
  if (analysisResult.value) {
    form.submitContent = analysisResult.value.suggestion;
  }
};

const handleSubmit = () => {
  if (!form.submitContent) {
    ElMessage.warning("请输入处理方法");
    return;
  }

  const params = {
    context: messageValue.value,
    solution: form.submitContent
  };

  errorSava(params)
    .then(response => {
      if (response.code === 0) {
        ElMessage.success("提交成功");
        form.submitContent = "";
        loadData(); // 刷新列表数据
      } else {
        ElMessage.error(response.message || "提交失败");
      }
    })
    .catch(error => {
      console.error(error);
      ElMessage.error("提交失败，请重试");
    });
};
onMounted(() => {
  loadData();
});
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  width: 100%;
  max-width: 100% !important;
  height: 100%;
  gap: 20px;
  box-sizing: border-box;

  .left-section {
    width: 30%;
    height: 100%;

    .ai-analysis-section {
      height: 100%;
      border-radius: 12px;
      padding: 20px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
      display: flex;
      flex-direction: column;
      gap: 16px;

      .section-title {
        .title-text {
          font-size: 18px;
          font-weight: 600;
          color: #1f2937;
        }
      }

      .analysis-content {
        flex: 1;
        height: calc(100% - 20px);
        display: flex;
        flex-direction: column;
        gap: 16px;

        .input-area {
          display: flex;
          flex-direction: column;
          gap: 12px;

          .model-select {
            .model-dropdown {
              width: 100%;
            }
          }

          .input-section {
            display: flex;
            flex-direction: column;
            gap: 12px;

            .message-input {
              flex: 1;
            }

            .analyze-btn {
              height: 36px;
              font-size: 14px;
              display: flex;
              align-items: center;
              justify-content: center;
              gap: 8px;
            }
          }
        }

        .result-area {
          height: calc(100% - 200px);
          flex: 1;
          display: flex;
          flex-direction: column;
          gap: 12px;
          padding: 16px;
          background-color: #f9fafb;
          border-radius: 8px;

          .result-title {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
          }

          .result-content {
            flex: 1;
            overflow-y: auto;
            word-break: break-all;

            .analysis-item {
              margin-bottom: 12px;

              .item-content {
                color: #1f2937;
                line-height: 1.5;
                white-space: pre-line;
                cursor: pointer;
              }
            }

            .empty-state {
              height: 100%;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              gap: 12px;
              color: #9ca3af;
              font-size: 14px;

              .el-icon {
                font-size: 28px;
              }
            }
          }
        }
      }
    }
  }

  .right-section {
    width: 70%;
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 20px;

    .alert-panel,
    .history-panel {
      height: calc(50% - 10px);
      flex: 1;
      border-radius: 12px;
      padding: 20px;
      box-sizing: border-box;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
      .panel-title {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 16px;
        font-size: 16px;
        font-weight: 600;
        color: #1f2937;
      }
    }
  }
}

.operate {
  cursor: pointer;
  color: #0064c8;
  vertical-align: middle;
}

.submit-section {
  margin-top: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;

  .submit-input {
    width: 100%;
  }

  .submit-btn {
    align-self: flex-end;
  }
}
.ip-cell {
  position: relative;
  width: 100%;
  overflow: hidden;
}
.ip-text {
  display: inline-block;
  width: 100%;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.custom-tooltip {
  max-width: 300px;
  white-space: pre-wrap;
  word-break: break-word;
  line-height: 1.5;
}
.title {
  font-size: 16px;
  font-weight: 550;
  padding-bottom: 16px;
  display: flex;
  align-items: center;
}
.mark {
  margin-right: 10px;
  width: 4px;
  height: 15px;
  background: #0064c8;
}
</style>
