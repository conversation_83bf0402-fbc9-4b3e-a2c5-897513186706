<template>
  <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
    <el-tab-pane label="邮件设置" name="email">
      <Emmail v-if="activeName === 'email'"></Emmail>
    </el-tab-pane>
    <el-tab-pane label="短信设置" name="message">
      <textMessage v-if="activeName === 'message'"></textMessage>
    </el-tab-pane>
  </el-tabs>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import { useRoute, useRouter } from "vue-router";
import Emmail from "./components/Email.vue";
import textMessage from "./components/textMessage.vue";
import type { TabsPaneContext } from "element-plus";

const route = useRoute();
const router = useRouter();

const activeName = ref(route.query.tab ? String(route.query.tab) : "email");

watch(
  () => route.query.tab,
  newTab => {
    if (newTab) {
      activeName.value = String(newTab);
    }
  }
);

watch(activeName, newActiveName => {
  router.replace({ query: { tab: newActiveName } });
});

const handleClick = (tab: TabsPaneContext) => {
  console.log(tab);
};
</script>

<style lang="scss" scoped>
.demo-tabs > .el-tabs__content {
  padding: 32px;
  color: #6b778c;
  font-size: 32px;
  font-weight: 600;
}
</style>
