<template>
  <TitlecCom :title="`告警邮箱设置`" />
  <div>
    <el-form
      :model="form"
      label-width="auto"
      style="max-width: 420px"
      class="demo-ruleForm"
      v-loading="loading"
    >
      <el-form-item label="邮箱账号：" prop="" required>
        <el-input
          v-model="form.account"
          style="width: 300px"
          placeholder="请输入邮箱账号"
          clearable
        />
      </el-form-item>
      <el-form-item label="SMTP密码：" prop="" required>
        <el-input
          v-model="form.password"
          style="width: 300px"
          placeholder="请输入SMTP密码"
          clearable
        />
      </el-form-item>
      <el-form-item label="SMTP服务器：" prop="" required>
        <el-input
          v-model="form.host"
          style="width: 300px"
          placeholder="请输入SMTP服务器"
          clearable
        />
      </el-form-item>
      <el-form-item label="端口：" prop="" required>
        <el-input v-model="form.port" style="width: 300px" placeholder="请输入端口" clearable />
      </el-form-item>
      <el-form-item label="测试发送：" prop="">
        <el-input
          v-model="form.testEmail"
          style="width: 200px"
          placeholder="请输入测试发送"
          clearable
        />
        <el-button style="margin-left: 10px" @click="checkTestEmail">测试发送</el-button>
      </el-form-item>
      <div style="text-align: center; margin-right: 95px">
        <el-button type="primary" @click="saveEmailSettings">保存设置</el-button>
      </div>
    </el-form>
    <el-dialog
      :close-on-click-modal="false"
      :align-center="true"
      title="温馨提示"
      v-model="confirmDialogVisible"
      width="30%"
      @confirm="testSendEmail"
      @cancel="handleCancel"
    >
      <span>确定要发送邮件吗</span>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCancel">取消</el-button>
          <el-button type="primary" @click="testSendEmail" :loading="confirmLoading"
            >确定</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import TitlecCom from "@/components/TitleCom/index.vue";
import { getEmailSetting, savaEmailSetting, emailTest } from "@/api/warnManage/method";
import { applicationStore } from "@/store/modules/application";
const loading = ref(false);
const useApplicationStore = applicationStore();
const confirmDialogVisible = ref(false);
const confirmLoading = ref(false);
const form = reactive({
  account: "",
  password: "",
  host: "",
  port: "",
  testEmail: ""
});
const appId = useApplicationStore.appId;
//加载邮箱配置
function emailSetting() {
  loading.value = true;
  getEmailSetting({ appid: appId })
    .then(response => {
      if (response.code === 0) {
        form.account = response.entity?.account;
        form.password = response.entity?.password;
        form.host = response.entity?.host;
        form.port = response.entity?.port;
        loading.value = false;
      }
    })
    .catch(error => {
      loading.value = false;
      console.log(error);
    });
}
//保存邮箱设置
function saveEmailSettings() {
  loading.value = true;
  const dataToSend = {
    appid: appId,
    account: form.account,
    password: form.password,
    host: form.host,
    port: form.port
  };
  savaEmailSetting(dataToSend)
    .then(response => {
      if (response.code === 0) {
        ElMessage.success("保存成功");
        loading.value = false;
      } else {
        ElMessage.error(response.desc);
        loading.value = false;
      }
    })
    .catch(error => {
      console.log(error);
      loading.value = false;
    });
}
// 检查测试邮件地址是否为空
function checkTestEmail() {
  if (!form.testEmail) {
    ElMessage.error("请先填写测试邮件地址");
  } else {
    confirmDialogVisible.value = true;
  }
}
// 处理确认框的取消事件
function handleCancel() {
  confirmDialogVisible.value = false;
}
//测试邮箱发送
function testSendEmail() {
  confirmLoading.value = true;
  loading.value = true;
  const testData = {
    appid: appId,
    account: form.account,
    password: form.password,
    host: form.host,
    port: form.port,
    testEmail: form.testEmail
  };
  emailTest(testData)
    .then(response => {
      if (response.code === 0) {
        ElMessage.success("邮件发送成功，请确认是否收到");
        loading.value = false;
        confirmLoading.value = false;
        confirmDialogVisible.value = false;
      } else {
        ElMessage.error(response.desc);
        loading.value = false;
        confirmLoading.value = false;
        confirmDialogVisible.value = false;
      }
    })
    .catch(error => {
      loading.value = false;
      console.log(error);
      confirmLoading.value = false;
      confirmDialogVisible.value = false;
    });
}
onMounted(() => {
  emailSetting();
});
</script>

<style lang="scss" scoped></style>
