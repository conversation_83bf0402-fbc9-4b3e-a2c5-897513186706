<template>
  <TitlecCom :title="`告警短信设置`" />

  <div>
    <el-form
      style="max-width: 500px"
      :model="numberValidateForm"
      label-width="auto"
      v-loading="loading"
    >
      <el-form-item label="请求方法：" prop="" required>
        <el-select
          v-model="numberValidateForm.method"
          placeholder="请选择请求方法"
          style="width: 150px"
        >
          <el-option
            v-for="item in requestMethods"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="编码方式：" prop="" required>
        <el-select
          v-model="numberValidateForm.charset"
          placeholder="请选择编码方式"
          style="width: 150px"
        >
          <el-option
            v-for="item in encodingTypes"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="URL地址：" prop="" required>
        <el-input
          v-model="numberValidateForm.url"
          placeholder="请输入URL地址"
          type="text"
          style="width: 350px"
        />
      </el-form-item>

      <el-form-item label="Content-Type：" prop="contentType">
        <el-input
          v-model="numberValidateForm.contentType"
          placeholder="请输入Content-Type"
          type="text"
          style="width: 350px"
        />
      </el-form-item>

      <el-form-item label="超时时间（秒）：" prop="" required>
        <el-input
          v-model.number="numberValidateForm.timeout"
          placeholder="请输入超时时间"
          type="number"
          style="width: 350px"
        />
      </el-form-item>
      <el-form-item label="请求模板：" prop="" required>
        <el-input
          v-model="numberValidateForm.template"
          type="textarea"
          placeholder="请输入请求模板"
          style="width: 350px"
          :autosize="{ minRows: 5, maxRows: 10 }"
        />
        <div style="color: #666666">$mobile：手机号码、$content：短信内容</div>
      </el-form-item>
      <el-form-item label="测试发送：" prop="">
        <el-input
          v-model="numberValidateForm.testMobile"
          style="width: 250px"
          placeholder="请输入测试发送"
          clearable
        />
        <el-button style="margin-left: 10px" @click="checkTestSms">测试发送</el-button>
        <div class="message-container" v-if="showErrorMessage">{{ errorMessage }}</div>
      </el-form-item>
      <div style="text-align: center; margin-right: 145px">
        <el-button type="primary" @click="submitMessage">保存设置</el-button>
      </div>
    </el-form>
    <el-dialog
      :align-center="true"
      title="温馨提示"
      v-model="confirmDialogVisible"
      width="20%"
      @confirm="sendingMessage"
    >
      <span>确定要发送短信吗</span>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="confirmDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="sendingMessage">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import TitlecCom from "@/components/TitleCom/index.vue";
import { reactive, ref, onMounted } from "vue";
import { getSmsSetting, saveSmsSetting, sendingSmsSetting } from "@/api/warnManage/message";
import { applicationStore } from "@/store/modules/application";
const useApplicationStore = applicationStore();
const appId = useApplicationStore.appId;

const loading = ref(false);
const confirmDialogVisible = ref(false);
// 表单数据
const numberValidateForm = reactive({
  method: "",
  charset: "",
  url: "",
  contentType: "",
  timeout: null,
  testMobile: "",
  template: ""
});

// 请求方法选项
const requestMethods = ref([
  { value: "GET", label: "GET" },
  { value: "POST", label: "POST" }
]);

// 编码方式选项
const encodingTypes = ref([
  { value: "UTF-8", label: "UTF-8" },
  { value: "GBK", label: "GBK" }
]);

// 获取短信设置
const getMessage = async () => {
  loading.value = true;
  try {
    const res = await getSmsSetting({ appid: appId });
    if (res.code === 0) {
      numberValidateForm.method = res.entity.method;
      numberValidateForm.charset = res.entity.charset;
      numberValidateForm.url = res.entity.url;
      numberValidateForm.contentType = res.entity.contentType;
      numberValidateForm.timeout = res.entity.timeout;
      numberValidateForm.template = res.entity.template;
    }
    loading.value = false;
  } catch (err) {
    loading.value = false;
    console.error(err);
  }
};

// 保存短信设置
const submitMessage = async () => {
  loading.value = true;
  try {
    const res = await saveSmsSetting({ ...numberValidateForm, appid: appId });
    if (res.code === 0) {
      ElMessage.success("保存成功");
    } else {
      ElMessage.error(res.desc);
    }
    loading.value = false;
  } catch (err) {
    loading.value = false;
    console.error(err);
  }
};

// 发送短信测试
function checkTestSms() {
  if (!numberValidateForm.testMobile) {
    ElMessage.error("请先填写测试短信号码");
  } else {
    confirmDialogVisible.value = true;
  }
}
const errorMessage = ref("");
const showErrorMessage = ref(false);
const sendingMessage = async () => {
  loading.value = true;
  try {
    const res = await sendingSmsSetting({ ...numberValidateForm, appid: appId });
    if (res.code === 0) {
      ElMessage.success("短信发送成功，请确认是否收到");
      confirmDialogVisible.value = false;
    }
    loading.value = false;
  } catch (err) {
    showErrorMessage.value = true;
    errorMessage.value = err;
    loading.value = false;
    confirmDialogVisible.value = false;
    console.log(err);
  }
};
onMounted(() => {
  getMessage();
});
</script>

<style lang="scss" scoped>
.message-container {
  position: relative;
  color: #f56c6c;
  min-width: 600px;
  z-index: 9;
  margin-top: 5px;
}
</style>
