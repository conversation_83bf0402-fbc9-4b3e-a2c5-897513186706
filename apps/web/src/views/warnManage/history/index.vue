<template>
  <div class="actions">
    <!-- <el-button type="primary" @click="selectTableList(0)">选中已处理</el-button>
    <el-button type="primary" @click="selectTableList(1)">选中未处理</el-button> -->
    <el-button type="danger" @click="delTableList(2)">删除告警</el-button>
    <el-button type="danger" @click="delTableList(3)">一键清空</el-button>
  </div>
  <el-table
    ref="multipleTableRef"
    :data="summaryCount"
    style="width: 100%"
    :header-cell-style="{ background: '#f9f9f9', color: '#333', fontWeight: 'normal' }"
    @selection-change="handleSelectionChange"
    v-loading="tabelListLoading"
  >
    <my-column type="selection" width="55" />
    <my-column property="moduleLabel" label="告警模块" />
    <my-column property="value" label="告警级别" align="center" header-align="center">
      <template v-slot="{ row }">
        <el-tag :type="row.level === 0 ? 'danger' : row.level === 1 ? 'warning' : 'success'">
          {{ row.level === 0 ? "紧急告警" : row.level === 1 ? "严重告警" : "一般告警" }}
        </el-tag>
      </template>
    </my-column>
    <my-column property="content" label="告警内容">
      <template v-slot="{ row }">
        <span>{{ row.content }}</span>
      </template>
    </my-column>
    <my-column property="status" label="状态" align="center" header-align="center">
      <template v-slot="{ row }">
        <el-tag effect="dark" :type="row.status === 1 ? 'danger' : 'success'">
          {{ row.status === 1 ? "未处理" : "已处理" }}
        </el-tag>
      </template>
    </my-column>
    <my-column property="createdAt" label="告警时间" />
    <my-column label="操作" align="center" header-align="center" fixed="right" width="180">
      <template v-slot="{ row }">
        <span v-if="row.status === 1" class="operate" @click="operate(1, row)">已处理</span>
        <span v-else class="operate" @click="operate(1, row)">未处理</span>
        <span class="divider"> / </span>
        <span class="operate" @click="operate(2, row)">删除</span>
      </template>
    </my-column>
  </el-table>
  <div class="pagination">
    <el-pagination
      v-model:current-page="state.page"
      v-model:page-size="state.rows"
      :page-sizes="[10, 20, 30]"
      :total="total"
      layout="total, sizes, prev, pager, next, jumper"
      @current-change="currentChange"
      @size-change="sizeChange"
    />
  </div>
  <!-- 删除弹窗-->
  <el-dialog
    v-model="deleteVisible"
    :title="delTitle"
    width="500"
    :close-on-click-modal="false"
    :align-center="true"
  >
    <span>{{ delMessage }}</span>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="deleteVisible = false">取消</el-button>
        <el-button v-if="delValue === 3" type="primary" @click="delCommits"> 确定 </el-button>
        <el-button v-else type="primary" @click="delCommit"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
  <!-- 修改处理状态弹窗-->
  <el-dialog
    v-model="statusVisible"
    title="确认状态"
    width="500"
    :close-on-click-modal="false"
    :align-center="true"
  >
    <span>{{ statusMessage }}</span>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="statusVisible = false">取消</el-button>
        <el-button type="primary" @click="statusCommit"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { getHistory, updateStatus, deleteAll, deleteBatch } from "@/api/warnManage/history";
import { applicationStore } from "@/store/modules/application";
const useApplicationStore = applicationStore();
const tabelListLoading = ref(false);
const deleteVisible = ref(false);
const statusVisible = ref(false);
const multipleSelection = ref([]);
const statusMessage = ref("");
const state = reactive({
  appid: useApplicationStore.appId,
  // appid: 123456,
  page: 1,
  rows: 10
});
const total = ref(0);
const summaryCount = ref([]);
const currentChange = (val: number) => {
  state.page = val;
  loadData();
};
const sizeChange = (val: number) => {
  state.rows = val;
  loadData();
};
const handleSelectionChange = val => {
  multipleSelection.value = val;
};
const delTitle = ref("");
const delMessage = ref("");
const delValue = ref();
const delTableList = (val: number) => {
  if (multipleSelection.value.length === 0 && val === 2) {
    ElMessage.error("请先选择需要删除的告警项！");
    return;
  }
  if (summaryCount.value?.length === 0) {
    return;
  }
  delTitle.value = val === 3 ? "确认清空" : "确认删除";
  delMessage.value = val === 3 ? "是否确认清空" : "是否确认删除";
  delValue.value = val;
  deleteVisible.value = true;
};

const multipleTableRef = ref(null);
// const selectTableList = (status: number) => {
//   if (!summaryCount.value || summaryCount.value.length === 0) {
//     return;
//   }

//   const selectedRows = summaryCount.value.filter(item => item.status === status);

//   toggleSelection(selectedRows);
// };
// const toggleSelection = (rows: Array<string>, showSelected?: boolean) => {
//   if (rows) {
//     rows.forEach(row => {
//       multipleTableRef.value!.toggleRowSelection(row, showSelected);
//     });
//   }
// };

const listValue = ref();
const operate = (val, row) => {
  if (val === 1) {
    statusVisible.value = true;
    statusMessage.value = `是否确认修改为 “${row.status === 1 ? "已处理" : "未处理"}” 状态？`;
    listValue.value = row;
  } else if (val === 2) {
    summaryCount.value = summaryCount.value.filter(item => item.id !== row.id);
  }
};

// 修改处理状态
const statusCommit = async () => {
  try {
    const res = await updateStatus(listValue.value);
    if (res.code === 0) {
      ElMessage.success("修改成功");
      loadData();
      statusVisible.value = false;
    } else {
      ElMessage.error("修改失败");
    }
  } catch (err) {
    ElMessage.error("修改失败");
    console.error(err);
  }
};

// 清空列表
const delCommits = async () => {
  try {
    const res = await deleteAll();
    if (res.code === 0) {
      ElMessage.success("清空成功");
      loadData();
      deleteVisible.value = false;
    } else {
      ElMessage.error("清空失败");
    }
  } catch (err) {
    deleteVisible.value = false;
    console.error(err);
  }
};

// 删除选中
const delCommit = async () => {
  const ids = multipleSelection.value.map(item => {
    return item.id;
  });
  try {
    const res = await deleteBatch(ids);
    if (res.code === 0) {
      ElMessage.success("删除成功");
      loadData();
      deleteVisible.value = false;
    } else {
      ElMessage.error("删除失败");
    }
  } catch (err) {
    ElMessage.error("删除失败");
    deleteVisible.value = false;
    console.error(err);
  }
};
const loadData = async () => {
  try {
    tabelListLoading.value = true;
    const res = await getHistory(state);
    if (res.code === 0) {
      summaryCount.value = res.records;
      total.value = Number(res.total);
    }
    tabelListLoading.value = false;
  } catch (err) {
    tabelListLoading.value = false;
    console.error(err);
  }
};
onMounted(() => {
  loadData();
});
</script>

<style lang="scss" scoped>
.actions {
  margin-bottom: 15px;
  display: flex;
  justify-content: end;
  gap: 10px;
}
.operate {
  color: #0064c8;
  cursor: pointer;
}
.pagination {
  margin-top: 20px;
  margin-right: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
