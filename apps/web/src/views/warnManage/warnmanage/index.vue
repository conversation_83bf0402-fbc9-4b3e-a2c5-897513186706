<template>
  <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
    <el-tab-pane label="告警策略" name="strategy">
      <strategy v-if="activeName === 'strategy'"></strategy>
    </el-tab-pane>
    <el-tab-pane label="通知组" name="notifierGroup">
      <notificationGroup v-if="activeName === 'notifierGroup'"></notificationGroup>
    </el-tab-pane>
    <el-tab-pane label="通知对象" name="notifier">
      <notificationObject v-if="activeName === 'notifier'"></notificationObject>
    </el-tab-pane>
    <!-- <el-tab-pane label="邮件设置" name="email">
      <Emmail v-if="activeName === 'email'"></Emmail>
    </el-tab-pane>
    <el-tab-pane label="短信设置" name="message">
      <textMessage v-if="activeName === 'message'"></textMessage>
    </el-tab-pane> -->
    <el-tab-pane label="告警算法" name="algorithm">
      <algorithm v-if="activeName === 'algorithm'"></algorithm>
    </el-tab-pane>
    <el-tab-pane label="告警记录" name="note">
      <Note v-if="activeName === 'note'"></Note>
    </el-tab-pane>
    <!-- <el-tab-pane label="工作流列表" name="workflow">
      <workflow v-if="activeName === 'workflow'"></workflow>
    </el-tab-pane>
    <el-tab-pane label="创建工作流" name="analysis">
      <Analysis v-if="activeName === 'analysis'"></Analysis>
    </el-tab-pane>
    <el-tab-pane label="prompt分组" name="prompt">
      <prompt v-if="activeName === 'prompt'"></prompt>
    </el-tab-pane>
    <el-tab-pane label="prompt模版" name="module">
      <module v-if="activeName === 'module'"></module>
    </el-tab-pane> -->
  </el-tabs>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import { useRoute, useRouter } from "vue-router";
import strategy from "../strategy/index.vue";
import notificationGroup from "../notification/components/notificationGroup.vue";
import notificationObject from "../notification/components/notificationObject.vue";
import Emmail from "../method/components/Email.vue";
import textMessage from "../method/components/textMessage.vue";
import algorithm from "../test/index.vue";
import Note from "../note/index.vue";
import Analysis from "../analysis/index.vue";
import prompt from "../prompt/index.vue";
import workflow from "../workflow/index.vue";
import module from "../module/index.vue";
import type { TabsPaneContext } from "element-plus";

const route = useRoute();
const router = useRouter();

const activeName = ref(route.query.tab ? String(route.query.tab) : "strategy");

watch(
  () => route.query.tab,
  newTab => {
    if (newTab) {
      activeName.value = String(newTab);
    }
  }
);

watch(activeName, newActiveName => {
  router.replace({ query: { tab: newActiveName } });
});

const handleClick = (tab: TabsPaneContext) => {
  console.log(tab);
};
</script>

<style lang="scss" scoped>
.demo-tabs > .el-tabs__content {
  padding: 32px;
  color: #6b778c;
  font-size: 32px;
  font-weight: 600;
}
</style>
