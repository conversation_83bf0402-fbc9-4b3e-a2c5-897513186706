<template>
  <div>
    <MyTable
      :data="systemList"
      :total="total"
      v-loading="tableLoading"
      @sizeChange="handleSizeChange"
      @currentChange="handleCurrentChange"
    >
      <my-column property="name" label="名称" />
      <my-column property="content" label="内容" />
      <my-column property="version" label="版本" />
      <my-column property="scope" label="作用域" />
      <my-column property="owner_id" label="所属ID" />
      <my-column property="is_active" label="是否激活">
        <template #default="{ row }">
          <el-tag :type="row.is_active !== true ? 'danger' : 'success'">
            {{ row.is_active == true ? "是" : "否" }}
          </el-tag>
        </template>
      </my-column>
      <my-column property="created_at" label="创建时间">
        <template #default="{ row }">
          <span>{{ formatTime(row.created_at) }}</span>
        </template>
      </my-column>
      <my-column property="updated_at" label="更新时间">
        <template #default="{ row }">
          <span>{{ formatTime(row.updated_at) }}</span>
        </template>
      </my-column>
    </MyTable>
  </div>
</template>
<script setup lang="ts">
import { getSystemPromptTemplate } from "@/api/workflow/index";
import { formatTime } from "@/utils/dateStr";
const tableLoading = ref(false);
const systemList = ref([]);
const total = ref(0);
const pageParams = reactive({
  page: 1,
  page_size: 10
});
// 分页处理
function handleCurrentChange(page: number) {
  pageParams.page = page;
  loadData();
}

function handleSizeChange(size: number) {
  pageParams.page_size = size;
  loadData();
}
function loadData() {
  tableLoading.value = true;
  getSystemPromptTemplate(pageParams)
    .then(response => {
      if (response && response.data) {
        systemList.value = response.data || [];
        total.value = response.data.total || response.data.length || 0;
        tableLoading.value = false;
      }
    })
    .catch(error => {
      tableLoading.value = false;
      console.log(error);
    });
}
onMounted(() => {
  loadData();
});
</script>
