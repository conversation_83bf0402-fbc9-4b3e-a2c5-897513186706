<template>
  <div class="log-container">
    <div class="search-container" :style="{ height: serviceShow ? '750px' : '680px' }">
      <el-form :model="filterForm">
        <el-form-item class="input-group" v-if="serviceShow">
          <label for="serviceName">服务名称：</label>
          <el-select
            id="serviceName"
            placeholder="请选择服务名称"
            v-model="filterForm.serviceName"
            filterable
            clearable
          >
            <el-option
              v-for="item in serviceNameOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item class="input-group">
          <label for="traceId">Trace ID：</label>
          <el-input
            id="traceId"
            v-model="filterForm.traceId"
            clearable
            placeholder="请输入Trace ID"
          />
        </el-form-item>
        <el-form-item class="input-group">
          <label for="ip">IP地址：</label>
          <el-input id="ip" v-model="filterForm.ip" clearable placeholder="***********" />
        </el-form-item>
        <el-form-item class="input-group">
          <label for="instanceId">实例ID：</label>
          <el-input
            id="instanceId"
            v-model="filterForm.instanceId"
            clearable
            placeholder="请输入实例ID"
          />
        </el-form-item>
        <el-form-item class="input-group">
          <label for="status">状态：</label>
          <el-select
            id="status"
            placeholder="请选择状态"
            v-model="filterForm.status"
            clearable
            filterable
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item class="input-group">
          <label for="traceType">调用链类型：</label>
          <el-select
            id="traceType"
            placeholder="请选择调用链类型"
            v-model="filterForm.traceType"
            clearable
            filterable
          >
            <el-option
              v-for="item in traceTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item class="input-group">
          <label for="endpoint">接口名称：</label>
          <el-input
            id="endpoint"
            v-model="filterForm.endpoint"
            clearable
            placeholder="请输入接口名称"
          />
        </el-form-item>
        <el-form-item class="input-group" style="display: flex; align-items: center">
          <label style="margin-right: 10px; white-space: nowrap">耗时（ms）：</label>
          <div style="display: flex; align-items: center">
            <el-input
              id="durationMin"
              v-model="filterForm.durationMin"
              clearable
              placeholder="最小值"
              style="width: 100px; margin-right: 5px"
            />
            ~
            <el-input
              id="durationMax"
              v-model="filterForm.durationMax"
              clearable
              placeholder="最大值"
              style="width: 100px; margin-left: 5px"
            />
          </div>
        </el-form-item>
        <el-form-item class="input-group">
          <label for="sql">SQL / NOSQL：</label>
          <el-input id="sql" v-model="filterForm.sql" clearable placeholder="请输入SQL / NOSQL" />
        </el-form-item>
        <el-form-item style="display: flex; margin-top: 20px">
          <el-popconfirm
            title="确定清空吗？"
            @confirm="resetSearch"
            confirm-button-text="确定"
            cancel-button-text="取消"
            icon="el-icon-warning"
            :hide-after="0"
          >
            <template #reference>
              <el-button type="danger" plain style="flex: 1; margin-right: 10px"> 清空 </el-button>
            </template>
          </el-popconfirm>
          <el-button
            type="primary"
            :disabled="isLoading"
            @click="executeSearch"
            :icon="Search"
            style="flex: 1"
          >
            查询
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <MyTable
      :data="callChain"
      :total="totalRecords"
      :pagination-background="true"
      class="tabel-container"
      pagination-layout="total,prev, next,"
      :default-sort="{ prop: 'durationMs && startTime', order: 'descending' }"
      @sort-change="handleSortChange"
      v-loading="isLoading"
      @sizeChange="onPageSizeChange"
      @currentChange="onCurrentPageChange"
    >
      <MyColumn property="traceId" label="Trace ID" width="310">
        <template #default="{ row }">
          <span :class="['vertical-line', row.result === 1 ? 'error-bg' : 'success-bg']"></span>
          <span class="service-name" @click="showType(row)">{{ row.traceId }}</span>
        </template>
      </MyColumn>
      <MyColumn property="serviceName" label="服务名称" width="200">
        <template #default="scope">
          <span>
            {{ scope.row.serviceName }} {{ scope.row.alias ? ` （${scope.row.alias}）` : "" }}
          </span>
        </template>
      </MyColumn>
      <MyColumn property="name" label="接口名称" />
      <MyColumn property="durationMs" label="耗时" width="120" sortable="custom">
        <template #default="{ row }">
          {{ row.durationMs + "ms" }}
        </template>
      </MyColumn>
      <MyColumn label="状态" width="120" header-align="center">
        <template #default="{ row }">
          <div style="text-align: center">
            <el-tag :type="row.result === 0 ? 'success' : 'danger'">
              {{ row.result === 0 ? "成功" : "失败" }}
            </el-tag>
          </div>
        </template>
      </MyColumn>
      <MyColumn property="startTime" label="开始时间" width="180" sortable="custom" />
      <MyColumn label="操作" width="100" fixed="right" align="center" header-align="center">
        <template #default="{ row }">
          <span class="action-link" @click="showType(row)">详情</span>
        </template>
      </MyColumn>
    </MyTable>
  </div>
  <div>
    <el-drawer
      v-model="logVisible"
      :title="`调用链详情【${titleTraceId}】`"
      size="80%"
      :before-close="handleDrawerClose"
    >
      <div class="trace-panel" v-loading="drawerLoading">
        <div class="span-panel" style="width: 750px">
          <table border>
            <thead>
              <tr>
                <td width="400">跨度（span）名称</td>
                <td width="200">请求类型</td>
                <td width="300">请求时间</td>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="(item, index) in paginatedCallTypeList"
                :key="index"
                @click="handleCallTypeClick(item)"
                style="cursor: pointer"
                :class="{
                  'selected-row': item.spanId === selectedSpanId,
                  noHover: item.spanId === selectedSpanId
                }"
              >
                <td :style="{ paddingLeft: item.durationPadding + 'px' }" title="item.spanName">
                  <span class="name" :title="item.spanName"
                    ><span :class="['bar', item.result === 0 ? 'success' : 'error']"></span
                    >{{ item.spanName }}</span
                  >
                </td>
                <td>
                  <span class="tbName">{{ item.requestType }}</span>
                </td>
                <td>
                  <span
                    :class="['duration', item.result === 0 ? 'success' : 'error']"
                    :style="{ width: `calc(${item.durationWidth}  - 10px)` }"
                  >
                    {{ item.durationMs + "ms" }}
                  </span>
                </td>
              </tr>
            </tbody>
          </table>
          <div class="pagination-cont">
            <el-pagination
              background
              layout="prev, pager, next, total"
              :total="totalItemsDetail"
              v-model:current-page="pagination.currentPageDetail"
              :page-size="pagination.pageSizeDetail"
              @current-change="handlePageChange"
            />
          </div>
        </div>
        <div class="attr-panel">
          <div class="tabs">
            <div class="tab active">跨度（span）信息</div>
          </div>
          <div class="tab-content">
            <table class="detail-table">
              <tbody>
                <tr>
                  <td class="label" width="180">服务名</td>
                  <td>
                    {{ spanList.serviceName }}{{ spanList.alias ? ` （${spanList.alias}）` : "" }}
                  </td>
                </tr>
                <tr>
                  <td class="label" width="180">IP地址</td>
                  <td>{{ spanList.ip }}</td>
                </tr>
                <tr>
                  <td class="label" width="180">Trace ID</td>
                  <td>{{ spanList.traceId }}</td>
                </tr>
                <tr>
                  <td class="label" width="180">Span ID</td>
                  <td>{{ spanList.spanId }}</td>
                </tr>
                <tr>
                  <td class="label" width="180">Span Name</td>
                  <td>{{ spanList.spanName }}</td>
                </tr>
                <tr>
                  <td class="label" width="180">状态信息</td>
                  <td>{{ spanList.statusMessage }}</td>
                </tr>
                <tr>
                  <td class="label" width="180">开始时间</td>
                  <td>{{ spanList.startTime }}</td>
                </tr>
                <tr>
                  <td class="label" width="180">耗时</td>
                  <td>{{ spanList.durationMs + " ms" }}</td>
                </tr>
              </tbody>
            </table>
          </div>
          <el-tabs style="margin-top: 10px">
            <div class="box1">
              <el-tab-pane label="跨度属性">
                <table class="detail-table">
                  <tbody>
                    <tr v-for="(value, key) in attributes" :key="key">
                      <td class="label" width="150">
                        <el-tooltip class="box-item" effect="dark" :content="key" placement="top">
                          <span class="text">{{ key }}</span>
                        </el-tooltip>
                      </td>
                      <td>
                        <el-tooltip class="box-item" effect="dark" :content="value" placement="top">
                          <span class="textValue">{{ value }}</span>
                        </el-tooltip>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </el-tab-pane>
              <el-tab-pane label="资源属性">
                <table class="detail-table">
                  <tbody>
                    <tr v-for="(value, key) in resourceAttributes" :key="key">
                      <td class="label" width="150">
                        <el-tooltip class="box-item" effect="dark" :content="key" placement="top">
                          <span class="text">{{ key }}</span>
                        </el-tooltip>
                      </td>
                      <td>
                        <el-tooltip class="box-item" effect="dark" :content="value" placement="top">
                          <span class="textValue">{{ value }}</span>
                        </el-tooltip>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </el-tab-pane>
              <el-tab-pane label="事件">
                <div v-if="eventList.length > 0">
                  <table
                    class="detail-table"
                    style="margin-bottom: 15px"
                    v-for="(item, index) in eventList"
                    :key="index"
                  >
                    <tbody>
                      <tr>
                        <td class="label" width="180">事件名称</td>
                        <td>
                          <el-tooltip
                            class="box-item"
                            effect="dark"
                            :content="item.name"
                            placement="top"
                          >
                            <span>{{ item.name }}</span>
                          </el-tooltip>
                        </td>
                      </tr>
                      <tr>
                        <td class="label" width="180">触发时间</td>
                        <td>
                          <el-tooltip
                            class="box-item"
                            effect="dark"
                            :content="item.time"
                            placement="top"
                          >
                            <span>{{ item.time }}</span>
                          </el-tooltip>
                        </td>
                      </tr>
                      <tr v-if="item.attributes && Object.keys(item.attributes).length > 0">
                        <td class="label" width="180">事件属性</td>
                        <td>
                          <span
                            class="textValue action-link"
                            @click="getEventType(item.attributes)"
                            >{{ "查看详情" }}</span
                          >
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
                <div v-else class="Nologs">未查询到事件</div>
              </el-tab-pane>
              <el-tab-pane label="日志">
                <div v-if="logsList.length > 0">
                  <div
                    v-for="(item, index) in logsList"
                    :key="index"
                    class="logs-container"
                    style="white-space: pre-wrap; word-break: break-word"
                  >
                    {{
                      `[${dayjs(item.timestamp).format("YYYY-MM-DD HH:mm:ss")}]  [${item.level}] [${item.metadata.scopeName}]`
                    }}
                    <div>
                      {{ item.message }}
                    </div>
                  </div>
                </div>
                <div v-else class="Nologs">未查询到日志</div>
              </el-tab-pane>
            </div>
          </el-tabs>
        </div>
      </div>
    </el-drawer>
    <el-dialog
      :align-center="true"
      v-model="eventDialogVisible"
      title="事件详情"
      width="1200"
      class="custom-dialog"
      center
    >
      <div class="dialog-content">
        <div class="detail-row" v-for="(value, key) in eventType" :key="key">
          <div class="detail-title">
            <span>{{ key }}</span>
          </div>
          <div class="detail-value">
            <span>{{ value }}</span>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import { Search } from "@element-plus/icons-vue";
import { ref, reactive, onMounted } from "vue";
import {
  callChainList,
  callChainServices,
  callChainTraces,
  callChainSpan,
  callChainSpanList,
  callChainEventList
} from "@/api/service/callChain";
import { useRoute } from "vue-router";
interface CallType {
  spanId: string;
  spanName: string;
  requestType: string;
  durationMs: number;
  durationWidth: string;
  result: number;
  durationPadding: number;
}
const isLoading = ref(false);
const callTypeList = ref<CallChainDetailItem[]>([]);
const resourceAttributes = ref(null);
const attributes = ref(null);
const logsList = ref([
  {
    timestamp: "",
    level: "",
    message: "",
    metadata: {
      scopeName: ""
    }
  }
]);
const eventDialogVisible = ref(false);
const filterForm = reactive({
  serviceName: "",
  status: "",
  endpoint: "",
  sql: "",
  durationMin: "",
  durationMax: "",
  instanceId: "",
  traceId: "",
  ip: "",
  traceType: ""
});
const appliedFilterForm = reactive({
  serviceName: "",
  status: "",
  endpoint: "",
  sql: "",
  durationMin: "",
  durationMax: "",
  instanceId: "",
  traceId: "",
  ip: "",
  traceType: ""
});
const obj = ref();
const options = ref([
  {
    value: 0,
    label: "成功"
  },
  {
    value: 1,
    label: "失败"
  }
]);
const serviceNameOptions = ref([
  {
    value: "",
    label: ""
  }
]);
const traceTypeOptions = ref([
  {
    value: 0,
    label: "Endpoint"
  },
  {
    value: 1,
    label: "SQL"
  },
  {
    value: 2,
    label: "NoSQL"
  },
  {
    value: 3,
    label: "MQ（生产者）"
  },
  {
    value: 4,
    label: "MQ（消费者）"
  }
]);
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  sort: "",
  order: "",
  currentPageDetail: 1,
  pageSizeDetail: 15
});
const callChain = ref([]);
const totalRecords = ref(0);
const logVisible = ref(false);
const logType = ref({
  serviceName: "",
  status: "",
  endpoint: "",
  sql: "",
  durationMin: "",
  durationMax: "",
  instanceId: "",
  traceId: "",
  traceType: "",
  ip: ""
});

// 日志服务名
const fetchcallChainServices = async () => {
  try {
    const res = await callChainServices({});
    if (res.code === 0 && res.records) {
      serviceNameOptions.value = res.records.map((item: any) => {
        return { value: item, label: item };
      });
    }
  } catch (error) {
    console.error("Error logs:", error);
  }
};

// 列表排序
const handleSortChange = (val: any) => {
  const order = val.order;
  const sort = val.prop;
  if (order === "ascending") {
    pagination.order = "0";
  } else {
    pagination.order = "1";
  }
  pagination.sort = sort;
  fetchCallChain();
};

// 调用链列表
const fetchCallChain = async () => {
  isLoading.value = true;
  const params = {
    page: pagination.currentPage.toString(),
    rows: pagination.pageSize.toString(),
    serviceName: serviceShow.value ? appliedFilterForm.serviceName : obj.value.serviceName,
    status: appliedFilterForm.status,
    endpoint: appliedFilterForm.endpoint.trim(),
    durationMin: appliedFilterForm.durationMin.trim(),
    durationMax: appliedFilterForm.durationMax.trim(),
    sql: appliedFilterForm.sql.trim(),
    sort: pagination.sort,
    order: pagination.order,
    instanceId: appliedFilterForm.instanceId.trim(),
    traceType: appliedFilterForm.traceType,
    traceId: appliedFilterForm.traceId.trim()
  };
  try {
    const res = await callChainList(params);
    if (res.code === 0 && res.records) {
      callChain.value = res.records;
      totalRecords.value = Number(res.total);
      isLoading.value = false;
    }
  } catch (error) {
    console.error("Error callChain:", error);
    isLoading.value = false;
  }
};

const titleTraceId = ref("");
const drawerLoading = ref(false);
const showType = async (row: any) => {
  logVisible.value = true;
  titleTraceId.value = row.traceId;
  drawerLoading.value = true;
  try {
    await fetchCallChainDetail(row.traceId, row.spanId);
  } finally {
    drawerLoading.value = false;
  }
};
const handleDrawerClose = () => {
  logType.value = {
    serviceName: "",
    status: "",
    endpoint: "",
    sql: "",
    durationMin: "",
    durationMax: "",
    instanceId: "",
    traceId: "",
    traceType: "",
    ip: ""
  };
  callTypeList.value = [];
  logsList.value = [];
  eventList.value = [];
  attributes.value = null;
  resourceAttributes.value = null;
  titleTraceId.value = "";
  logVisible.value = false;
};
const executeSearch = () => {
  pagination.currentPage = 1;
  Object.assign(appliedFilterForm, filterForm);
  fetchCallChain();
};

const resetSearch = () => {
  for (const key in filterForm) {
    filterForm[key] = "";
  }
  fetchCallChain();
};

const onPageSizeChange = (size: number) => {
  pagination.pageSize = size;
  fetchCallChain();
};

const onCurrentPageChange = (page: number) => {
  pagination.currentPage = page;
  fetchCallChain();
};

const serviceShow = ref(false);
// 调用链详情
interface CallChainDetailItem {
  spanId: string;
  parentSpanId?: string; // 可选属性
  duration: number;
  durationWidth?: string; // 可选属性
  durationPadding?: number; // 可选属性
}

interface FetchCallChainDetailParams {
  traceId: string;
  page: string;
  rows: string;
}

interface FetchCallChainDetailResponse {
  code: number;
  records: CallChainDetailItem[];
}

const fetchCallChainDetail = async (traceId: string, spanId: string): Promise<void> => {
  const params: FetchCallChainDetailParams = {
    traceId,
    page: pagination.currentPageDetail.toString(),
    rows: pagination.pageSizeDetail.toString()
  };

  try {
    const res: FetchCallChainDetailResponse = await callChainTraces(params);
    if (res.code === 0 && res.records) {
      const totalDuration = res.records.reduce(
        (sum: number, item: CallChainDetailItem) => sum + item.duration,
        0
      );

      res.records.forEach((item: CallChainDetailItem) => {
        item.durationWidth = (item.duration / totalDuration) * 100 + "%";
        item.durationPadding = 25; // 初始化为 25
      });

      const recordMap = new Map<string, CallChainDetailItem>(
        res.records.map((item: CallChainDetailItem) => [item.spanId, item])
      );

      res.records.forEach((item: CallChainDetailItem) => {
        if (item.parentSpanId) {
          const parent = recordMap.get(item.parentSpanId);
          if (parent) {
            item.durationPadding = parent.durationPadding ? parent.durationPadding + 25 : 25;
          }
        }
      });

      callTypeList.value = res.records;

      // 设置选中的行
      const selectedRow = res.records.find((item: CallChainDetailItem) => item.spanId === spanId);
      if (selectedRow) {
        handleCallTypeClick(selectedRow);

        const indexInCallTypeList = callTypeList.value.findIndex(
          (item: CallChainDetailItem) => item.spanId === spanId
        );
        if (indexInCallTypeList !== -1) {
          const pageNumber = Math.floor(indexInCallTypeList / pagination.pageSizeDetail) + 1;
          pagination.currentPageDetail = pageNumber;
        }
      }
    }
  } catch (error) {
    console.error("调用链详情 error:", error);
  }
};

const totalItemsDetail = computed(() => callTypeList.value.length);
const paginatedCallTypeList = computed<CallType[]>(() => {
  const start = (pagination.currentPageDetail - 1) * pagination.pageSizeDetail;
  const end = start + pagination.pageSizeDetail;
  return callTypeList.value.slice(start, end);
});

const handlePageChange = (newPage: number) => {
  pagination.currentPageDetail = newPage;
};
const selectedSpanId = ref(null);

// 点击详情列表
const handleCallTypeClick = async (row: any) => {
  selectedSpanId.value = row.spanId;
  drawerLoading.value = true;
  try {
    await fetchcallChainSpanMassage({ spanId: row.spanId, traceId: row.traceId });
    await fetchcallChainSpanList({ spanId: row.spanId, traceId: row.traceId });
    await fetchcallChainEventList({ spanId: row.spanId, traceId: row.traceId });
  } finally {
    drawerLoading.value = false;
  }
};

// 调用链详情日志
const fetchcallChainSpanList = async (data: any) => {
  try {
    const res = await callChainSpanList(data);
    if (res.code === 0 && res.records) {
      logsList.value = res.records;
    }
  } catch (error) {
    console.error("Error logs:", error);
  }
};

interface SpanList {
  alias: string;
  serviceName: string;
  ip: string;
  traceId: string;
  spanId: string;
  spanName: string;
  statusMessage: string;
  startTime: string;
  durationMs: number;
}

const spanList = ref<SpanList>({
  serviceName: "",
  ip: "",
  traceId: "",
  spanId: "",
  spanName: "",
  statusMessage: "",
  startTime: "",
  durationMs: 0
});

// 调用链跨度信息
const fetchcallChainSpanMassage = async (data: any) => {
  try {
    const res = await callChainSpan(data);
    if (res.code === 0) {
      spanList.value = res.entity;
      resourceAttributes.value = res.entity?.resourceAttributes;
      attributes.value = res.entity?.attributes;
    }
  } catch (error) {
    console.error("Error logs:", error);
  }
};

// 调用链详情事件
const eventList = ref([
  {
    name: "",
    time: "",
    attributes: "",
    severityText: "",
    body: ""
  }
]);
const fetchcallChainEventList = async (data: any) => {
  try {
    const res = await callChainEventList(data);
    if (res.code === 0 && res.records) {
      eventList.value = res.records;
    }
  } catch (error) {
    console.error("Error logs:", error);
  }
};
const eventType = ref({});
const getEventType = (type: any) => {
  eventType.value = type;
  eventDialogVisible.value = true;
};
onMounted(() => {
  try {
    const service = sessionStorage.getItem("serviceName");

    if (service) {
      obj.value = JSON.parse(service);
    } else {
      serviceShow.value = true;
      fetchcallChainServices();
    }
    const route = useRoute();
    const { sql, traceId, serviceName, instanceId, endpoint, ip } = route.query as {
      sql?: string;
      ip?: string;
      traceId?: string;
      serviceName?: string;
      instanceId?: string;
      endpoint?: string;
    };
    filterForm.sql = appliedFilterForm.sql = sql ?? "";
    filterForm.ip = appliedFilterForm.ip = ip ?? "";
    filterForm.traceId = appliedFilterForm.traceId = traceId ?? "";
    filterForm.serviceName = appliedFilterForm.serviceName = serviceName ?? "";
    filterForm.instanceId = appliedFilterForm.instanceId = instanceId ?? "";
    filterForm.endpoint = appliedFilterForm.endpoint = endpoint ?? "";
    fetchCallChain();
  } catch (error) {
    console.error("接收参数问题 ===>>>", error);
  }
});
</script>

<style lang="scss" scoped>
.log-container {
  display: flex;
  overflow-y: auto;
  overflow-x: hidden;

  .search-container {
    min-width: 260px;
    border: 1px solid #eee;
    background: #ffffff;
    padding: 15px 15px 25px 15px;
    margin-right: 10px;
    z-index: 99;
    top: 186px;
    left: 20px;
  }

  .tabel-container {
    flex: 1;
    min-width: calc(100% - 270px);
  }
}

.detail-table {
  width: 100%;
  color: #606266;
  font-size: 14px;
  border: 1px solid #ebeef5;
  border-collapse: collapse;

  tr {
    td {
      border: 1px solid #ebeef5;
      word-break: break-all;
      padding: 15px;
    }

    td.label {
      background: #f5f7fa;
      width: 211px;
      min-width: 120px;
    }
  }
}

.trace-panel {
  width: 100%;
  height: 100%;
  display: flex;
  color: #333;
  font-size: 14px;
  box-sizing: border-box;

  .attr-panel {
    flex: 1;
    margin-left: 10px;
    tr {
      height: 50px;
    }
  }

  .span-panel {
    min-width: 750px;
    position: relative;
    table {
      border: 1px solid #ebeef5;
      border-collapse: collapse;

      tbody {
        tr {
          transition: background 0.5s;
          height: 47px;

          &:hover {
            background: #f5f7fa;
          }
        }
      }

      td {
        padding: 12px;
        position: relative;

        .duration {
          position: absolute;
          align-items: center;
          padding-left: 15px;
          display: flex;
          left: 5px;
          right: 5px;
          top: 5px;
          bottom: 5px;

          &.success {
            background: #c1e488;
          }

          &.error {
            background: #ff9393;
          }
        }

        .bar {
          width: 3px;
          height: 18px;
          margin-right: 10px;
          display: inline-block;
          vertical-align: middle;

          &.success {
            background: #009431;
          }

          &.error {
            background: #e00000;
          }
        }

        .name {
          vertical-align: middle;
          display: inline-block;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          max-width: 260px;
        }
        .tbName {
          vertical-align: middle;
          display: inline-block;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          max-width: 150px;
          line-height: 18px;
        }
      }

      thead {
        tr {
          height: 45px;
          td {
            background: #f9f9f9;
            border-bottom: 1px solid #ebeef5;
          }
        }
      }
    }
  }
}

.tabs {
  display: flex;
  margin-bottom: 10px;
  margin-top: -2px;

  .tab {
    color: #409eff;
    align-items: center;
    text-align: center;
    padding: 0 0 10px;
    border-bottom: 2px solid #409eff;
  }

  .tab-content {
    border-radius: 4px;
    margin-top: -1px;
    margin-bottom: 20px;
  }
}

.text {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  width: 180px;
  display: inline-block;
}

.textValue {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  max-width: 400px;
  display: inline-block;
  line-height: 18px;
}

.box1 {
  height: 370px;
  overflow-x: hidden;
  overflow-y: auto;
}

.logs-container {
  padding: 7px 5px;
  font-size: 15px;
}

.selected-row {
  background-color: #f5f7fa;
}

.error-bg {
  background-color: #e00000;
}

.success-bg {
  background-color: #009431;
}

.action-link {
  color: #0064c8;
  cursor: pointer;
}

.input-group {
  margin-bottom: 10px;
}

.vertical-line {
  margin-right: 10px;
  width: 3px;
  height: 18px;
  display: inline-block;
  vertical-align: middle;
}

.service-name {
  cursor: pointer;
  color: #0064c8;
  vertical-align: middle;
}

.noHover {
  pointer-events: none;
}

:deep(.el-drawer__header) {
  margin: 5px !important;
}
.Nologs {
  margin-top: 20px;
  font-size: 15px;
}
.custom-dialog .dialog-content {
  max-height: 700px;
  max-width: 1200px;
  overflow-y: auto;
  border-bottom: 1px solid #eaeaea;
}

.detail-row {
  display: flex;
  border: 1px solid #eaeaea;
  border-bottom: none;
  .detail-title {
    width: 150px;
    padding: 25px 10px;
    font-size: 15px;
    background: #f5f7fa;
    border-right: 1px solid #eaeaea;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .detail-value {
    padding: 25px 10px;
    width: 1000px;
    font-size: 15px;
  }
}
.pagination-cont {
  width: 750px;
  display: flex;
  justify-content: center;
  margin-top: 30px;
}
</style>
