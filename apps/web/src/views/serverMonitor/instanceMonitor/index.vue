<template>
  <div class="log-container">
    <div class="search-container">
      <el-form>
        <el-form-item class="input-group">
          <label for="instanceId">实例ID：</label>
          <el-select
            id="instanceId"
            placeholder="请选择实例ID"
            v-model="pageParams.instanceId"
            filterable
            clearable
          >
            <el-option
              v-for="instanceId in InstanceIds"
              :key="instanceId"
              :label="instanceId"
              :value="instanceId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item class="input-group">
          <label for="ip">IP地址：</label>
          <el-select
            id="ip"
            placeholder="请选择IP地址"
            v-model="pageParams.ip"
            filterable
            clearable
          >
            <el-option v-for="ip in InstanceIps" :key="ip" :label="ip" :value="ip"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item style="display: flex; margin-top: 20px">
          <el-popconfirm
            @confirm="resetSearch"
            title="确定清空吗？"
            confirm-button-text="确定"
            cancel-button-text="取消"
            icon="el-icon-warning"
            :hide-after="0"
          >
            <template #reference>
              <el-button type="danger" plain style="flex: 1; margin-right: 10px"> 清空 </el-button>
            </template>
          </el-popconfirm>
          <el-button type="primary" :icon="Search" style="flex: 1" @click="search">
            查询
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="tabel-container">
      <div class="flex justify-between indicator-wrapper">
        <BaseEcharts :options="options1" height="250px" v-loading="StatRequestCountLoading" />
        <BaseEcharts :options="options2" height="250px" v-loading="StatErrorCountLoading" />
        <BaseEcharts :options="options3" height="250px" v-loading="StatAvgCountLoading" />
      </div>
      <MyTable
        :data="list.records"
        :total="list.total"
        v-loading="tableLoading"
        @sizeChange="handleSizeChange"
        @currentChange="handleCurrentChange"
        :default-sort="{
          prop: 'requestCount && errorCount && errorRate && durationMs',
          order: 'descending'
        }"
        @sort-change="handleSortChange"
      >
        <my-column property="instanceId" label="实例ID" width="350">
          <template #default="scope">
            <span
              class="vertical-line"
              :class="{
                'error-bg': scope.row.errorCount > 0,
                'success-bg': scope.row.errorCount === 0
              }"
            ></span>
            <span class="service-name" @click="shouType(scope.row)">
              {{ scope.row.instanceId }}
            </span>
          </template>
        </my-column>
        <my-column property="ip" label="IP地址" />
        <!-- <my-column property="osType" label="操作系统" /> -->
        <my-column property="endTime" label="最近请求时间" width="180" />
        <my-column property="requestCount" label="请求数（次）" sortable="custom">
          <template v-slot="{ row }">
            <span>
              {{ formatNums(row.requestCount).fixValue
              }}{{ formatNums(row.requestCount).unit || "" }}
            </span>
          </template>
        </my-column>
        <my-column property="errorCount" label="错误数（次）" sortable="custom" />
        <my-column property="errorRate" label="错误率" sortable="custom">
          <template v-slot="{ row }">
            <span> {{ row.errorRate }}% </span>
          </template>
        </my-column>
        <my-column property="durationMs" label="平均耗时" sortable="custom">
          <template v-slot="{ row }">
            <span> {{ row.durationMs }}ms </span>
          </template>
        </my-column>
        <my-column label="操作" align="center" header-align="center" fixed="right" width="200">
          <template #default="scope">
            <span class="operate" @click="shouType(scope.row)">详情 </span>
            <span class="divider"> / </span>
            <span class="operate" @click="shouJVM(scope.row)">JVM监测</span>
            <span class="divider"> / </span>
            <span class="operate" @click="handleCallChain(scope.row)">调用链</span>
          </template>
        </my-column>
      </MyTable>
    </div>
  </div>

  <div>
    <el-drawer v-model="logVisible" :title="title" size="50%">
      <table class="detail-table" v-loading="drawerLoading">
        <tbody>
          <tr>
            <td class="label">实例ID</td>
            <td>
              {{ detailList.instanceId }}
            </td>
          </tr>
          <tr>
            <td class="label">IP地址</td>
            <td>{{ detailList.ip }}</td>
          </tr>
          <tr>
            <td class="label">服务名称</td>
            <td>
              {{ detailList.serviceName }}
              {{ useServiceNameStore.alias ? ` （${useServiceNameStore.alias}）` : "" }}
            </td>
          </tr>
          <tr>
            <td class="label">主机名</td>
            <td>{{ detailList.hostName }}</td>
          </tr>
          <tr>
            <td class="label">主机CPU架构</td>
            <td>{{ detailList.hostArch }}</td>
          </tr>
          <tr>
            <td class="label">操作系统</td>
            <td>{{ detailList.osType }}</td>
          </tr>
          <tr>
            <td class="label">操作系统描述</td>
            <td>{{ detailList.osDescription }}</td>
          </tr>
          <tr>
            <td class="label">程序语言</td>
            <td>{{ detailList.language }}</td>
          </tr>
          <tr>
            <td class="label">进程ID</td>
            <td>{{ detailList.processPid }}</td>
          </tr>
          <tr>
            <td class="label">运行时名称</td>
            <td>{{ detailList.processRuntimeName }}</td>
          </tr>
          <tr>
            <td class="label">运行时版本</td>
            <td>{{ detailList.processRuntimeVersion }}</td>
          </tr>
          <tr>
            <td class="label">运行时描述</td>
            <td>{{ detailList.processRuntimeDescription }}</td>
          </tr>
          <tr>
            <td class="label">可执行路径</td>
            <td>{{ detailList.processExecutablePath }}</td>
          </tr>
          <tr>
            <td class="label">启动命令</td>
            <td>{{ detailList.processCommandLine }}</td>
          </tr>
        </tbody>
      </table>
    </el-drawer>
  </div>
  <div>
    <el-drawer v-model="JVMvisible" :title="jvmTitle" size="55%">
      <div class="flex justify-between indicator-wrapper">
        <BaseEcharts
          :options="cpuUtilization"
          width="22vw"
          height="250px"
          v-loading="JvmStatCpuLoading"
        />
        <div v-if="showJvmStatMemory" class="ranking-wrapper w-100%">
          <TitlecCom :title="statMemoryTitle"></TitlecCom>
          <el-empty description="暂无数据" />
        </div>
        <BaseEcharts
          v-else
          :options="jvmStatMemory"
          width="22vw"
          height="250px"
          v-loading="jvmStatMemoryLoading"
        />
      </div>
      <div class="flex justify-between indicator-wrapper">
        <div v-if="showJvmStatMemoryPool" class="ranking-wrapper w-100%">
          <TitlecCom :title="statMemoryPoolTitle"></TitlecCom>
          <el-empty description="暂无数据" />
        </div>
        <BaseEcharts
          v-else
          :options="jvmStatMemoryPool"
          width="22vw"
          height="250px"
          v-loading="JvmStatMemoryPoolLoading"
        />
        <div v-if="showJvmStatGc" class="ranking-wrapper w-100%">
          <TitlecCom :title="statGcTitle"></TitlecCom>
          <el-empty description="暂无数据" />
        </div>
        <BaseEcharts
          v-else
          :options="jvmStatGc"
          width="22vw"
          height="250px"
          v-loading="jvmStatGcLoading"
        />
      </div>
      <div class="flex justify-between indicator-wrapper">
        <div v-if="showJvmStatGcCount" class="ranking-wrapper w-100%">
          <TitlecCom :title="statGcCountTitle"></TitlecCom>
          <el-empty description="暂无数据" />
        </div>
        <BaseEcharts
          v-else
          :options="jvmStatGcCount"
          width="22vw"
          height="250px"
          v-loading="jvmStatGcCountLoading"
        />
        <div v-if="showJvmStatThreadType" class="ranking-wrapper w-100%">
          <TitlecCom :title="statThreadTypeTitle"></TitlecCom>
          <el-empty description="暂无数据" />
        </div>
        <BaseEcharts
          v-else
          :options="jvmStatThreadType"
          width="22vw"
          height="250px"
          v-loading="jvmStatThreadTypeLoading"
        />
      </div>
      <div class="flex justify-between indicator-wrapper">
        <div v-if="showJvmStatThreadState" class="ranking-wrapper w-100%">
          <TitlecCom :title="statThreadStateTitle"></TitlecCom>
          <el-empty description="暂无数据" />
        </div>
        <BaseEcharts
          v-else
          :options="jvmStatThreadState"
          width="22vw"
          height="250px"
          v-loading="jvmStatThreadStateLoading"
        />
        <div v-if="showjvmStatClassCount" class="ranking-wrapper w-100%">
          <TitlecCom :title="statClassCountTitle"></TitlecCom>
          <el-empty description="暂无数据" />
        </div>
        <BaseEcharts
          v-else
          :options="jvmStatClassCount"
          width="22vw"
          height="250px"
          v-loading="jvmStatClassCountLoading"
        />
      </div>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import TitlecCom from "@/components/TitleCom/index.vue";
import MyTable from "@/components/table/my-table.vue";
import MyColumn from "@/components/table/my-column.vue";
import { Search } from "@element-plus/icons-vue";
import { formatNums } from "@/utils/formatStr";
import {
  getInstanceList,
  getInstanceDetail,
  getInstanceIps,
  getInstanceIds,
  getStatRequestCount,
  getStatErrorCount,
  getStatAvgDuration,
  getJvmStatCpu,
  getJvmStatMemory,
  getJvmStatMemoryPool,
  getJvmStatGc,
  getJvmStatGcCount,
  getJvmStatThreadType,
  getJvmStatThreadState,
  getJvmStatClassCount
} from "@/api/service/instance";
import { applicationStore } from "@/store/modules/application";
import { serviceNameStore } from "@/store/modules/service";
import BaseEcharts from "@/components/baseEcharts/index.vue";
import { getChartOptions } from "@/components/baseEcharts/chartsOptions";
const router = useRouter();
import { useRouter } from "vue-router";
const useApplicationStore = applicationStore();
const useServiceNameStore = serviceNameStore();
const title = ref(""); //抽屉标题
const instanceId = ref("");
const StatRequestCountLoading = ref(false); //请求统计loading
const StatErrorCountLoading = ref(false); //错误统计loading
const StatAvgCountLoading = ref(false); //耗时统计loading
const JvmStatCpuLoading = ref(false); //CPU利用率loading
const jvmStatMemoryLoading = ref(false); //按类型统计内存使用率loading
const JvmStatMemoryPoolLoading = ref(false); //按内存池统计内存使用率loading
const jvmStatGcLoading = ref(false); //统计GC时间;loading
const jvmStatGcCountLoading = ref(false); //统计GC数量loading
const jvmStatThreadTypeLoading = ref(false); //按照线程类型统计loading
const jvmStatThreadStateLoading = ref(false); //按照线程状态统计loading
const jvmStatClassCountLoading = ref(false); //统计类加载数量loading
const statMemoryTitle = ref(""); //类型统计内存使用率标题
const statMemoryPoolTitle = ref(""); //内存池统计内存使用率标题
const statGcTitle = ref(""); //GC时间标题
const statGcCountTitle = ref(""); //GC数量标题
const statThreadTypeTitle = ref(""); //线程类型统计标题
const statThreadStateTitle = ref(""); //线程状态统计标题
const statClassCountTitle = ref(""); //类加载数量标题
function getTimeUnit(granularity: any) {
  switch (granularity) {
    case 0:
      return "按秒";
    case 1:
      return "按分钟";
    case 2:
      return "按小时";
    case 3:
      return "按天";
    case 4:
      return "按月";
    default:
      return undefined;
  }
}
//loading动画
const tableLoading = ref(true);
const logVisible = ref(false);
const JVMvisible = ref(false);
const jvmTitle = ref("");
//列表参数
const pageParams = reactive({
  appid: "",
  serviceName: "",
  page: 1,
  rows: 10,
  sort: "",
  order: "",
  ip: "",
  instanceId: ""
});
//排序
const handleSortChange = (val: any) => {
  const order = val.order;
  const sort = val.prop;
  if (order === "ascending") {
    pageParams.order = "0";
  } else {
    pageParams.order = "1";
  }
  pageParams.sort = sort;
  loadData();
};
//修改每页条数
const handleSizeChange = (val: number) => {
  pageParams.rows = val;
  loadData();
};
//分页
const handleCurrentChange = (val: number) => {
  pageParams.page = val;
  loadData();
};
//跳转到调用链
const handleCallChain = (row: any) => {
  router.push({
    path: "/dashboard/overview/Call-chain",
    query: {
      instanceId: row.instanceId
    }
  });
};
//搜索
function search() {
  pageParams.page = 1;
  pageParams.sort = "";
  pageParams.order = "";
  loadData();
  StatRequestCount();
  StatErrorCount();
  StatAvgCount();
}
//清空
function resetSearch() {
  pageParams.ip = "";
  pageParams.instanceId = "";
}
const list = reactive({
  records: [],
  total: 0
});
//列表数据
function loadData() {
  tableLoading.value = true;
  pageParams.serviceName = useServiceNameStore.serviceName;
  pageParams.appid = useApplicationStore.appId;
  getInstanceList(pageParams)
    .then(response => {
      if (response.code === 0) {
        list.records = response.records;
        list.total = Number(response.total);
        tableLoading.value = false;
      }
    })
    .catch(error => {
      tableLoading.value = false;
      console.log(error);
    });
}
//详情
const shouType = (row: any) => {
  title.value = "实例详情 【" + row.instanceId + "】";
  instanceId.value = row.instanceId;
  logVisible.value = true;
  detailData();
};
//jvm图表
const shouJVM = (row: any) => {
  instanceId.value = row.instanceId;
  JVMvisible.value = true;
  jvmTitle.value = "JVM监测 【" + row.instanceId + "】";
  CpuUtilization();
  JvmStatMemory();
  JvmStatMemoryPool();
  JvmStatGc();
  JvmStatGcCount();
  JvmStatThreadType();
  JvmStatThreadState();
  JvmStatClassCount();
};
//详情jvm传参
const dataTosed = reactive({
  appid: "",
  serviceName: "",
  instanceId: instanceId
});
//详情字段
const detailList = ref({
  instanceId: "",
  ip: "",
  serviceName: "",
  hostName: "",
  hostArch: "",
  osType: "",
  osDescription: "",
  language: "",
  processPid: "",
  processRuntimeName: "",
  processRuntimeVersion: "",
  processRuntimeDescription: "",
  processExecutablePath: "",
  processCommandLine: ""
});
//详情接口
const drawerLoading = ref(false);
function detailData() {
  drawerLoading.value = true;
  dataTosed.serviceName = useServiceNameStore.serviceName;
  dataTosed.appid = useApplicationStore.appId;
  getInstanceDetail(dataTosed).then(response => {
    if (response.code === 0) {
      detailList.value = response.entity;
      drawerLoading.value = false;
    }
  });
}
//IP地址下拉框数据
const InstanceIps = ref([]);
const dataToSend = reactive({
  appid: "",
  serviceName: ""
});
function getInstanceIp() {
  dataToSend.serviceName = useServiceNameStore.serviceName;
  dataToSend.appid = useApplicationStore.appId;
  getInstanceIps(dataToSend).then(response => {
    if (response.code === 0) {
      InstanceIps.value = response.records;
    }
  });
}
//实例ID下拉框数据
const InstanceIds = ref([]);
const params = reactive({
  appid: "",
  serviceName: ""
});
function getInstanceId() {
  params.appid = useApplicationStore.appId;
  params.serviceName = useServiceNameStore.serviceName;
  getInstanceIds(params).then(response => {
    if (response.code === 0) {
      InstanceIds.value = response.records;
    }
  });
}
//请求数统计
const options1 = ref({});
function StatRequestCount() {
  StatRequestCountLoading.value = true;
  getStatRequestCount({
    appid: useApplicationStore.appId,
    instanceId: pageParams.instanceId,
    serviceName: useServiceNameStore.serviceName,
    ip: pageParams.ip
  })
    .then(res => {
      if (res.code === 0 && res.entity && res.entity.datas) {
        const originalTimes = res.entity.datas.map((data: { time: any }) => data.time);
        const seriesData = [res.entity.datas.map((data: { value: any }) => data.value)];
        const params = {
          name: " 次",
          typ: res.entity.granularity,
          color: ["#78bf75"],
          titleType: "请求数",
          originalTimes: originalTimes,
          seriesData: seriesData
        };
        options1.value = getChartOptions(params);
        StatRequestCountLoading.value = false;
      }
    })
    .catch(error => {
      StatRequestCountLoading.value = false;
      console.log(error);
    });
}
//错误统计
const options2 = ref({});
function StatErrorCount() {
  StatErrorCountLoading.value = true;
  getStatErrorCount({
    appid: useApplicationStore.appId,
    instanceId: pageParams.instanceId,
    serviceName: useServiceNameStore.serviceName,
    ip: pageParams.ip
  })
    .then(res => {
      if (res.code === 0 && res.entity && res.entity.datas) {
        const originalTimes = res.entity.datas.map((data: { time: any }) => data.time);
        const seriesData = [res.entity.datas.map((data: { value: any }) => data.value)];
        const params = {
          name: " 次",
          typ: res.entity.granularity,
          color: ["#f56c6c"],
          titleType: "错误数",
          originalTimes: originalTimes,
          seriesData: seriesData
        };
        options2.value = getChartOptions(params);
        StatErrorCountLoading.value = false;
      }
    })
    .catch(error => {
      StatErrorCountLoading.value = false;
      console.log(error);
    });
}
//耗时统计
const options3 = ref({});
function StatAvgCount() {
  StatAvgCountLoading.value = true;
  getStatAvgDuration({
    appid: useApplicationStore.appId,
    instanceId: pageParams.instanceId,
    serviceName: useServiceNameStore.serviceName,
    ip: pageParams.ip
  })
    .then(res => {
      if (res.code === 0 && res.entity && res.entity.datas) {
        const originalTimes = res.entity.datas.map((data: { time: any }) => data.time);
        const seriesData = [res.entity.datas.map((data: { value: any }) => data.value)];
        const params = {
          name: " ms",
          typ: res.entity.granularity,
          color: ["#78bf75"],
          titleType: "平均耗时",
          originalTimes: originalTimes,
          seriesData: seriesData,
          type: "line"
        };
        options3.value = getChartOptions(params);
        StatAvgCountLoading.value = false;
      }
    })
    .catch(error => {
      StatAvgCountLoading.value = false;
      console.log(error);
    });
}
//CPU利用率
const cpuUtilization = ref({});
function CpuUtilization() {
  JvmStatCpuLoading.value = true;
  dataTosed.serviceName = useServiceNameStore.serviceName;
  dataTosed.appid = useApplicationStore.appId;
  getJvmStatCpu(dataTosed).then(res => {
    if (res.code === 0 && res.entity && res.entity.datas) {
      const originalTimes = res.entity.datas.map((data: { time: any }) => data.time);
      const seriesData = [res.entity.datas.map((data: { value: any }) => data.value)];
      const params = {
        name: "%",
        typ: res.entity.granularity,
        color: ["#78bf75"],
        titleType: "CPU利用率",
        originalTimes: originalTimes,
        seriesData: seriesData,
        type: "line"
      };
      cpuUtilization.value = getChartOptions(params);
      JvmStatCpuLoading.value = false;
    }
  });
}
//按类型统计内存使用率
const jvmStatMemory = ref({});
const showJvmStatMemory = ref(false);
function JvmStatMemory() {
  jvmStatMemoryLoading.value = true;
  dataTosed.serviceName = useServiceNameStore.serviceName;
  dataTosed.appid = useApplicationStore.appId;
  getJvmStatMemory(dataTosed).then(res => {
    if (res.code === 0 && res.entity && res.entity.datas) {
      if (Object.keys(res.entity.datas).length === 0) {
        showJvmStatMemory.value = true;
        jvmStatMemoryLoading.value = false;
        statMemoryTitle.value = `内存类型 （${getTimeUnit(res.entity.granularity)}）`;
        return;
      }
      const firstKey = Object.keys(res.entity.datas)[0];
      const originalTimes = res.entity.datas[firstKey].map((data: { time: any }) => data.time);
      const seriesData = Object.keys(res.entity.datas).map(key =>
        res.entity.datas[key].map((data: { value: any }) => data.value)
      );
      const defaultColors = ["#78bf75", "#4ea3ef", "#ff9a3e", "#ef4e4e", "#4e9aff"];
      const names = Object.keys(res.entity.datas).map(key => `${key}`);
      const colors = defaultColors.slice(0, names.length);
      const params = {
        names: names,
        color: colors,
        name: "MB",
        typ: res.entity.granularity,
        titleType: "内存类型",
        originalTimes: originalTimes,
        seriesData: seriesData,
        type: "line",
        legend: { show: true }
      };
      jvmStatMemory.value = getChartOptions(params);
      showJvmStatMemory.value = false;
      jvmStatMemoryLoading.value = false;
    }
  });
}
//按内存池统计内存使用率
const jvmStatMemoryPool = ref({});
const showJvmStatMemoryPool = ref(false);
function JvmStatMemoryPool() {
  JvmStatMemoryPoolLoading.value = true;
  dataTosed.serviceName = useServiceNameStore.serviceName;
  dataTosed.appid = useApplicationStore.appId;
  getJvmStatMemoryPool(dataTosed).then(res => {
    if (res.code === 0 && res.entity && res.entity.datas) {
      if (Object.keys(res.entity.datas).length === 0) {
        showJvmStatMemoryPool.value = true;
        JvmStatMemoryPoolLoading.value = false;
        statMemoryPoolTitle.value = `内存详情 （${getTimeUnit(res.entity.granularity)}）`;
        return;
      }
      const firstKey = Object.keys(res.entity.datas)[0];
      const originalTimes = res.entity.datas[firstKey].map((data: { time: any }) => data.time);
      const seriesData = Object.keys(res.entity.datas).map(key =>
        res.entity.datas[key].map((data: { value: any }) => data.value)
      );
      const defaultColors = ["#78bf75", "#4ea3ef", "#ff9a3e", "#ef4e4e", "#4e9aff"];
      const names = Object.keys(res.entity.datas).map(key => `${key}`);
      const colors = defaultColors.slice(0, names.length);
      const params = {
        names: names,
        color: colors,
        name: "MB",
        typ: res.entity.granularity,
        titleType: "内存详情",
        originalTimes: originalTimes,
        seriesData: seriesData,
        type: "line",
        legend: { show: true }
      };
      jvmStatMemoryPool.value = getChartOptions(params);
      showJvmStatMemoryPool.value = false;
      JvmStatMemoryPoolLoading.value = false;
    }
  });
}
//统计GC时间
const jvmStatGc = ref({});
const showJvmStatGc = ref(false);
function JvmStatGc() {
  jvmStatGcLoading.value = true;
  dataTosed.serviceName = useServiceNameStore.serviceName;
  dataTosed.appid = useApplicationStore.appId;
  getJvmStatGc(dataTosed).then(res => {
    if (res.code === 0 && res.entity && res.entity.datas) {
      if (Object.keys(res.entity.datas).length === 0) {
        showJvmStatGc.value = true;
        jvmStatGcLoading.value = false;
        statGcTitle.value = `GC时长 （${getTimeUnit(res.entity.granularity)}）`;
        return;
      }
      const firstKey = Object.keys(res.entity.datas)[0];
      const originalTimes = res.entity.datas[firstKey].map((data: { time: any }) => data.time);
      const seriesData = Object.keys(res.entity.datas).map(key =>
        res.entity.datas[key].map((data: { value: any }) => data.value)
      );
      const defaultColors = ["#78bf75", "#4ea3ef", "#ff9a3e", "#ef4e4e", "#4e9aff"];
      const names = Object.keys(res.entity.datas).map(key => `${key}`);
      const colors = defaultColors.slice(0, names.length);
      const params = {
        names: names,
        color: colors,
        name: "ms",
        typ: res.entity.granularity,
        titleType: "GC时长",
        originalTimes: originalTimes,
        seriesData: seriesData,
        type: "line",
        numberType: false,
        legend: { show: true }
      };
      jvmStatGc.value = getChartOptions(params);
      showJvmStatGc.value = false;
      jvmStatGcLoading.value = false;
    }
  });
}
//统计GC数量
const jvmStatGcCount = ref({});
const showJvmStatGcCount = ref(false);
function JvmStatGcCount() {
  jvmStatGcCountLoading.value = true;
  dataTosed.serviceName = useServiceNameStore.serviceName;
  dataTosed.appid = useApplicationStore.appId;
  getJvmStatGcCount(dataTosed).then(res => {
    if (res.code === 0 && res.entity && res.entity.datas) {
      if (Object.keys(res.entity.datas).length === 0) {
        jvmStatGcCountLoading.value = false;
        showJvmStatGcCount.value = true;
        statGcCountTitle.value = `GC数量 （${getTimeUnit(res.entity.granularity)}）`;
        return;
      }
      const firstKey = Object.keys(res.entity.datas)[0];
      const originalTimes = res.entity.datas[firstKey].map((data: { time: any }) => data.time);
      const seriesData = Object.keys(res.entity.datas).map(key =>
        res.entity.datas[key].map((data: { value: any }) => data.value)
      );
      const defaultColors = ["#78bf75", "#4ea3ef", "#ff9a3e", "#ef4e4e", "#4e9aff"];
      const names = Object.keys(res.entity.datas).map(key => `${key}`);
      const colors = defaultColors.slice(0, names.length);
      const params = {
        names: names,
        color: colors,
        name: "",
        typ: res.entity.granularity,
        titleType: "GC数量",
        originalTimes: originalTimes,
        seriesData: seriesData,
        type: "bar",
        stack: true,
        legend: { show: true }
      };
      jvmStatGcCount.value = getChartOptions(params);
      showJvmStatGcCount.value = false;
      jvmStatGcCountLoading.value = false;
    }
  });
}
//按照线程类型统计
const jvmStatThreadType = ref({});
const showJvmStatThreadType = ref(false);
function JvmStatThreadType() {
  jvmStatThreadTypeLoading.value = true;
  dataTosed.serviceName = useServiceNameStore.serviceName;
  dataTosed.appid = useApplicationStore.appId;
  getJvmStatThreadType(dataTosed).then(res => {
    if (res.code === 0 && res.entity && res.entity.datas) {
      if (Object.keys(res.entity.datas).length === 0) {
        jvmStatThreadTypeLoading.value = false;
        showJvmStatThreadType.value = true;
        statThreadTypeTitle.value = `线程类型 （${getTimeUnit(res.entity.granularity)}）`;
        return;
      }
      const firstKey = Object.keys(res.entity.datas)[0];
      const originalTimes = res.entity.datas[firstKey].map((data: { time: any }) => data.time);
      const seriesData = Object.keys(res.entity.datas).map(key =>
        res.entity.datas[key].map((data: { value: any }) => data.value)
      );
      const defaultColors = ["#78bf75", "#4ea3ef", "#ff9a3e", "#ef4e4e", "#4e9aff"];
      const names = Object.keys(res.entity.datas).map(key => `${key}`);
      const colors = defaultColors.slice(0, names.length);
      const params = {
        names: names,
        color: colors,
        name: "",
        typ: res.entity.granularity,
        titleType: "线程类型",
        originalTimes: originalTimes,
        seriesData: seriesData,
        type: "line",
        legend: { show: true }
      };
      jvmStatThreadType.value = getChartOptions(params);
      showJvmStatThreadType.value = false;
      jvmStatThreadTypeLoading.value = false;
    }
  });
}
//按照线程状态统计
const jvmStatThreadState = ref({});
const showJvmStatThreadState = ref(false);
function JvmStatThreadState() {
  jvmStatThreadStateLoading.value = true;
  dataTosed.serviceName = useServiceNameStore.serviceName;
  dataTosed.appid = useApplicationStore.appId;
  getJvmStatThreadState(dataTosed).then(res => {
    if (res.code === 0 && res.entity && res.entity.datas) {
      if (Object.keys(res.entity.datas).length === 0) {
        jvmStatThreadStateLoading.value = false;
        showJvmStatThreadState.value = true;
        statThreadStateTitle.value = `线程状态 （${getTimeUnit(res.entity.granularity)}） `;
        return;
      }
      const firstKey = Object.keys(res.entity.datas)[0];
      const originalTimes = res.entity.datas[firstKey].map((data: { time: any }) => data.time);
      const seriesData = Object.keys(res.entity.datas).map(key =>
        res.entity.datas[key].map((data: { value: any }) => data.value)
      );
      const defaultColors = ["#78bf75", "#4ea3ef", "#ff9a3e", "#ef4e4e", "#4e9aff"];
      const names = Object.keys(res.entity.datas).map(key => `${key}`);
      const colors = defaultColors.slice(0, names.length);
      const params = {
        names: names,
        color: colors,
        name: "",
        typ: res.entity.granularity,
        titleType: "线程状态",
        originalTimes: originalTimes,
        seriesData: seriesData,
        type: "bar",
        stack: true,
        legend: { show: true }
      };
      jvmStatThreadState.value = getChartOptions(params);
      showJvmStatThreadState.value = false;
      jvmStatThreadStateLoading.value = false;
    }
  });
}
// 统计类加载数量
const jvmStatClassCount = ref({});
const showjvmStatClassCount = ref(false);
function JvmStatClassCount() {
  jvmStatClassCountLoading.value = true;
  dataTosed.serviceName = useServiceNameStore.serviceName;
  dataTosed.appid = useApplicationStore.appId;
  getJvmStatClassCount(dataTosed).then(res => {
    if (res.code === 0 && res.entity && res.entity.datas) {
      if (Object.keys(res.entity.datas).length === 0) {
        showjvmStatClassCount.value = true;
        jvmStatClassCountLoading.value = false;
        statClassCountTitle.value = `类 （${getTimeUnit(res.entity.granularity)}）`;
        return;
      }
      const firstKey = Object.keys(res.entity.datas)[0];
      const originalTimes = res.entity.datas[firstKey].map((data: { time: any }) => data.time);
      const seriesData = Object.keys(res.entity.datas).map(key =>
        res.entity.datas[key].map((data: { value: any }) => data.value)
      );
      const defaultColors = ["#78bf75", "#4ea3ef", "#ff9a3e", "#ef4e4e", "#4e9aff"];
      const names = Object.keys(res.entity.datas).map(key => `${key}`);
      const colors = defaultColors.slice(0, names.length);
      const params = {
        names: names,
        color: colors,
        name: "",
        typ: res.entity.granularity,
        titleType: "类",
        originalTimes: originalTimes,
        seriesData: seriesData,
        type: "line",
        numberType: false,
        legend: { show: true }
      };
      jvmStatClassCount.value = getChartOptions(params);
      showjvmStatClassCount.value = false;
      jvmStatClassCountLoading.value = false;
    }
  });
}
onMounted(() => {
  loadData();
  getInstanceIp();
  getInstanceId();
  StatRequestCount();
  StatErrorCount();
  StatAvgCount();
});
</script>

<style lang="scss" scoped>
.log-container {
  display: flex;
  overflow-y: auto;
  overflow-x: hidden;

  .search-container {
    min-width: 260px;
    height: 250px;
    border: 1px solid #eee;
    background: #ffffff;
    padding: 15px 15px 25px 15px;
    margin-right: 10px;
    z-index: 99;
    top: 186px;
    left: 20px;
  }

  .tabel-container {
    flex: 1;
    min-width: calc(100% - 270px);
  }
}
.input-group {
  margin-bottom: 10px;
}

.indicator-wrapper > *:not(:last-child) {
  margin-right: 8px;
  box-sizing: border-box;
}
.operate {
  color: #0064c8;
  cursor: pointer;
}
.vertical-line {
  margin-right: 10px;
  width: 3px;
  height: 18px;
  display: inline-block;
  vertical-align: middle;
}
.error-bg {
  background-color: #e00000;
}
.success-bg {
  background-color: #009431;
}
.service-name {
  cursor: pointer;
  color: #0064c8;
  vertical-align: middle;
}
.detail-table {
  width: 100%;
  max-width: 1200px;
  color: #606266;
  font-size: 14px;
  border: 1px solid #ebeef5;
  border-collapse: collapse;
}
.detail-table tr td {
  padding: 15px;
  border: 1px solid #ebeef5;
  word-break: break-all;
}
.detail-table tr {
  width: 200px;
}
.detail-table tr td.label {
  background: #f5f7fa;
  width: 150px;
  min-width: 150px;
  word-break: break-all;
}
:deep(.el-drawer__header) {
  margin: 5px !important;
}
.ranking-wrapper {
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 2px;
  margin-bottom: 10px;
}
</style>
