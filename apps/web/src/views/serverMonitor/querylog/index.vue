<template>
  <div class="log-container">
    <div class="search-container">
      <el-form :model="filterForm">
        <el-form-item class="input-group">
          <label for="serviceName">服务名称：</label>
          <el-select
            id="logLevel"
            placeholder="请选择服务名称"
            v-model="filterForm.serviceName"
            clearable
            filterable
          >
            <el-option
              v-for="item in serviceNameOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item class="input-group">
          <label for="traceId">Trace ID：</label>
          <el-input
            id="traceId"
            v-model="filterForm.traceId"
            clearable
            placeholder="请输入Trace ID"
          />
        </el-form-item>
        <el-form-item class="input-group">
          <label for="ip">IP地址：</label>
          <el-input id="ip" v-model="filterForm.ip" clearable placeholder="***********" />
        </el-form-item>
        <el-form-item class="input-group">
          <label for="logLevel">日志级别：</label>
          <el-select
            id="logLevel"
            placeholder="请选择日志级别"
            v-model="filterForm.logLevel"
            clearable
            filterable
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item class="input-group">
          <label for="logDomain">日志域：</label>
          <el-input
            id="logDomain"
            v-model="filterForm.logDomain"
            clearable
            placeholder="请输入日志域"
          />
        </el-form-item>

        <el-form-item class="input-group">
          <label for="instanceId">实例ID：</label>
          <el-input
            id="instanceId"
            v-model="filterForm.instanceId"
            clearable
            placeholder="请输入实例ID"
          />
        </el-form-item>
        <el-form-item class="input-group">
          <label for="logContent">日志内容：</label>
          <el-input
            id="logContent"
            v-model="filterForm.logContent"
            clearable
            placeholder="请输入日志内容"
          />
        </el-form-item>
        <el-form-item style="display: flex; margin-top: 20px">
          <el-popconfirm
            title="确定清空吗？"
            @confirm="resetSearch"
            confirm-button-text="确定"
            cancel-button-text="取消"
            icon="el-icon-warning"
            :hide-after="0"
          >
            <template #reference>
              <el-button type="danger" plain style="flex: 1; margin-right: 10px"> 清空 </el-button>
            </template>
          </el-popconfirm>
          <el-button
            type="primary"
            :disabled="isLoading"
            @click="executeSearch"
            :icon="Search"
            style="flex: 1"
          >
            查询
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <MyTable
      :data="logs"
      :total="totalRecords"
      pagination-layout="total,prev, next,"
      :pagination-background="true"
      class="tabel-container"
      v-loading="isLoading"
      @sizeChange="onPageSizeChange"
      @currentChange="onCurrentPageChange"
    >
      <MyColumn property="metadata.serviceName" label="服务名">
        <template #default="scope">
          <span>
            {{ scope.row.metadata.serviceName }}
            {{ scope.row.metadata.alias ? ` （${scope.row.metadata.alias}）` : "" }}
          </span>
        </template>
      </MyColumn>
      <MyColumn property="sourceIp" label="IP地址" width="180" />
      <MyColumn property="timestamp" label="时间" width="180">
        <template #default="{ row }">
          <span>{{ dayjs(row.timestamp).format("YYYY-MM-DD HH:mm:ss") }}</span>
        </template>
      </MyColumn>
      <MyColumn label="日志级别" width="150" align="center" header-align="center">
        <template #default="scope">
          <el-tag
            :type="
              ['ERROR', 'FATAL'].includes(scope.row.level)
                ? 'danger'
                : ['INFO', 'TRACE', 'DEBUG'].includes(scope.row.level)
                  ? 'success'
                  : 'warning'
            "
          >
            {{ scope.row.level }}
          </el-tag>
        </template>
      </MyColumn>
      <MyColumn property="metadata.scopeName" label="日志域" />
      <MyColumn property="message" label="日志内容" />
      <MyColumn label="操作" width="120" fixed="right" align="center" header-align="center">
        <template #default="scope">
          <span class="action-link" @click="shouType(scope.row)">详情</span>
        </template>
      </MyColumn>
    </MyTable>
  </div>
  <div>
    <el-drawer v-model="logVisible" title="日志详情" size="50%">
      <table class="detail-table">
        <tbody>
          <tr>
            <td class="label">服务名</td>
            <td>
              {{ logType.metadata.serviceName }}
              {{ logType.metadata.alias ? ` （${logType.metadata.alias}）` : "" }}
            </td>
          </tr>
          <tr>
            <td class="label">IP地址</td>
            <td>{{ logType.sourceIp }}</td>
          </tr>
          <tr>
            <td class="label">时间</td>
            <td>{{ dayjs(logType.timestamp).format("YYYY-MM-DD HH:mm:ss") }}</td>
          </tr>
          <tr>
            <td class="label">日志级别</td>
            <td>
              <el-tag
                size="large"
                :type="
                  ['ERROR', 'FATAL'].includes(logType.level)
                    ? 'danger'
                    : ['INFO', 'TRACE', 'DEBUG'].includes(logType.level)
                      ? 'success'
                      : 'warning'
                "
                >{{ logType.level }}</el-tag
              >
            </td>
          </tr>
          <tr>
            <td class="label">Trace ID</td>
            <td>
              <div class="content-wrapper">
                <span class="content-text">{{ logType.metadata.traceId }}</span>
                <span class="action-link" @click="copyToClipboard(logType.metadata.traceId)"
                  >复制</span
                >
              </div>
            </td>
          </tr>
          <tr>
            <td class="label">实例ID</td>
            <td>
              <div class="content-wrapper">
                <span class="content-text">{{ logType.metadata.instanceId }}</span>
                <span class="action-link" @click="copyToClipboard(logType.metadata.instanceId)"
                  >复制</span
                >
              </div>
            </td>
          </tr>
          <tr>
            <td class="label">日志域</td>
            <td>
              <div class="content-wrapper">
                <span class="content-text">{{ logType.metadata.scopeName }}</span>
                <span class="action-link" @click="copyToClipboard(logType.metadata.scopeName)"
                  >复制</span
                >
              </div>
            </td>
          </tr>
          <tr>
            <td class="label">日志内容</td>
            <td>
              <div class="content-wrapper">
                <span class="content-text">{{ logType.message }}</span>
                <span class="action-link" @click="copyToClipboard(logType.message)">复制</span>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import { ref, reactive, computed, onMounted } from "vue";
import MyTable from "@/components/table/my-table.vue";
import MyColumn from "@/components/table/my-column.vue";
import { logsList, logsSeveritys, logsServices, logsIPs } from "@/api/service/logs";
import { Search } from "@element-plus/icons-vue";
import { useRoute } from "vue-router";
const isLoading = ref(false);
const filterForm = reactive({
  serviceName: "",
  traceId: "",
  logLevel: "",
  logDomain: "",
  instanceId: "",
  ip: "",
  logContent: "",
  sourceType: "service"
});
const appliedFilterForm = reactive({
  serviceName: "",
  traceId: "",
  logLevel: "",
  logDomain: "",
  instanceId: "",
  ip: "",
  logContent: ""
});
const options = ref([]);
const serviceNameOptions = ref([]);
const pagination = reactive({ currentPage: 1, pageSize: 10 });
const logs = ref([]);
const totalRecords = ref(0);
const logVisible = ref(false);
const logType = ref({
  timestamp: "",
  level: "",
  message: "",
  sourceIp: "",
  metadata: {
    serviceName: "",
    traceId: "",
    instanceId: "",
    scopeName: "",
    alias: ""
  }
});

// 日志列表
const fetchLogs = async () => {
  isLoading.value = true;
  const params = {
    page: pagination.currentPage.toString(),
    rows: pagination.pageSize.toString(),
    serviceName: filterForm.serviceName,
    traceId: filterForm.traceId.trim(),
    severityText: filterForm.logLevel,
    scopeName: filterForm.logDomain.trim(),
    instanceId: filterForm.instanceId.trim(),
    body: filterForm.logContent.trim(),
    ip: filterForm.ip.trim(),
    sourceType: filterForm.sourceType
  };
  try {
    const res = await logsList(params);
    if (res.code === 0) {
      logs.value = res.records;
      totalRecords.value = Number(res.total);
      isLoading.value = false;
    }
  } catch (error) {
    console.error("Error logs:", error);
    isLoading.value = false;
  }
};

// 日志级别
const fetchLogsSeveritys = async () => {
  try {
    const res = await logsSeveritys({});
    if (res.code === 0 && res.records) {
      options.value = res.records.map((item: any) => {
        return { value: item, label: item };
      });
    }
  } catch (error) {
    console.error("Error logs:", error);
  }
};

// 日志服务名
const fetchLogsServices = async () => {
  try {
    const res = await logsServices({});
    if (res.code === 0 && res.records) {
      serviceNameOptions.value = res.records.map((item: any) => {
        return { value: item, label: item };
      });
    }
  } catch (error) {
    console.error("Error logs:", error);
  }
};

const shouType = (row: any) => {
  logType.value = row;
  logVisible.value = true;
};
const executeSearch = () => {
  pagination.currentPage = 1;
  fetchLogs();
};

const resetSearch = () => {
  for (const key in filterForm) {
    filterForm[key] = "";
  }
  fetchLogs();
};

const onPageSizeChange = (size: number) => {
  pagination.pageSize = size;
  fetchLogs();
};

const onCurrentPageChange = (page: number) => {
  pagination.currentPage = page;
  fetchLogs();
};

const copyToClipboard = (text: string) => {
  if (navigator.clipboard) {
    navigator.clipboard
      .writeText(text)
      .then(() => {
        ElMessage.success("复制成功");
      })
      .catch(() => {
        execCommandCopy(text);
      });
  } else {
    execCommandCopy(text);
  }
};
// 传统复制方法
const execCommandCopy = (text: string) => {
  try {
    const textarea = document.createElement("textarea");
    textarea.value = text;
    textarea.style.position = "fixed";
    textarea.style.opacity = "0";
    document.body.appendChild(textarea);
    textarea.select();
    const success = document.execCommand("copy");
    document.body.removeChild(textarea);
    if (success) {
      ElMessage.success("复制成功");
    } else {
      throw new Error("复制失败，请手动复制");
    }
  } catch (err) {
    ElMessage.error("复制失败，请手动复制");
  }
};
onMounted(() => {
  const route = useRoute();
  const { serviceName, ip, logLevel } = route.query as {
    serviceName?: string;
    ip?: string;
    logLevel?: string;
  };
  filterForm.ip = appliedFilterForm.ip = ip ?? "";
  filterForm.serviceName = appliedFilterForm.serviceName = serviceName ?? "";
  filterForm.logLevel = appliedFilterForm.logLevel = logLevel ?? "";
  fetchLogs();
  fetchLogsSeveritys();
  fetchLogsServices();
});
</script>

<style lang="scss" scoped>
.log-container {
  display: flex;
  .search-container {
    min-width: 260px;
    max-height: 650px;
    border: #eee 1px solid;
    background: #ffffff;
    padding: 15px 15px 25px 15px;
    margin-right: 10px;
    z-index: 99;
    top: 186px;
    left: 20px;
  }

  .tabel-container {
    flex: 1;
    min-width: calc(100% - 270px);
  }
}
.action-link {
  color: #0064c8;
  cursor: pointer;
}
.input-group {
  margin-bottom: 10px;
}

.detail-table {
  width: 100%;
  max-width: 1200px;
  color: #606266;
  font-size: 14px;
  border: 1px solid #ebeef5;
  border-collapse: collapse;
}
.detail-table tr td {
  padding: 15px;
  border: 1px solid #ebeef5;
  // word-break: break-all;
}
.detail-table tr {
  width: 200px;
}
.detail-table tr td.label {
  background: #f5f7fa;
  width: 120px;
  min-width: 120px;
  word-break: break-all;
}
:deep(.el-drawer__header) {
  margin: 5px !important;
}
.detail-table .content-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
  gap: 8px;
}

.detail-table .content-text {
  flex: 1;
  word-break: break-all;
  min-width: 0; /* 关键属性，防止长文本溢出 */
}

.detail-table .action-link {
  flex-shrink: 0;
  white-space: nowrap;
  color: #0064c8;
  cursor: pointer;
  margin-left: auto; /* 确保始终靠右 */
}
</style>
