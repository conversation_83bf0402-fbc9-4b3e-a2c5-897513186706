import java from "@/assets/icons/java.svg";
import tomcat from "@/assets/icons/Tomcat.svg";
import clickhouse from "@/assets/icons/clickhouse.svg";
import kafka from "@/assets/icons/kafka.svg";
import mysql from "@/assets/icons/mysql.svg";
import redis from "@/assets/icons/redis.svg";
import jetty from "@/assets/icons/jetty.svg";
import mongodb from "@/assets/icons/mongodb.svg";
import undertow from "@/assets/icons/undertow.svg";
import netty from "@/assets/icons/netty.svg";
import apache_dubbo from "@/assets/icons/apache_dubbo.svg";
import rabbitmq from "@/assets/icons/rabbitmq.svg";
import jms from "@/assets/icons/jms.svg";
import elasticsearch from "@/assets/icons/elasticsearch.svg";
import centos from "@/assets/icons/centos.svg";
import ubuntu from "@/assets/icons/ubuntu.svg";

const iconMap: Map<string, any> = new Map<string, any>([
  ["java", java],
  ["tomcat", tomcat],
  ["clickhouse", clickhouse],
  ["kafka", kafka],
  ["mysql", mysql],
  ["redis", redis],
  ["mongodb", mongodb],
  ["jetty", jetty],
  ["undertow", undertow],
  ["netty", netty],
  ["apache_dubbo", apache_dubbo],
  ["apache-dubbo", apache_dubbo],
  ["rabbitmq", rabbitmq],
  ["jms", jms],
  ["elasticsearch", elasticsearch],
  ["CentOS Linux", centos],
  ["Ubuntu", ubuntu]
]);
export default iconMap;
