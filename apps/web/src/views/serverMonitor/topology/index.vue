<template>
  <div
    class="topo-wrapper"
    ref="container"
    style="position: relative; height: 100%"
    v-loading="loading"
  >
    <div id="container" ref="container"></div>
  </div>
</template>

<script setup lang="ts">
import G6 from "@antv/g6";
import { getTopology, Entity, Links, Nodes } from "@/api/service/topology";
import { callChainServices } from "@/api/service/callChain";
import defaultSvg from "@/assets/icons/default.svg";
import { applicationStore } from "@/store/modules/application";
import { serviceNameStore } from "@/store/modules/service";
import { formatNums } from "@/utils/formatStr";
import { useRouter } from "vue-router";
import iconMap from "./topoSvgMap";

const useServiceNameStore = serviceNameStore();
const router = useRouter();
const useApplicationStore = applicationStore();
const container = ref<HTMLElement>();
const loading = ref(false);
const selectedServiceName = ref("");
const serviceNameOptions = ref<{ value: string; label: string }[]>([]);
const topologyData = ref<Entity>({
  nodes: [] as Nodes[],
  links: [] as Links[]
});

function initG6Data() {
  // 创建 Tooltip 实例
  const tooltip = new G6.Tooltip({
    // 获取内容的方法，返回一个 HTML 元素或字符串
    getContent(e: any): HTMLDivElement | string {
      const outDiv = document.createElement("div");
      if (e.item.getType() === "node") {
        const model = e.item.getModel();
        const times = formatNums(model.performanceContent.requestCount);
        outDiv.innerHTML = `
        <div style="font-size:14px;padding:10px 8px;box-shadow:rgb(174, 174, 174) 0px 0px 10px">
         <div style="margin:0px 0px 4px 0px">类型：${model.performanceContent.product}</div>
          <div style="margin:0px 0px 4px 0px">请求数：${times.fixValue}${times.unit}</div>
          <div style="margin:0px 0px 4px 0px">错误数：${model.performanceContent.errorCount}</div>
          <div style="margin:0px 0px 4px 0px">错误率：${model.performanceContent.errorRate}%</div>
          <div style="margin:0px 0px 4px 0px">响应时间：${model.performanceContent.avgDuration}ms</div>
        </div>
      `;
        return outDiv;
      } else if (e.item.getType() === "edge") {
        const model = e.item.getModel();
        const times = formatNums(model.performanceContent.requestCount);
        outDiv.innerHTML = `
        <div style="font-size:14px;padding:10px 8px;box-shadow:rgb(174, 174, 174) 0px 0px 10px">
          <div style="margin:0px 0px 4px 0px">请求次数：${times.fixValue}${times.unit}</div>
          <div style="margin:0px 0px 4px 0px">错误数：${model.performanceContent.errorCount}</div>
          <div style="margin:0px 0px 4px 0px">错误率：${model.performanceContent.errorRate}%</div>
          <div style="margin:0px 0px 4px 0px">响应时间：${model.performanceContent.avgDuration}ms</div>
        </div>
      `;
        return outDiv;
      }
      // 如果不匹配任何类型，返回空字符串以隐藏 tooltip
      return "";
    },
    // 可选：设置偏移
    offsetX: 10,
    offsetY: -140,
    containerClassName: "g6-tooltip"
  });
  const toolbar = new G6.ToolBar({
    getContent: () => {
      if (topologyData.value.nodes) {
        return `
        <div style="display: flex; align-items: center; gap: 10px;">
          <div style="display: flex; align-items: center; gap: 5px;">
            <span style="font-size: 12px; color: #666;">服务名称：</span>
            <select id="serviceNameSelect" style="padding: 2px 5px; border: 1px solid #ccc; border-radius: 3px; font-size: 12px; min-width: 120px;">
              <option value="">全部服务</option>
              ${serviceNameOptions.value
                .map(
                  option =>
                    `<option value="${option.value}" ${selectedServiceName.value === option.value ? "selected" : ""}>${option.label}</option>`
                )
                .join("")}
            </select>
          </div>
          <li class="text-center mr-6px inline-block leading-6 border-1" code='refresh'>还原</li>
          <li class="text-center inline-block leading-6" code='unloadCanvas'>下载</li>
        </div>
        `;
      }
      return `<div style="display:none"></div>`;
    },
    handleClick: (code, graph: any) => {
      if (code === "refresh") {
        graph.clear();
        // 重新加载或获取拓扑图数据
        const newData = handleTopoData(); // 获取新的拓扑数据
        graph.changeData(newData); // 重新渲染新的拓扑图数据
        graph.render();
      } else if (code === "unloadCanvas") {
        const currentZoom = graph.getZoom();
        graph.zoomTo(initialZoom);
        graph.downloadFullImage("tree-graph", "image/png", {
          backgroundColor: "#ffffff",
          padding: [30, 15, 15, 15]
        });
        graph.zoomTo(currentZoom);
      } else {
        // 其他操作保持默认不变
        toolbar.handleDefaultOperator(code);
      }
    }
  });

  const width = container.value && container.value!.scrollWidth;
  const height = container.value && container.value.scrollHeight;
  const graph = new G6.Graph({
    container: container.value as HTMLElement,
    width: width,
    height: height,
    // controlPoints: false,
    plugins: [tooltip, toolbar],
    modes: {
      default: ["drag-canvas", "drag-node", "zoom-canvas"]
    },
    // fitView: true,
    fitCenter: true,
    layout: {
      type: "forceAtlas2",
      preventOverlap: true,
      // kr: handleTopoData().nodes.length > 30 ? 600 : 200,
      kr: 600,
      prune: false,
      center: [250, 300],
      nodeSize: [40, 40]
      // maxIterations: 300,
      // workerEnabled: true,
      // hub: true
    },
    defaultNode: {
      /* node type */
      type: "circle",
      /* node size */
      size: [70],
      /* style for the keyShape */
      style: {
        // fill: "#ffffff",
        // stroke: "#e00000",
        // stroke: "#009431",
        lineWidth: 4,
        cursor: "pointer"
      },
      labelCfg: {
        /* label's position, options: center, top, bottom, left, right */
        position: "bottom",
        offset: 12,
        /* label's offset to the keyShape, 4 by default */
        //   offset: 12,
        /* label's style */
        style: {
          fontSize: 16
          // fill: "#ccc",
          // fontWeight: 500
        }
      },
      /* configurations for four linkpoints */
      /* icon configuration */
      icon: {
        /* whether show the icon, false by default */
        show: true,
        /* icon's img address, string type */
        // img: java,
        /* icon's size, 20 * 20 by default: */
        width: 40,
        height: 40,
        center: "center",
        cursor: "pointer"
      }
    },
    defaultEdge: {
      size: 1,
      color: "#808080",
      style: {
        endArrow: true,
        cursor: "pointer"
      },
      labelCfg: {
        // autoRotate: true,
        style: {
          fill: "#808080",
          fontSize: 16,
          cursor: "pointer"
        }
      }
    }
  });
  const data: any = handleTopoData();
  graph.data(data);
  // s

  const initialZoom = graph.getZoom();

  let menu: HTMLDivElement | null = null; // 菜单
  let tooltipVisible = true; // 控制 tooltip 的显示和隐藏
  graph.on("afterchange", () => {
    if (graph.getNodes().length > 0) {
      // 当有数据时，显示 toolbar
      graph.addPlugin(toolbar);
    } else {
      // 没有数据时隐藏 toolbar
      graph.removePlugin(toolbar);
    }
  });
  graph.on("node:click", function (evt) {
    const target: any = evt.item;
    const type = target.getModel();
    tooltipVisible = false;
    tooltip.hide();
    const { x, y } = graph.getClientByPoint(type.x, type.y);
    if (menu) {
      container.value!.removeChild(menu);
    }
    // 生成菜单栏
    menu = document.createElement("div");
    menu.style.position = "absolute";
    menu.style.left = `${x + 20}px`; // 调整菜单栏的水平位置
    menu.style.top = `${y - 130}px`;
    menu.style.width = `108px`;
    menu.style.backgroundColor = "#fff";
    menu.style.border = "1px solid #ccc";
    menu.style.borderRadius = "3px";
    menu.style.fontSize = "14px";
    menu.style.padding = "3px 10px 3px 10px";
    menu.style.boxShadow = "0px 0px 10px rgba(0, 0, 0, 0.1)";
    menu.style.textAlign = "center";
    const menuItems = [
      { label: "查看详情", onClick: () => handleMenuItemClick("service-detail", type) },
      { label: "实例监测", onClick: () => handleMenuItemClick("example-mointor", type) }
    ];
    menuItems.forEach(item => {
      const menuItem = document.createElement("div");
      menuItem.className = "topo-menu-item";
      menuItem.style.cursor = "pointer";
      menuItem.textContent = item.label;
      menuItem.style.margin = "10px 0px 8px 0px";
      menuItem.addEventListener("click", item.onClick);
      menu && menu.appendChild(menuItem);
    });
    if (container.value) {
      container.value.appendChild(menu);
    }
    // 点击菜单每一项

    // 监听鼠标点击事件以关闭菜单
    const handleClickOutside = (event: MouseEvent) => {
      if (menu && !menu.contains(event.target as Node)) {
        if (container.value) {
          container.value.removeChild(menu);
        }
        menu = null; // 清空菜单引用
        tooltipVisible = true;
        document.removeEventListener("mousedown", handleClickOutside);
      }
    };

    // 添加点击事件监听
    document.addEventListener("mousedown", handleClickOutside);
  });
  // tooltip 显示隐藏
  graph.on("node:mouseenter", evt => {
    if (tooltipVisible) {
      tooltip.updatePosition(evt);
    } else {
      tooltip.hide();
    }
  });
  graph.on("node:mouseenter", evt => {
    const node = evt.item as any;
    const edges = node?.getEdges(); // 获取与该节点相关的所有边

    edges?.forEach((edge: any) => {
      graph.updateItem(edge, {
        style: {
          stroke: "#337ab7",
          lineWidth: 2
        },
        labelCfg: {
          style: {
            fill: "#337ab7"
          }
        }
      });
    });
  });

  graph.on("node:mouseleave", evt => {
    const node = evt.item as any;
    const edges = node?.getEdges(); // 获取与该节点相关的所有边

    edges?.forEach((edge: any) => {
      graph.updateItem(edge, {
        style: {
          stroke: "#808080", // 恢复为原来的颜色
          lineWidth: 1
        },
        labelCfg: {
          style: {
            fill: "#808080"
          }
        }
      });
    });
  });

  graph.on("edge:mouseenter", evt => {
    const edge: any = evt.item;
    graph.updateItem(edge, {
      style: {
        stroke: "#337ab7",
        lineWidth: 2
      },
      labelCfg: {
        style: {
          fill: "#337ab7"
        }
      }
    });
  });

  // 监听边移出事件
  graph.on("edge:mouseleave", evt => {
    const edge: any = evt.item;
    graph.updateItem(edge, {
      style: {
        stroke: "#808080", // 恢复为原来的颜色
        lineWidth: 1
      },
      labelCfg: {
        style: {
          fill: "#808080"
        }
      }
    });
  });

  setTimeout(() => {
    graph.changeData(handleTopoData()); // 重新渲染新的拓扑图数据
    graph.render();
    loading.value = false; // 在图形渲染完成后关闭loading

    // 添加下拉框事件监听
    const serviceNameSelect = document.getElementById("serviceNameSelect");
    if (serviceNameSelect) {
      serviceNameSelect.addEventListener("change", async (e: any) => {
        selectedServiceName.value = e.target.value;
        loading.value = true; // 开始筛选时显示loading
        try {
          // 重新获取拓扑数据
          await getTopologyData();
          // 重新渲染图形
          graph.clear();
          const newData = handleTopoData();
          graph.changeData(newData);
          graph.render();
        } catch (error) {
          console.error("筛选数据失败:", error);
        } finally {
          loading.value = false; // 无论成功还是失败都关闭loading
        }
      });
    }
  }, 100);
}
const handleMenuItemClick = (label: string, type: any) => {
  useServiceNameStore.setServiceName(type.id); // 使用id（原始服务名称）
  useServiceNameStore.setAlias(type.alias || ""); // 存储别名
  if (label === "service-detail") {
    router.push("/dashboard/overview/service-overview");
  } else {
    router.push("/dashboard/overview/instance-monitor");
  }
};

// 获取服务名称列表
async function getServiceNameList() {
  try {
    const res = await callChainServices({});
    if (res.code === 0 && res.records) {
      serviceNameOptions.value = res.records.map((item: string) => ({
        value: item,
        label: item
      }));
    }
  } catch (error) {
    console.error("获取服务名称列表失败:", error);
  }
}

async function getTopologyData() {
  const parasm = {
    appid: useApplicationStore.appId || "",
    serviceName: selectedServiceName.value || undefined
  };
  try {
    await getTopology(parasm).then(res => {
      if (res.code === 0) {
        topologyData.value = res.entity || {};
      }
    });
  } catch (error) {
    console.log(error);
    throw error; // 重新抛出错误，让调用方处理
  }
}
function handleTopoData() {
  const nodes =
    topologyData.value?.nodes?.map(item => ({
      id: item.name,
      label: item.alias ? `${item.name}（${item.alias}）` : item.name,
      alias: item.alias, // 新增alias字段
      icon: {
        img: handleIcon(item.product)
      },
      style: {
        fill: "#ffffff",
        stroke: item.errorCount > 0 ? "#e00000" : "#337ab7",
        lineWidth: 2
      },
      performanceContent: {
        requestCount: item.requestCount,
        errorRate: item.errorRate,
        errorCount: item.errorCount,
        avgDuration: item.avgDuration,
        product: item.product
      }
    })) || [];

  const links = topologyData.value?.links || [];
  const relationships: any = {};
  links.forEach(link => {
    const { src, dst } = link;
    if (!relationships[src]) {
      relationships[src] = new Set();
    }
    relationships[src].add(dst);
  });
  const edges = links.map(item => {
    const { src, dst } = item;
    const isBidirectional = relationships[dst]?.has(src);

    return {
      source: src,
      target: dst,
      type: isBidirectional ? "quadratic" : "",
      label: item.avgDuration + "ms",
      style: {
        endArrow: true
      },
      performanceContent: {
        requestCount: item.requestCount,
        errorRate: item.errorRate,
        errorCount: item.errorCount,
        avgDuration: item.avgDuration
      }
    };
  });

  return {
    nodes: nodes,
    edges: edges
  };
}
function handleIcon(productIcon: string) {
  // 过滤出匹配的项
  return iconMap.get(productIcon) || defaultSvg;
}

onMounted(async () => {
  loading.value = true; // 页面初始化时显示loading
  try {
    await getServiceNameList();
    await getTopologyData();
    if (container.value) {
      initG6Data();
    }
  } catch (error) {
    console.error("页面初始化失败:", error);
    loading.value = false; // 异常情况下关闭loading
  }
});
</script>

<style scoped>
:deep(.g6-component-tooltip) {
  padding: 0;
  box-shadow: none;
}
.topo-wrapper {
  background-color: #ffffff; /* 网格背景色 */
  background-image: linear-gradient(90deg, rgba(0, 0, 0, 0.1) 1px, transparent 0.5px),
    linear-gradient(180deg, rgba(0, 0, 0, 0.1) 1px, transparent 0.5px);
  background-size: 30px 30px; /* 网格的大小 */
}
.topo-menu-item:hover {
  color: aqua;
}
.topo-menu-item {
  color: aqua !important;
}
:deep(.g6-component-toolbar) {
  text-align: center;
  display: flex;
  justify-content: space-between;
  align-items: center;
  top: 28px;
  left: 40px;
  /* background: #808080;
  color: #fff; */
}

:deep(.g6-component-toolbar select) {
  background: #fff;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  color: #606266;
  cursor: pointer;
  transition: border-color 0.2s;
}

:deep(.g6-component-toolbar select:hover) {
  border-color: #c0c4cc;
}

:deep(.g6-component-toolbar select:focus) {
  border-color: #409eff;
  outline: none;
}
/* .g6-tooltip {
  position: fixed;
} */
</style>
