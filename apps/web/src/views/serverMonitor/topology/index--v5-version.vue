<template>
  <div id="container" style="width: 100%; height: 800px" v-loading="loading"></div>
</template>

<script setup lang="ts">
import { Graph, Icon, treeToGraphData } from "@antv/g6";
import { getTopology, Entity, Links, Nodes } from "@/api/service/topology";
import defaultSvg from "@/assets/icons/default.svg";
import { applicationStore } from "@/store/modules/application";
import { serviceNameStore } from "@/store/modules/service";
import { formatNums } from "@/utils/formatStr";
import { useRouter } from "vue-router";
import iconMap from "./topoSvgMap";
import { iconProps } from "element-plus";
const loading = ref(false);
const topologyData = ref<Entity>({
  nodes: [] as Nodes[],
  links: [] as Links[]
});
const useServiceNameStore = serviceNameStore();
const router = useRouter();
const useApplicationStore = applicationStore();

onMounted(async () => {
  await getTopologyData();
  const graph = new Graph({
    container: "container",
    // autoFit: "view",
    data: handleTopoData(),
    behaviors: [
      "drag-canvas",
      "zoom-canvas",
      "drag-element",
      {
        type: "hover-activate",
        degree: 1, // 👈🏻 Activate relations.
        state: "highlight",
        inactiveState: "dim",
        onHover: (event: any) => {},
        onHoverEnd: (event: any) => {}
      }
    ],
    node: {
      style: {
        labelText: d => d.id
      }

      // animation: {
      //   enter: false
      // }
    },
    edge: {
      style: {
        endArrow: true,
        cursor: "pointer"
      },
      state: {
        highlight: {
          stroke: "#337ab7"
        }
      }
      // labelCfg: {
      //   // autoRotate: true,
      //   endArrow: true,
      //   style: {
      //     fill: "#808080",
      //     fontSize: 16,
      //     cursor: "pointer"
      //   }
      // }
    },
    plugins: [{ key: "grid-line", type: "grid-line", follow: false }],
    layout: {
      type: "force-atlas2",
      preventOverlap: true,
      // kr: handleTopoData()?.nodes.length > 1 ? 600 : 100,
      kr: handleTopoData()?.nodes.length > 40 ? 600 : 300,
      // dissuadeHubs: true
      center: [250, 250]
    }
    // autoResize: true,
    // zoomRange: [0.1, 5]
  });
  // graph.removeNodeData(["Modeling Methods"]);
  graph.render();
  const hideRelatedNodesAndEdges = (nodeId: string) => {
    const node = graph.getNodeData(nodeId);
    console.log(node, "node::");
  };
  hideRelatedNodesAndEdges("Modeling Methods");
});
function handleIcon(productIcon: string) {
  // 过滤出匹配的项
  return iconMap.get(productIcon) || defaultSvg;
}
function handleTopoData() {
  const nodes =
    topologyData.value?.nodes?.map(item => ({
      id: item.name,
      label: item.name,
      type: "circle",
      style: {
        fill: "#ffffff",
        stroke: item.errorCount > 0 ? "#e00000" : "#337ab7",
        lineWidth: 2,
        size: 50,
        iconSrc: handleIcon(item.product)
      },
      state: {
        highlight: {
          stroke: "#337ab7"
        }
      },
      performanceContent: {
        requestCount: item.requestCount,
        errorRate: item.errorRate,
        errorCount: item.errorCount,
        avgDuration: item.avgDuration,
        product: item.product
      }
    })) || [];

  const links = topologyData.value?.links || [];
  const relationships: any = {};
  links.forEach(link => {
    const { src, dst } = link;
    if (!relationships[src]) {
      relationships[src] = new Set();
    }
    relationships[src].add(dst);
  });
  const edges = links.map(item => {
    const { src, dst } = item;
    const isBidirectional = relationships[dst]?.has(src);

    return {
      source: src,
      target: dst,
      type: isBidirectional ? "quadratic" : "",
      label: item.avgDuration + "ms",
      style: {
        endArrow: true
      },
      performanceContent: {
        requestCount: item.requestCount,
        errorRate: item.errorRate,
        errorCount: item.errorCount,
        avgDuration: item.avgDuration
      }
    };
  });
  return {
    nodes: nodes,
    edges: edges
  };
}

async function getTopologyData() {
  loading.value = true;
  const parasm = {
    appid: useApplicationStore.appId || ""
  };
  try {
    await getTopology(parasm).then(res => {
      topologyData.value = res.entity || {};
    });
    loading.value = false;
  } catch (error) {
    loading.value = false;
    console.log(error);
  }
}
</script>

<style scoped>
:deep(.g6-component-tooltip) {
  padding: 0;
  box-shadow: none;
}
.topo-wrapper {
  background-color: #ffffff; /* 网格背景色 */
  background-image: linear-gradient(90deg, rgba(0, 0, 0, 0.1) 1px, transparent 0.5px),
    linear-gradient(180deg, rgba(0, 0, 0, 0.1) 1px, transparent 0.5px);
  background-size: 30px 30px; /* 网格的大小 */
}
.topo-menu-item:hover {
  color: aqua;
}
.topo-menu-item {
  color: aqua !important;
}
:deep(.g6-component-toolbar) {
  text-align: center;
  display: flex;
  justify-content: space-between;
  align-items: center;
  top: 28px;
  left: 40px;
  /* background: #808080;
  color: #fff; */
}
</style>
