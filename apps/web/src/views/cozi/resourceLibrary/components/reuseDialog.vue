<template>
  <!-- 对话流创建弹窗 -->
  <el-dialog v-model="visible" width="600px" @close="handleCancel">
    <template #title>
      {{ dialogTitle1 }}
    </template>
    <!-- 表单区域 -->
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      label-position="top"
      style="width: 100%"
    >
      <!-- 图片上传 -->
      <el-form-item>
        <el-upload
          class="avatar-uploader"
          :show-file-list="false"
          @success="handleAvatarSuccess"
          @before-upload="beforeAvatarUpload"
        >
          <img v-if="formData.imageUrl" :src="formData.imageUrl" class="avatar" />
          <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
        </el-upload>
      </el-form-item>

      <!-- 对话流名称 -->
      <el-form-item :label="`${props.dialogTitle}名称`" prop="name">
        <!-- 修正：placeholder 语法错误，去掉多余大括号 -->
        <el-input
          v-model="formData.name"
          :placeholder="`请输入${props.dialogTitle}名称`"
          maxlength="30"
          show-word-limit
        />
      </el-form-item>

      <!-- 对话流描述 -->
      <el-form-item :label="`${props.dialogTitle}描述`" prop="desc">
        <el-input
          v-model="formData.desc"
          :placeholder="`请输入${props.dialogTitle}描述`"
          maxlength="600"
          show-word-limit
          type="textarea"
          :rows="4"
        />
      </el-form-item>
    </el-form>

    <!-- 底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleCreate">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, defineProps, defineEmits, computed } from "vue";
import {
  ElUpload,
  ElInput,
  ElForm,
  ElFormItem,
  ElButton,
  ElIcon,
  type FormInstance,
  type FormRules,
  type UploadProps,
  ElMessage // 新增：导入 ElMessage
} from "element-plus";
import { Plus } from "@element-plus/icons-vue"; // 明确导入图标

// 接收父组件的属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  dialogTitle: {
    type: String,
    default: ""
  }
});

// 发射事件到父组件
const emit = defineEmits([
  "update:visible", // 关闭弹窗
  "onCreate" // 提交表单数据
]);

// 同步弹窗显示状态（props 是只读的，必须用 ref 中转）
const visible = ref(props.visible);
watch(
  () => props.visible,
  newVal => {
    visible.value = newVal;
    // 关闭时重置表单
    if (!newVal) {
      formData.value = { name: "", desc: "", imageUrl: "" };
    }
  }
);

// 表单数据
const formRef = ref<FormInstance>();
const formData = ref({
  name: "", // 名称
  desc: "", // 描述
  imageUrl: "" // 上传图片预览地址
});

const dialogTitle1 = computed(() => (props.dialogTitle ? `创建${props.dialogTitle}` : `创建`));

// 2. 动态计算表单规则（响应式，确保校验提示跟随标题变化）
const formRules = computed<FormRules>(() => ({
  name: [
    {
      required: true,
      message: `请输入${props.dialogTitle}名称`, // 动态提示
      trigger: "blur"
    },
    { max: 30, message: "最多30个字符", trigger: "blur" }
  ],
  desc: [{ max: 600, message: "最多600个字符", trigger: "blur" }]
}));

// 上传成功回调
const handleAvatarSuccess: UploadProps["onSuccess"] = (_, uploadFile) => {
  if (uploadFile.raw) {
    formData.value.imageUrl = URL.createObjectURL(uploadFile.raw);
  }
};

// 上传前校验（格式、大小）
const beforeAvatarUpload: UploadProps["beforeUpload"] = rawFile => {
  if (rawFile.type !== "image/jpeg") {
    ElMessage.error("仅支持 JPG 格式图片！");
    return false;
  } else if (rawFile.size / 1024 / 1024 > 2) {
    ElMessage.error("图片大小不能超过 2MB！");
    return false;
  }
  return true;
};

// 取消按钮：关闭弹窗并通知父组件
const handleCancel = () => {
  visible.value = false;
  emit("update:visible", false);
};
watch(dialogTitle1, val => {
  console.log("dialogTitle1:", val);
});

// 确认按钮：校验表单并提交
const handleCreate = () => {
  formRef.value?.validate(isValid => {
    if (isValid) {
      emit("onCreate", formData.value); // 传递表单数据
      handleCancel();
    }
  });
};
</script>

<style scoped>
/* 图片上传样式 */
.avatar-uploader {
  width: 178px;
  height: 178px;
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
  display: inline-block; /* 确保和文字对齐 */
}

.avatar-uploader:hover {
  border-color: var(--el-color-primary);
}

.avatar {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 底部按钮对齐 */
.dialog-footer {
  text-align: right;
}
</style>
