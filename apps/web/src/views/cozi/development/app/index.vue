<template>
  <div class="agent-editor">
    <div class="top-bar">
      <!-- 头像 hover 触发悬浮框 -->
      <div class="avatar-container" ref="avatarContainer">
        <div
          class="avatar-group flex items-center"
          @mouseenter="showProfile = true"
          @mouseleave="showProfile = false"
        >
          <el-avatar :size="40" class="ml-3">
            <img src="https://picsum.photos/60/60" alt="智能体头像" />
          </el-avatar>
          <span class="ml-2 text-lg font-semibold">{{ userList.name }}</span>
        </div>

        <div
          v-if="showProfile"
          class="profile-popup bg-white p-3 rounded shadow-md z-10 transition-all duration-200"
          @mouseenter="showProfile = true"
          @mouseleave="showProfile = false"
          ref="profilePopup"
        >
          <el-avatar :size="70" class="mb-2" />
          <p class="text-base font-semibold mt-5">{{ userList.name }}</p>
          <p class="text-sm text-gray-500 mt-5">{{ agentDesc }}</p>
          <p class="text-xs text-gray-400 mt-5">创建时间：{{ creationTime }}</p>
        </div>
        <div class="mode-selector ml-3" @click="handleEdit">
          <el-icon class="text-gray-500">
            <Edit />
          </el-icon>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="top-actions flex items-center mr-4 gap-2">
        <span style="font-weight: normal; font-size: 13px; color: #666"
          >草稿自动保存于14:29:38</span
        >
        <el-button
          type="primary"
          size="large"
          class="ml-3"
          style="width: 70px"
          @click="handleRelease"
          >发布</el-button
        >
      </div>
    </div>
  </div>
  <user-dialog
    ref="userDialogRef"
    v-model:visible="userDialog"
    :user-list="userList"
    @onConfirm="handleUserDialogConfirm"
  />
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, computed } from "vue";
// 导入固定数据
import {
  modeList,
  recommendCards,
  recommendGerenCards,
  modules,
  dialogConfig,
  initialMessages,
  replies
} from "./data";
import UserDialog from "./components/userDialog.vue";
const agentName = ref("ssss");
const agentDesc = ref("这是一个智能体的描述信息...");
const creationTime = ref("");
const currentMode = ref(modeList[0]);
const showProfile = ref(false);
const userDialog = ref(false);
const userList = reactive({
  name: "张三",
  desc: "这是一个智能体的描述信息..."
});
// 选择模式事件
const handleModeSelect = (mode: any) => {
  currentMode.value = mode;
  // 可扩展：触发父组件事件或API请求
  console.log("切换模式:", mode.label);
};
// 编辑事件
const handleEdit = () => {
  // ElMessage.success("编辑成功");
  // 编辑弹窗
  userDialog.value = true;
};
const handleRelease = () => {
  ElMessage.success("发布成功");
};

const handleUserDialogConfirm = (data: any) => {
  agentName.value = data.name;
  agentDesc.value = data.desc;
  userDialog.value = false;
};
onMounted(() => {
  creationTime.value = new Date().toLocaleString(); // 初始化创建时间
});
</script>

<style lang="scss" scoped>
.agent-editor {
  height: 100vh;
  width: 100vw;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;

  // 顶部栏
  .top-bar {
    height: 60px;
    border-bottom: 1px solid #e5e7eb;
    background: #f7f7fc;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .avatar-container {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: space-evenly;
    }

    .avatar-group {
      cursor: pointer;
      padding: 10px 0;
    }

    .profile-popup {
      position: absolute;
      min-width: 300px;
      min-height: 300px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      border: 1px solid #e5e7eb;
      z-index: 100;
      top: 50px;
      left: 20px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-evenly;
    }

    .top-actions {
      .el-button {
        padding: 0 12px;
      }
    }
    .mode-selector {
      width: 360px;
      cursor: pointer;
      // 触发区样式
      .trigger-wrapper {
        border-color: #dcdcdc;
        transition: border-color 0.2s;

        &:hover {
          border-color: #999;
        }
      }

      // 下拉菜单自定义样式
      .custom-dropdown-menu {
        padding: 8px;
        min-width: 320px;
        border-radius: 6px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      }

      // 每个模式项
      .mode-item {
        padding: 8px 0;
        transition: background 0.2s;

        &:hover {
          background: #f5f7fa;
        }
      }

      // 图标颜色
      .text-primary {
        color: #409eff;
      }
    }
  }
}
</style>
