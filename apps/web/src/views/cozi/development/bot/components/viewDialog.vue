<template>
  <el-dialog v-model="visible" title="提示词库" width="800px" @close="handleClose">
    <!-- 顶部搜索和新建按钮 -->
    <div class="top-bar flex justify-between items-center mb-4">
      <div class="tabs">
        <span class="tab-item active" @click="switchTab('recommend')">推荐</span>
        <span class="tab-item" @click="switchTab('personal')">个人</span>
      </div>
      <div class="search-and-create flex items-center gap-3">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索"
          :prefix-icon="Search"
          size="small"
          class="search-input"
        />
        <el-button type="primary" size="small" @click="createNewPrompt">+ 新建提示词</el-button>
      </div>
    </div>

    <!-- 主体内容：左右分栏 -->
    <div class="main-content flex gap-4">
      <!-- 左侧提示词列表 -->
      <div class="prompt-list w-1/3 bg-gray-50 rounded-lg p-3 h-[400px] overflow-y-auto">
        <div
          v-for="prompt in filteredPrompts"
          :key="prompt.id"
          class="prompt-item p-3 mb-2 rounded-md cursor-pointer transition-all"
          :class="{ 'bg-blue-50 border-l-4 border-blue-500': selectedPromptId === prompt.id }"
          @click="selectPrompt(prompt.id)"
        >
          <h3 class="font-medium text-gray-800">{{ prompt.title }}</h3>
          <p class="text-sm text-gray-500 mt-1 line-clamp-2">{{ prompt.description }}</p>
        </div>
      </div>

      <!-- 右侧提示词详情 -->
      <div
        class="prompt-detail w-2/3 bg-white rounded-lg p-4 h-[400px] overflow-y-auto border border-gray-200"
      >
        <div v-if="selectedPrompt" class="prose max-w-none">
          <h3 class="text-lg font-semibold mb-2"># 角色：{{ selectedPrompt.role }}</h3>
          <p class="text-gray-600 mb-4">{{ selectedPrompt.roleDescription }}</p>

          <h3 class="text-lg font-semibold mb-2"># 目标：</h3>
          <p class="text-gray-600 mb-4">{{ selectedPrompt.goal }}</p>

          <h3 class="text-lg font-semibold mb-2"># 技能：</h3>
          <ul class="list-decimal list-inside mb-4 pl-2">
            <li v-for="skill in selectedPrompt.skills" :key="skill.id" class="text-gray-600">
              {{ skill }}
            </li>
          </ul>

          <h3 class="text-lg font-semibold mb-2"># 工作流：</h3>
          <ol class="list-decimal list-inside mb-4 pl-2">
            <li v-for="(step, index) in selectedPrompt.workflow" :key="index" class="text-gray-600">
              {{ step }}
            </li>
          </ol>

          <h3 class="text-lg font-semibold mb-2"># 输出格式：</h3>
          <p class="text-gray-600 mb-4">{{ selectedPrompt.outputFormat }}</p>

          <h3 class="text-lg font-semibold mb-2"># 限制：</h3>
          <ul class="list-disc list-inside mb-4 pl-2">
            <li v-for="(limit, index) in selectedPrompt.limits" :key="index" class="text-gray-600">
              {{ limit }}
            </li>
          </ul>
        </div>
        <div v-else class="flex items-center justify-center h-full text-gray-400">
          请选择一个提示词模板
        </div>
      </div>
    </div>

    <!-- 底部操作按钮 -->
    <div class="bottom-buttons flex justify-end gap-3 mt-4">
      <el-button size="small" @click="copyPrompt">复制提示词</el-button>
      <el-button type="primary" size="small" @click="insertPrompt">插入提示词</el-button>
    </div>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, watch, defineProps, defineEmits, computed } from "vue";
import { Search } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';

// 1. 接收父组件传递的初始显示状态
const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false
  }
});

// 2. 子组件内部维护本地状态
const visible = ref(props.dialogVisible);
const currentTab = ref("recommend");
const searchKeyword = ref("");
const selectedPromptId = ref<string | null>(null);

// 3. 模拟提示词数据
const recommendPrompts = ref([
  {
    id: "1",
    title: "通用结构",
    description: "适用于多种场景的提示词结构，可以根据具体需求增删对应模块",
    role: "角色名称",
    roleDescription: "角色概述和主要职责的一句话描述",
    goal: "角色的工作目标，如果有多目标可以分点列出，但建议聚焦1-2个目标",
    skills: [
      "为了实现目标，角色需要具备的技能1",
      "为了实现目标，角色需要具备的技能2",
      "为了实现目标，角色需要具备的技能3"
    ],
    workflow: ["描述角色工作流程的第一步", "描述角色工作流程的第二步", "描述角色工作流程的第三步"],
    outputFormat: "如果对角色的输出格式有特定要求，可以在这里强调并举例说明想要的输出格式",
    limits: [
      "描述角色在互动过程中需要遵循的限制条件1",
      "描述角色在互动过程中需要遵循的限制条件2",
      "描述角色在互动过程中需要遵循的限制条件3"
    ]
  },
  {
    id: "2",
    title: "任务执行",
    description: "适用于有明确的工作步骤的任务执行场景，通过明确每一步的工作要求...",
    role: "任务执行者",
    roleDescription: "负责按照指定步骤完成特定任务的角色",
    goal: "高效、准确地完成指定任务，确保每一步都符合要求",
    skills: ["具备清晰的逻辑思维能力", "能够严格按照流程执行任务", "具备问题解决能力"],
    workflow: [
      "接收任务并明确任务要求",
      "制定详细的执行计划",
      "按照计划逐步执行任务",
      "检查任务完成情况并提交结果"
    ],
    outputFormat: "以结构化报告形式输出，包含任务完成情况、遇到的问题及解决方案",
    limits: ["必须严格按照指定流程执行", "遇到问题及时汇报", "确保任务在规定时间内完成"]
  },
  {
    id: "3",
    title: "角色扮演",
    description: "适用于聊天陪伴、互动娱乐场景，可帮助模型轻松塑造个性化的人物角色...",
    role: "聊天伙伴",
    roleDescription: "负责与用户进行友好、有趣的对话交流",
    goal: "提供愉快的聊天体验，回应用户的问题和需求",
    skills: ["具备良好的沟通能力", "了解各种话题，能够进行多角度交流", "具备幽默感和同理心"],
    workflow: [
      "热情问候用户",
      "积极回应用户的话题",
      "根据用户兴趣拓展对话内容",
      "保持对话的连贯性和趣味性"
    ],
    outputFormat: "自然、口语化的对话风格，避免过于正式的表达",
    limits: ["保持友好、尊重的态度", "避免涉及敏感话题", "积极引导对话向健康、积极的方向发展"]
  },
  {
    id: "4",
    title: "技能调用",
    description: "适用于获取特定技能的场景，可精准调用模型的专业能力...",
    role: "技能专家",
    roleDescription: "具备特定专业技能，能够为用户提供专业指导和帮助",
    goal: "为用户提供专业、准确的技能指导和解决方案",
    skills: ["具备扎实的专业知识", "能够清晰地解释复杂概念", "具备解决实际问题的能力"],
    workflow: [
      "了解用户的问题和需求",
      "分析问题的核心和关键点",
      "提供专业的解决方案",
      "解答用户的疑问"
    ],
    outputFormat: "结构化、条理清晰的解答，包含必要的理论解释和实际示例",
    limits: [
      "确保提供的信息准确、可靠",
      "对于不确定的问题，如实告知用户",
      "避免提供错误或误导性的信息"
    ]
  },
  {
    id: "5",
    title: "基于知识库回答",
    description: "适用于客服等基于特定知识库回答的场景",
    role: "知识顾问",
    roleDescription: "基于特定知识库为用户提供准确、专业的回答",
    goal: "为用户提供基于知识库的准确、全面的回答",
    skills: ["熟悉知识库内容", "具备快速检索和提取信息的能力", "能够清晰、准确地传达信息"],
    workflow: [
      "理解用户的问题",
      "从知识库中检索相关信息",
      "整合信息并形成回答",
      "向用户提供准确、清晰的回答"
    ],
    outputFormat: "准确、简洁的回答，必要时提供引用来源",
    limits: [
      "回答必须基于知识库内容，不得编造信息",
      "对于超出知识库范围的问题，如实告知用户",
      "确保回答准确、客观，避免主观臆断"
    ]
  }
]);

const personalPrompts = ref([
  {
    id: "101",
    title: "自定义模板",
    description: "用户自定义的提示词模板",
    role: "自定义角色",
    roleDescription: "用户自定义的角色描述",
    goal: "用户自定义的目标",
    skills: ["用户自定义的技能1", "用户自定义的技能2"],
    workflow: ["用户自定义的步骤1", "用户自定义的步骤2"],
    outputFormat: "用户自定义的输出格式",
    limits: ["用户自定义的限制条件1"]
  }
]);

// 4. 计算属性：根据当前标签和搜索关键词过滤提示词
const filteredPrompts = computed(() => {
  const prompts = currentTab.value === "recommend" ? recommendPrompts.value : personalPrompts.value;
  if (!searchKeyword.value) return prompts;
  return prompts.filter(
    prompt =>
      prompt.title.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
      prompt.description.toLowerCase().includes(searchKeyword.value.toLowerCase())
  );
});

// 5. 计算属性：当前选中的提示词
const selectedPrompt = computed(() => {
  if (!selectedPromptId.value) return null;
  const allPrompts = [...recommendPrompts.value, ...personalPrompts.value];
  return allPrompts.find(prompt => prompt.id === selectedPromptId.value);
});

// 6. 监听父组件prop变化，同步到本地状态
watch(
  () => props.dialogVisible,
  newVal => {
    visible.value = newVal;
  }
);

// 7. 定义发射给父组件的事件
const emit = defineEmits(["update:dialogVisible", "onInsertPrompt"]);

// 8. 事件处理函数
const handleClose = () => {
  visible.value = false;
  emit("update:dialogVisible", false);
};

const switchTab = (tab: string) => {
  currentTab.value = tab;
  selectedPromptId.value = null; // 切换标签时清除选中状态
};

const selectPrompt = (id: string) => {
  selectedPromptId.value = id;
};

const copyPrompt = () => {
  if (!selectedPrompt.value) {
    ElMessage({
      message: "请先选择一个提示词",
      type: "warning"
    });
    return;
  }
  // 构建提示词文本
  const promptText = `# 角色：${selectedPrompt.value.role}
${selectedPrompt.value.roleDescription}

# 目标：
${selectedPrompt.value.goal}

# 技能：
${selectedPrompt.value.skills.map((skill, index) => `${index + 1}. ${skill}`).join("\n")}

# 工作流：
${selectedPrompt.value.workflow.map((step, index) => `${index + 1}. ${step}`).join("\n")}

# 输出格式：
${selectedPrompt.value.outputFormat}

# 限制：
${selectedPrompt.value.limits.map((limit, index) => `- ${limit}`).join("\n")}`;

  // 复制到剪贴板
  if (navigator.clipboard) {
    navigator.clipboard
      .writeText(promptText)
      .then(() => {
        ElMessage({
          message: "提示词已复制到剪贴板",
          type: "success"
        });
      })
      .catch(err => {
        console.error("无法复制文本: ", err);
        ElMessage({
          message: "复制失败，请手动复制",
          type: "error"
        });
      });
  } else {
    // 兼容性回退
    const textarea = document.createElement('textarea');
    textarea.value = promptText;
    document.body.appendChild(textarea);
    textarea.select();
    document.execCommand('copy');
    document.body.removeChild(textarea);
    ElMessage({
      message: "提示词已复制到剪贴板",
      type: "success"
    });
  }
};

const insertPrompt = () => {
  if (!selectedPrompt.value) return;
  // 构建提示词文本
  const promptText = `# 角色：${selectedPrompt.value.role}
${selectedPrompt.value.roleDescription}

# 目标：
${selectedPrompt.value.goal}

# 技能：
${selectedPrompt.value.skills.map((skill, index) => `${index + 1}. ${skill}`).join("\n")}

# 工作流：
${selectedPrompt.value.workflow.map((step, index) => `${index + 1}. ${step}`).join("\n")}

# 输出格式：
${selectedPrompt.value.outputFormat}

# 限制：
${selectedPrompt.value.limits.map((limit, index) => `- ${limit}`).join("\n")}`;

  // 通知父组件插入提示词
  emit("onInsertPrompt", promptText);
  handleClose();
};

const createNewPrompt = () => {
  // 这里可以打开新建提示词的弹窗
  ElMessage({
    message: "新建提示词功能待实现",
    type: "info"
  });
};
</script>

<style scoped>
/* 标签样式 */
.tabs {
  display: flex;
  gap: 20px;
}

.tab-item {
  padding: 5px 0;
  cursor: pointer;
  position: relative;
  font-size: 14px;
  color: #666;
}

.tab-item.active {
  color: #409eff;
  font-weight: 500;
}

.tab-item.active::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #409eff;
}

/* 搜索框样式 */
.search-input {
  width: 200px;
}

/* 提示词列表样式 */
.prompt-list {
  scrollbar-width: thin;
  scrollbar-color: #d1d5db #f3f4f6;
}

.prompt-list::-webkit-scrollbar {
  width: 6px;
}

.prompt-list::-webkit-scrollbar-thumb {
  background-color: #d1d5db;
  border-radius: 3px;
}

.prompt-list::-webkit-scrollbar-track {
  background-color: #f3f4f6;
}

.prompt-item {
  transition: all 0.2s ease;
}

.prompt-item:hover {
  background-color: #f9fafb;
}

/* 提示词详情样式 */
.prompt-detail {
  scrollbar-width: thin;
  scrollbar-color: #d1d5db #f3f4f6;
}

.prompt-detail::-webkit-scrollbar {
  width: 6px;
}

.prompt-detail::-webkit-scrollbar-thumb {
  background-color: #d1d5db;
  border-radius: 3px;
}

.prompt-detail::-webkit-scrollbar-track {
  background-color: #f3f4f6;
}

.prose h3 {
  margin-top: 1rem;
  margin-bottom: 0.5rem;
  color: #1f2937;
}

.prose p {
  margin-bottom: 1rem;
  color: #4b5563;
}

.prose ul,
.prose ol {
  margin-bottom: 1rem;
  padding-left: 1.5rem;
}

.prose li {
  margin-bottom: 0.25rem;
}

/* 按钮样式 */
.bottom-buttons {
  padding-top: 10px;
  border-top: 1px solid #f0f0f0;
}
</style>
