// 智能体编辑页面固定数据

// 模式列表
export const modeList = [
  {
    value: "auto",
    label: "单 Agent（自主规划模式）",
    desc: "用户与大模型进行对话，由一个大模型自主思考决策，适用于较为简单的业务逻辑。"
  },
  {
    value: "chat",
    label: "单 Agent（对话流模式）",
    desc: "该智能体会严格按照对话流编排的流程进行执行，支持保留多轮历史对话记录，适用于结构化或有明确流程的任务。"
  },
  {
    value: "multi",
    label: "多 Agent（合作模式）",
    desc: "多个智能体合作完成任务，每个智能体负责不同的任务，适用于复杂的业务场景。"
  },
  {
    value: "custom",
    label: "自定义模式",
    desc: "用户可以自定义智能体的行为和功能，适用于特殊需求的场景。"
  }
];

// 推荐提示词卡片
export const recommendCards = [
  {
    title: "通用结构",
    description: "适用于多种场景的提示词结构，可以根据具体需求增删对应模块",
    icon: "fa-cubes",
    color: "#3b82f6"
  },
  {
    title: "任务执行",
    description: "适用于有明确的工作步骤的任务执行场景，通过明确每一步的工作要求...",
    icon: "fa-tasks",
    color: "#10b981"
  },
  {
    title: "角色扮演",
    description: "适用于聊天陪伴、互动娱乐场景，可帮助模型轻松塑造个性化的人物角色...",
    icon: "fa-user-circle",
    color: "#f59e0b"
  },
  {
    title: "技能调用",
    description: "适用于获取特定技能的场景，可精准调用模型的专业能力...",
    icon: "fa-magic",
    color: "#8b5cf6"
  },
  {
    title: "信息提取",
    description: "适用于从文本中提取结构化信息的场景，可帮助模型自动识别和提取关键信息...",
    icon: "fa-extract",
    color: "#ec4899"
  },
  {
    title: "情感分析",
    description: "适用于分析文本情感的场景，可帮助模型判断文本的情感倾向...",
    icon: "fa-smile-o",
    color: "#f59e0b"
  }
];

// 个人提示词卡片
export const recommendGerenCards = [
  {
    id: 1,
    title: "项目规划助手",
    desc: "帮助规划和管理软件开发项目",
    icon: "el-icon-sitemap",
    color: "#8B5CF6"
  },
  {
    id: 2,
    title: "需求分析工具",
    desc: "分析和梳理用户需求",
    icon: "el-icon-list",
    color: "#F43F5E"
  }
];

// 模块配置
export const modules = [
  {
    title: "技能",
    items: [
      {
        name: "plugin",
        title: "插件",
        desc: "插件能够让智能体调用外部 API，例如搜索信息、浏览网页、生成图片等，扩展智能体的能力和使用场景。"
      },
      {
        name: "workflow",
        title: "工作流",
        desc: "工作流支持通过可视化的方式，对插件、大语言模型、代码块等功能进行组合，从而实现复杂、稳定的业务流程编排。"
      }
    ]
  },
  {
    title: "知识",
    subtitle: "按需调用",
    items: [
      {
        name: "text",
        title: "文本",
        desc: "将文档、URL、三方数据源上传为文本知识库后，用户发送消息时，智能体能够引用文本知识中的内容回答问题。"
      },
      {
        name: "table",
        title: "表格",
        desc: "用户上传表格后，支持基于自然语言对数据库进行查询和计算。"
      },
      {
        name: "photo",
        title: "照片",
        desc: "照片上传后可添加语义描述，智能体可以基于描述匹配最合适的照片。"
      }
    ]
  },
  {
    title: "记忆",
    items: [
      {
        name: "variable",
        title: "变量",
        desc: "用于保存用户个人信息，让智能体记住用户的特征，使回复更加个性化。"
      },
      {
        name: "database",
        title: "数据库",
        desc: "以表格结构组织数据，可实现类似书签和图书管理等功能。"
        // badge: 1
      }
    ]
  },
  {
    title: "对话体验",
    items: [
      {
        name: "greeting",
        title: "开场白",
        desc: "开场白"
      },
      {
        name: "feedback",
        title: "用户问题建议",
        desc: "在智能体回复后，自动根据对话内容提供 3 条用户提问建议",
        switch: true
      },
      {
        name: "shortcut",
        title: "快捷指令",
        desc: "快捷指令是对话输入框上方的按钮，配置完成后，用户可以快速发起预设对话"
      },
      {
        name: "background",
        title: "背景图片",
        desc: "为你的智能体在扣子智能体商店增加对话背景图片，对话时更加沉浸"
      }
    ]
  }
];

// 弹窗配置数据
export const dialogConfig = {
  plugin: {
    title: "添加插件",
    buttonText: "创建插件",
    items: [
      { id: 1, name: "代码分析插件", description: "提供代码质量分析和优化建议" },
      { id: 2, name: "性能监控插件", description: "监控应用性能并生成报告" },
      { id: 3, name: "安全检测插件", description: "检测代码中的安全漏洞" },
      { id: 4, name: "自动部署插件", description: "实现自动化部署流程" },
      { id: 5, name: "日志分析插件", description: "分析应用日志并提供见解" }
    ]
  },
  workflow: {
    title: "添加工作流",
    buttonText: "创建工作流",
    items: [
      { id: 1, name: "CI/CD工作流", description: "持续集成和持续部署工作流" },
      { id: 2, name: "测试工作流", description: "自动化测试流程" },
      { id: 3, name: "构建工作流", description: "应用构建和打包流程" }
    ]
  },
  text: {
    title: "添加文本知识",
    buttonText: "创建文本知识",
    items: [
      { id: 1, name: "产品文档", description: "产品使用说明书和功能介绍" },
      { id: 2, name: "API文档", description: "应用程序接口参考文档" },
      { id: 3, name: "用户手册", description: "详细的用户操作指南" }
    ]
  },
  table: {
    title: "添加表格知识",
    buttonText: "创建表格知识",
    items: [
      { id: 1, name: "销售数据", description: "产品销售统计和分析数据" },
      { id: 2, name: "用户数据", description: "用户信息和行为分析数据" }
    ]
  },
  photo: {
    title: "添加照片知识",
    buttonText: "上传照片",
    items: [
      { id: 1, name: "产品图片", description: "产品相关的图片资料" },
      { id: 2, name: "场景图片", description: "应用场景相关的图片" },
      { id: 3, name: "图表图片", description: "数据可视化图表图片" }
    ]
  },
  variable: {
    title: "添加变量",
    buttonText: "创建变量",
    items: [
      { id: 1, name: "用户名称", description: "存储用户的姓名信息" },
      { id: 2, name: "用户偏好", description: "存储用户的偏好设置" },
      { id: 3, name: "系统参数", description: "存储系统配置参数" }
    ]
  },
  greeting: {
    title: "设置开场白",
    buttonText: "保存设置",
    items: [
      { id: 1, name: "默认问候", description: "标准的问候语" },
      { id: 2, name: "专业问候", description: "专业风格的问候语" },
      { id: 3, name: "友好问候", description: "亲切友好的问候语" }
    ]
  },
  shortcut: {
    title: "添加快捷指令",
    buttonText: "创建指令",
    items: [
      { id: 1, name: "帮助中心", description: "快速查看帮助信息" },
      { id: 2, name: "功能介绍", description: "查看智能体功能介绍" },
      { id: 3, name: "联系我们", description: "获取联系支持的方式" }
    ]
  },
  background: {
    title: "设置背景图片",
    buttonText: "应用背景",
    items: [
      { id: 1, name: "专业商务", description: "专业商务风格的背景图片" },
      { id: 2, name: "科技感", description: "具有科技感的背景图片" },
      { id: 3, name: "简约风格", description: "简约干净的背景图片" }
    ]
  }
};

// 初始聊天消息
export const initialMessages = [
  {
    type: "receive",
    content: "您好！我是智能助手，有什么可以帮助您的吗？",
    timestamp: new Date().toISOString()
  }
];

// AI回复模板
export const replies = [
  "感谢您的提问！我可以帮您解答关于智能体开发的各种问题。",
  "这个问题很有意思。让我为您详细解释一下...",
  "我理解您的需求了。这是我的建议...",
  "您的问题涉及到多个方面，让我们逐一分析...",
  "很高兴能帮到您！如果还有其他问题，请随时提问。"
];
