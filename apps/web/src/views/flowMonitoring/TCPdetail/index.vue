<template>
  <div class="detail-container">
    <TitlecCom :title="detailtop"></TitlecCom>
    <MyTable
      :data="summaryCount"
      style="height: 150px"
      class="mb-15px"
      v-loading="tabelListLoading"
    >
      <my-column width="150" property="ip1" label="客户端IP" />
      <my-column width="150" property="port1" label="端口" />
      <my-column width="150" property="bytes_2to1" label="字节数">
        <template v-slot="{ row }">
          <span>
            {{ convertBytes(row.bytes_2to1).fixValue }}
            {{ convertBytes(row.bytes_2to1).unit || "" }}
          </span>
        </template>
      </my-column>
      <my-column width="150" property="packets_2to1" label="数据包数" />
      <my-column width="150" property="ip2" label="服务端IP" />
      <my-column width="150" property="port2" label="端口" />
      <my-column width="150" property="bytes_1to2" label="字节数">
        <template v-slot="{ row }">
          <span>
            {{ convertBytes(row.bytes_1to2).fixValue }}
            {{ convertBytes(row.bytes_1to2).unit || "" }}
          </span>
        </template>
      </my-column>
      <my-column width="150" property="packets_1to2" label="数据包数" />
      <my-column width="150" property="packets" label="总数据包数" />
      <my-column width="150" property="bytes" label="总字节数">
        <template v-slot="{ row }">
          <span>
            {{ convertBytes(row.bytes).fixValue }}
            {{ convertBytes(row.bytes).unit || "" }}
          </span>
        </template>
      </my-column>
      <my-column width="150" property="protocol" label="协议" />
      <my-column width="200" property="start_ts" label="开始时间" />
      <my-column width="200" property="last_ts" label="结束时间" />
      <my-column width="150" property="syn_count" label="连接请求计数" />
      <my-column width="150" property="rst_count" label="连接重置计数" />
      <my-column width="150" property="fin_count" label="连接关闭计数" />
    </MyTable>
    <TitlecCom :title="detailstatus"></TitlecCom>
    <div class="detail-container-bottom">
      <div class="chart-container" @scroll="handleScroll">
        <table>
          <tr style="height: 30px; background-color: #f9f9f9">
            <th>{{ "序号" }}</th>
            <th>{{ IP1 }}</th>
            <th>{{ IP2 }}</th>
            <th>{{ "绝对时间" }}</th>
          </tr>
          <tr
            v-for="entry in tabelList"
            :key="entry.id"
            :style="{ 'background-color': entry.id === selectKeyVal ? '#f5f7fa' : '' }"
          >
            <td style="border: none; width: 20px">{{ entry.id }}</td>
            <!-- 入向数据 -->
            <el-tooltip
              v-if="entry.direction === 2"
              class="box-item"
              effect="dark"
              :hide-after="0"
              placement="top"
            >
              <template #content>
                <div v-html="generateEntryHTML(entry)"></div>
              </template>
              <td style="border: none">
                <div>
                  <span
                    class="tabel-item"
                    style="
                      background: #c8c8c8;
                      overflow: hidden;
                      text-overflow: ellipsis;
                      white-space: nowrap;
                    "
                    @click="jsonType(entry)"
                    v-html="generateEntryHTML(entry)"
                  ></span>
                  <el-icon style="color: #c8c8c8; font-weight: bold; font-size: 23px">
                    <Right />
                  </el-icon>
                </div>
              </td>
            </el-tooltip>
            <td v-else style="border: none"></td>
            <!-- 出向数据 -->
            <el-tooltip
              v-if="entry.direction === 1"
              class="box-item"
              effect="dark"
              :hide-after="0"
              placement="top"
            >
              <template #content>
                <div v-html="generateEntryHTML(entry)"></div>
              </template>
              <td style="border: none">
                <div>
                  <el-icon style="color: #efd066; font-weight: bold; font-size: 23px">
                    <Back />
                  </el-icon>
                  <span
                    class="tabel-item"
                    style="
                      background: #efd066;
                      overflow: hidden;
                      text-overflow: ellipsis;
                      white-space: nowrap;
                    "
                    @click="jsonType(entry)"
                    v-html="generateEntryHTML(entry)"
                  ></span>
                </div>
              </td>
            </el-tooltip>
            <td v-else style="border: none"></td>
            <!-- 时间数据 -->
            <el-tooltip
              class="box-item"
              effect="dark"
              :hide-after="0"
              :content="formatTimestamp(entry.timestamp)"
              placement="top"
            >
              <td :style="{ background: entry.direction === 2 ? '#cbddf1' : '#e9f0f8' }">
                <span class="tabel-time-item">
                  {{ formatTimestamp(entry.timestamp) }}
                </span>
              </td>
            </el-tooltip>
          </tr>
          <!-- 显示所有数据提示 -->
          <tr v-if="!toScroll">
            <td colspan="4">
              <span style="color: #444444">{{
                tabelList.length > 0 ? "已显示全部数据" : "未查询到数据"
              }}</span>
            </td>
          </tr>
        </table>
      </div>
      <div class="base-container">
        <!-- <Uint8Array /> -->
        <div
          class="array-container"
          ref="arrayContainer"
          @mousedown="startDrag"
          @mousemove="dragging"
          @mouseup="endDrag"
        >
          <div
            v-for="(row, rowIndex) in groupedData"
            :key="rowIndex"
            class="row"
            :ref="el => (rows[rowIndex] = el)"
          >
            <!-- 偏移量 -->
            <span
              class="offset"
              @click="toggleHighlightRow(rowIndex)"
              :class="{ highlighted: highlightedRows.includes(rowIndex) }"
            >
              {{ getOffset(rowIndex) }}
            </span>
            <!-- 数据字节 -->
            <span
              v-for="byteIndex in groupSize"
              :key="byteIndex"
              class="array-item"
              @click="toggleHighlight(rowIndex * groupSize + (byteIndex - 1))"
              :class="{
                highlighted: highlightedIndexes.includes(rowIndex * groupSize + (byteIndex - 1))
              }"
              :ref="el => (bytes[rowIndex * groupSize + (byteIndex - 1)] = el)"
            >
              {{
                row[byteIndex - 1] !== undefined
                  ? row[byteIndex - 1].toString(16).toUpperCase().padStart(2, "0")
                  : ""
              }}
            </span>
          </div>
        </div>

        <!-- 原始 JSON 数据区域 -->
        <div
          class="json-container"
          ref="jsonContainer"
          @mousedown="startDrag"
          @mousemove="dragging"
          @mouseup="endDrag"
        >
          <span
            v-for="(char, index) in jsonFields"
            :key="index"
            :ref="el => (jsonFieldsRefs[index] = el)"
            :class="{ highlighted: highlightedFields.includes(index) }"
            @click="toggleJsonFieldHighlight(index)"
          >
            {{ char }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, nextTick } from "vue";
import TitlecCom from "@/components/TitleCom/index.vue";
import { useRoute } from "vue-router";
import { getDetail, getTraffic } from "@/api/flow/detail";
import { serviceTimeStore } from "@/store/modules/global";
import { formatTimestamp } from "@/utils/dateStr";
import { convertBytes } from "@/utils/trafficStr";
const route = useRoute();
const detailtop = ref("TCP会话详情");
const detailstatus = ref("连接状态");

// 时序图列表
const tabelList = ref([]);

// TCP会话详情
const tabelListLoading = ref(false);
const IP1 = ref("");
const IP2 = ref("");
const serTimeStore = serviceTimeStore();
const getDetailVal = async () => {
  try {
    tabelListLoading.value = true;
    const params = {
      startTime: receivedParams.value.start,
      endTime: receivedParams.value.end,
      segmentId: receivedParams.value.segment_id,
      sessionId: receivedParams.value.session_id
    };
    const res = await getDetail(params);
    if (res.code === 0) {
      IP1.value = res.entity.ip1;
      IP2.value = res.entity.ip2;
      summaryCount.value = [
        {
          ...res.entity,
          start_ts: formatTimestamp(res.entity.start_ts),
          last_ts: formatTimestamp(res.entity.last_ts)
        }
      ];
    }
    tabelListLoading.value = false;
  } catch (err) {
    tabelListLoading.value = false;
    console.error(err);
  }
};

// 数据包详情
const getPacket = async record => {
  tabelLoading.value = true;
  try {
    uint8ArrayFromJson.value = record.data;
    tabelLoading.value = false;
  } catch (err) {
    tabelLoading.value = false;
    console.error(err);
  }
};

// 时序图
const chartPage = ref(1);
const chartPage_size = ref(20);
const toScroll = ref(true);
const getTabelList = async () => {
  tabelLoading.value = true;
  try {
    const params = {
      startTime: receivedParams.value.start,
      endTime: receivedParams.value.end,
      segmentId: receivedParams.value.segment_id,
      sessionId: receivedParams.value.session_id,
      page: chartPage.value,
      row: chartPage_size.value
    };

    const res = await getTraffic(params);

    if (res.code === 0 && res.records.length > 0) {
      toScroll.value = true;
      if (uint8ArrayFromJson.value.length === 0) {
        getPacket(res.records[0]);
      }
      if (!tabelList.value.length > 0) {
        tabelList.value = res.records.map((item, index) => ({
          ...item,
          id: index + 1
        }));
        if (tabelList.value.length < 15) {
          toScroll.value = false;
        }
      } else if (tabelList.value.length > 0) {
        const currentLength = tabelList.value.length;
        tabelList.value.push(
          ...res.records.map((item, index) => ({
            ...item,
            id: currentLength + index + 1
          }))
        );
      }
    } else {
      toScroll.value = false;
    }

    tabelLoading.value = false;
  } catch (err) {
    tabelLoading.value = false;
    console.error(err);
  }
};

const generateEntryHTML = entry => {
  return `
     荷载长度 = ${entry.length}, window = ${entry.window}<br />
    Seq = ${entry.seq}, Ack = ${entry.ack}
  `;
};

// TCP会话详情
const summaryCount = ref([]);

// 滚动加载
const tabelLoading = ref(false);
const handleScroll = event => {
  const container = event.target;
  const scrollTop = container.scrollTop;
  const scrollHeight = container.scrollHeight;
  const clientHeight = container.clientHeight;

  if (scrollTop + clientHeight >= scrollHeight - 350 && !tabelLoading.value && toScroll.value) {
    chartPage.value = chartPage.value + 1;
    getTabelList();
  }
};
const receivedParams = ref({
  segment_id: "",
  session_id: "",
  start: "",
  end: ""
});

onMounted(() => {
  receivedParams.value.segment_id = route.query.segment_id || "";
  receivedParams.value.session_id = route.query.session_id || "";
  receivedParams.value.start = route.query.start || "";
  receivedParams.value.end = route.query.end || "";
  getDetailVal();
  getTabelList();
});
const selectKeyVal = ref(null);
const jsonType = typ => {
  highlightedRows.value = [];
  highlightedFields.value = [];
  highlightedIndexes.value = [];
  getPacket(typ);
  selectKeyVal.value = typ.id;
};

const uint8ArrayFromJson = ref([]);
// 生成 Uint8Array 的分组数据
const groupSize = 14;
const groupedData = computed(() => {
  const result = [];
  const uint8Array = uint8ArrayFromJson.value;
  for (let i = 0; i < uint8Array.length; i += groupSize) {
    const group = uint8Array.slice(i, i + groupSize);
    result.push(group);
  }

  return result;
});

// 原始数据的 ASCII 字符列表
const jsonFields = computed(() => {
  const byteValues = Array.from(uint8ArrayFromJson.value);

  const jsonString = byteValues
    .map(byte => {
      const char = byte <= 32 || byte >= 127 ? "." : String.fromCharCode(byte);
      return char;
    })
    .join("");

  return jsonString;
});

// 偏移量计算
function getOffset(rowIndex) {
  return (rowIndex * groupSize).toString(16).toUpperCase().padStart(8, "0");
}

// 高亮行和字段的索引
const highlightedIndexes = ref([]);
const highlightedFields = ref([]);
const highlightedRows = ref([]);

// 切换选中状态
const arrayContainer = ref(null);
const rows = ref([]);
const bytes = ref([]);

const jsonContainer = ref(null);
const jsonFieldsRefs = ref([]);

// 更新滚动函数
function scrollToElement(element, container) {
  if (element && container) {
    container.scrollTo({
      top: element.offsetTop - container.offsetTop - 200,
      behavior: "smooth"
    });
  }
}

let dragStartIndex = null;
let dragEndIndex = null;
let isDragging = false;
let draggingType = null;

function startDrag(event) {
  const byteElement = event.target.closest(".array-item");
  const jsonFieldElement = event.target.closest(".json-container span");

  if (byteElement) {
    // 处理字节拖拽
    const byteIndex = Array.from(byteElement.parentNode.children).indexOf(byteElement);
    const rowIndex = Array.from(byteElement.parentNode.parentNode.children).indexOf(
      byteElement.parentNode
    );
    dragStartIndex = rowIndex * groupSize + byteIndex;
    draggingType = "byte";
    isDragging = true;
  } else if (jsonFieldElement) {
    // 处理 JSON 字段拖拽
    const fieldIndex = Array.from(jsonFieldElement.parentNode.children).indexOf(jsonFieldElement);
    dragStartIndex = fieldIndex;
    draggingType = "jsonField";
    isDragging = true;
  }
}

function dragging(event) {
  if (!isDragging || dragStartIndex === null) return;

  const byteElement = event.target.closest(".array-item");
  const jsonFieldElement = event.target.closest(".json-container span");

  if (draggingType === "byte" && byteElement) {
    // 处理字节拖拽
    const byteIndex = Array.from(byteElement.parentNode.children).indexOf(byteElement);
    const rowIndex = Array.from(byteElement.parentNode.parentNode.children).indexOf(
      byteElement.parentNode
    );
    dragEndIndex = rowIndex * groupSize + byteIndex;
    highlightRange(dragStartIndex, dragEndIndex);
  } else if (draggingType === "jsonField" && jsonFieldElement) {
    // 处理 JSON 字段拖拽
    const fieldIndex = Array.from(jsonFieldElement.parentNode.children).indexOf(jsonFieldElement);
    dragEndIndex = fieldIndex;
    highlightRange(dragStartIndex + 1, dragEndIndex + 1);
  }
}

function endDrag() {
  isDragging = false;
  dragStartIndex = null;
  dragEndIndex = null;
  draggingType = null;
}

function highlightRange(startIndex, endIndex) {
  const minIndex = Math.min(startIndex, endIndex) - 1;
  const maxIndex = Math.max(startIndex, endIndex) - 1;

  highlightedIndexes.value = [];
  for (let i = minIndex; i <= maxIndex; i++) {
    highlightedIndexes.value.push(i);
  }

  updateHighlightedRowsAndFields(minIndex, maxIndex);
}

function updateHighlightedRowsAndFields(minIndex, maxIndex) {
  highlightedRows.value = [];
  highlightedFields.value = [];
  const jsonString = JSON.stringify(jsonFields.value);
  const mappingIndex = minIndex % jsonString.length;

  for (let i = minIndex; i <= maxIndex; i++) {
    const rowIndex = Math.floor(i / groupSize);
    const fieldIndex = i % jsonString.length;
    if (!highlightedRows.value.includes(rowIndex)) {
      highlightedRows.value.push(rowIndex);
    }
    if (draggingType === "byte") {
      // 处理字节拖拽
      if (!highlightedRows.value.includes(rowIndex)) {
        highlightedRows.value.push(rowIndex);
      }

      // 确保字节拖拽时，JSON 字段也会高亮
      if (!highlightedFields.value.includes(fieldIndex)) {
        highlightedFields.value.push(fieldIndex);
        const jsonFieldElement = jsonFieldsRefs.value[mappingIndex];
        scrollToElement(jsonFieldElement, jsonContainer.value);
      }
    } else if (draggingType === "jsonField") {
      // 处理 JSON 字段拖拽
      if (!highlightedFields.value.includes(fieldIndex)) {
        highlightedFields.value.push(fieldIndex);
        const jsonFieldElement = bytes.value[mappingIndex];
        scrollToElement(jsonFieldElement, arrayContainer.value);
      }
    }
  }
}

function toggleHighlight(index) {
  const idx = highlightedIndexes.value.indexOf(index);
  const rowIndex = Math.floor(index / groupSize);
  highlightedIndexes.value = [];
  highlightedFields.value = [];
  highlightedRows.value = [];

  if (idx === -1) {
    highlightedIndexes.value.push(index);

    const jsonString = JSON.stringify(jsonFields.value);
    const mappingIndex = index % jsonString.length;
    if (!highlightedFields.value.includes(mappingIndex)) {
      highlightedFields.value.push(mappingIndex);
    }

    if (!highlightedRows.value.includes(rowIndex)) {
      highlightedRows.value.push(rowIndex);
    }

    nextTick(() => {
      const jsonFieldElement = jsonFieldsRefs.value[mappingIndex];
      scrollToElement(jsonFieldElement, jsonContainer.value);
    });
  } else {
    highlightedIndexes.value.splice(idx, 1);

    const jsonString = JSON.stringify(jsonFields.value);
    const mappingIndex = index % jsonString.length;
    const fieldIdx = highlightedFields.value.indexOf(mappingIndex);
    if (fieldIdx !== -1) {
      highlightedFields.value.splice(fieldIdx, 1);
    }

    const allBytesDeselected = groupedData.value[rowIndex].every(
      (_, byteIndex) => !highlightedIndexes.value.includes(rowIndex * groupSize + byteIndex)
    );
    if (allBytesDeselected) {
      const rowIdx = highlightedRows.value.indexOf(rowIndex);
      if (rowIdx !== -1) {
        highlightedRows.value.splice(rowIdx, 1);
      }
    }
  }
}

function toggleHighlightRow(rowIndex) {
  const idx = highlightedRows.value.indexOf(rowIndex);

  if (idx === -1) {
    highlightedRows.value.push(rowIndex);
    for (let i = 0; i < groupSize; i++) {
      const byteIndex = rowIndex * groupSize + i;
      if (!highlightedIndexes.value.includes(byteIndex)) {
        highlightedIndexes.value.push(byteIndex);
      }
    }
    const jsonString = JSON.stringify(jsonFields.value);
    for (let i = 0; i < groupSize; i++) {
      const mappingIndex = (rowIndex * groupSize + i) % jsonString.length;
      if (!highlightedFields.value.includes(mappingIndex)) {
        highlightedFields.value.push(mappingIndex);
      }
    }

    nextTick(() => {
      const firstFieldIndex = (rowIndex * groupSize) % jsonString.length;
      const jsonFieldElement = jsonFieldsRefs.value[firstFieldIndex];
      scrollToElement(jsonFieldElement, jsonContainer.value);
    });
  } else {
    highlightedRows.value.splice(idx, 1);
    for (let i = 0; i < groupSize; i++) {
      const byteIndex = rowIndex * groupSize + i;
      const byteIdx = highlightedIndexes.value.indexOf(byteIndex);
      if (byteIdx !== -1) {
        highlightedIndexes.value.splice(byteIdx, 1);
      }
    }
    const jsonString = JSON.stringify(jsonFields.value);
    for (let i = 0; i < groupSize; i++) {
      const mappingIndex = (rowIndex * groupSize + i) % jsonString.length;
      const fieldIdx = highlightedFields.value.indexOf(mappingIndex);
      if (fieldIdx !== -1) {
        highlightedFields.value.splice(fieldIdx, 1);
      }
    }
  }
}

function toggleJsonFieldHighlight(fieldIndex) {
  const byteIndex = fieldIndex;

  const rowIndex = Math.floor(byteIndex / groupSize);
  const idx = highlightedFields.value.indexOf(fieldIndex);
  highlightedIndexes.value = [];
  highlightedFields.value = [];
  highlightedRows.value = [];
  if (idx === -1) {
    // 添加高亮
    highlightedFields.value.push(fieldIndex);

    if (!highlightedIndexes.value.includes(byteIndex)) {
      highlightedIndexes.value.push(byteIndex);
    }
    if (!highlightedRows.value.includes(rowIndex)) {
      highlightedRows.value.push(rowIndex);
    }

    nextTick(() => {
      const byteElement = bytes.value[byteIndex];
      scrollToElement(byteElement, arrayContainer.value);
    });
  } else {
    // 移除高亮
    highlightedFields.value.splice(idx, 1);

    const byteIdx = highlightedIndexes.value.indexOf(byteIndex);
    if (byteIdx !== -1) {
      highlightedIndexes.value.splice(byteIdx, 1);
    }

    const allFieldsDeselected = groupedData.value[rowIndex].every(
      (_, byteIndex) => !highlightedIndexes.value.includes(rowIndex * groupSize + byteIndex)
    );
    if (allFieldsDeselected) {
      const rowIdx = highlightedRows.value.indexOf(rowIndex);
      if (rowIdx !== -1) {
        highlightedRows.value.splice(rowIdx, 1);
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.indicator-container > *:not(:last-child) {
  margin-right: 8px;
  box-sizing: border-box;
}
.detail-container {
  width: 100%;
  height: 100%;
  padding: 15px;
}
.indicator-wrapper {
  padding: 26px;
  border: 1px solid rgba(225, 228, 233, 1);
  text-align: center;
  border-radius: 2px;
  width: 100%;
  &-end {
    font-size: 40px;
    font-weight: normal;
    font-weight: 500;
    display: flex;
    justify-content: space-evenly;
    flex-direction: column;
  }
  &-unit {
    font-size: 16px;
    font-weight: 400;
    color: #999999;
    margin-top: 5px;
    text-align: center;
    height: 18px;
  }
  &-desc {
    font-size: 35px;
  }
}
.text-lg {
  color: #999999;
  font-size: 18px;
  margin-top: 5px;
}
.detail-container-bottom {
  display: flex;
  justify-content: space-between;

  .chart-container {
    width: 54vw;
    height: 650px;
    overflow-y: auto;
    overflow-x: hidden;
    border: 1px solid #ddd;

    table {
      width: 100%;
      margin: auto;
      background-color: #fff;
    }
    th,
    td {
      text-align: center;
    }
    td {
      padding: 5px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      justify-content: space-between;
    }
    th {
      position: sticky;
      top: 0;
      background-color: #f9f9f9;
      font-size: 14px;
      font-weight: normal;
      color: #000;
      z-index: 20;
    }
    div {
      display: flex;
      align-items: center;
    }
    .tabel-item {
      width: 18.5vw;
      height: 100%;
      font-size: 15px;
      display: inline-block;
      position: relative;
      cursor: pointer;
    }
    .tabel-time-item {
      width: 8vw;
      height: 100%;
      display: inline-block;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .base-container {
    width: 44vw;
    height: 650px;
    overflow-y: auto;
    overflow-x: hidden;
    display: flex;
    user-select: none;
    gap: 5px;
    border: 1px solid rgba(225, 228, 233, 1);
    box-shadow: none;
    .selected-row {
      background-color: #f5f7fa;
    }
    .array-container {
      width: 60vw;
      background-color: #f4f4f4;
      // overflow-y: auto;
      overflow: scroll;
      scrollbar-width: none;
    }

    .row {
      display: flex;
      align-items: center;
    }

    .offset {
      width: 6vw;
      max-width: 13vw;
      height: 25px;
      text-align: center;
      font-weight: bold;
      line-height: 25px;
      color: #444;
      background-color: #e8e8e8;
      border-right: 1px solid #ccc;
      cursor: pointer;
    }

    .offset.highlighted {
      background-color: #cccbcb;
    }

    .array-item {
      cursor: pointer;
      transition: background-color 0.3s ease;
      text-align: center;
      min-width: 20px;
      font-size: 16px;
      display: inline-block;
      width: 25px;
      height: 25px;
      line-height: 25px;
    }

    .array-item:empty {
      background-color: transparent;
      border-color: transparent;
    }

    .json-container {
      width: 40vw;
      padding: 8px;
      background-color: #f9f9f9;
      overflow: scroll;
      scrollbar-width: none;
      span {
        display: inline-block;
        width: 20px;
        height: 22px;
        line-height: 22px;
        text-align: center;
        cursor: pointer;
      }
    }

    .highlighted {
      background-color: #cccbcb;
    }
  }
}
</style>
