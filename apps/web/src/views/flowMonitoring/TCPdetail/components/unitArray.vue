<template>
  <div
    class="array-container"
    ref="arrayContainer"
    @mousedown="startDrag"
    @mousemove="dragging"
    @mouseup="endDrag"
  >
    <div
      v-for="(row, rowIndex) in groupedData"
      :key="rowIndex"
      class="row"
      :ref="el => (rows[rowIndex] = el)"
    >
      <!-- 偏移量 -->
      <span
        class="offset"
        @click="toggleHighlightRow(rowIndex)"
        :class="{ highlighted: highlightedRows.includes(rowIndex) }"
      >
        {{ getOffset(rowIndex) }}
      </span>
      <!-- 数据字节 -->
      <span
        v-for="byteIndex in groupSize"
        :key="byteIndex"
        class="array-item"
        @click="toggleHighlight(rowIndex * groupSize + (byteIndex - 1))"
        :class="{
          highlighted: highlightedIndexes.includes(rowIndex * groupSize + (byteIndex - 1))
        }"
        :ref="el => (bytes[rowIndex * groupSize + (byteIndex - 1)] = el)"
      >
        {{
          row[byteIndex - 1] !== undefined
            ? row[byteIndex - 1].toString(16).toUpperCase().padStart(2, "0")
            : ""
        }}
      </span>
    </div>
  </div>

  <!-- 原始 JSON 数据区域 -->
  <div
    class="json-container"
    ref="jsonContainer"
    @mousedown="startDrag"
    @mousemove="dragging"
    @mouseup="endDrag"
  >
    <span
      v-for="(char, index) in jsonFields"
      :key="index"
      :ref="el => (jsonFieldsRefs[index] = el)"
      :class="{ highlighted: highlightedFields.includes(index) }"
      @click="toggleJsonFieldHighlight(index)"
    >
      {{ char }}
    </span>
  </div>
</template>

<script setup>
import { ref, computed, nextTick } from "vue";

// 生成随机的 jsonData
function generateRandomUUID() {
  return ([1e7] + -1e3 + -4e3 + -8e3 + -1e11).replace(/[018]/g, c =>
    (c ^ ((Math.random() * 16) >> (c / 4))).toString(16)
  );
}
function generateRandomJsonData(numItems) {
  const jsonData = [];

  for (let i = 0; i < numItems; i++) {
    jsonData.push({
      traceId: generateRandomUUID(),
      spanId: generateRandomUUID(),
      result: Math.floor(Math.random() * 10)
    });
  }

  return jsonData;
}
const jsonData = generateRandomJsonData(12);

// 原始数据列表
const jsonFields = computed(() => {
  return JSON.stringify(jsonData);
});

// 将 JSON 数据转为字符串再转为 Uint8Array
function jsonDataToUint8Array(jsonData) {
  const jsonString = JSON.stringify(jsonData);
  return new TextEncoder().encode(jsonString);
}

// 转换后的 Uint8Array
const uint8ArrayFromJson = jsonDataToUint8Array(jsonData);

// 生成 Uint8Array 的分组数据
const groupSize = 14;
const groupedData = computed(() => {
  const result = [];
  for (let i = 0; i < uint8ArrayFromJson.length; i += groupSize) {
    result.push(uint8ArrayFromJson.slice(i, i + groupSize));
  }
  return result;
});

// 偏移量计算
function getOffset(rowIndex) {
  return (rowIndex * groupSize).toString(16).toUpperCase().padStart(8, "0");
}

// 高亮行和字段的索引
const highlightedIndexes = ref([]);
const highlightedFields = ref([]);
const highlightedRows = ref([]);

// 切换选中状态
const arrayContainer = ref(null);
const rows = ref([]);
const bytes = ref([]);

const jsonContainer = ref(null);
const jsonFieldsRefs = ref([]);

// 更新滚动函数
function scrollToElement(element, container) {
  if (element && container) {
    container.scrollTo({
      top: element.offsetTop - container.offsetTop - 200,
      behavior: "smooth"
    });
  }
}

let dragStartIndex = null;
let dragEndIndex = null;
let isDragging = false;
let draggingType = null;

function startDrag(event) {
  const byteElement = event.target.closest(".array-item");
  const jsonFieldElement = event.target.closest(".json-container span");

  if (byteElement) {
    // 处理字节拖拽
    const byteIndex = Array.from(byteElement.parentNode.children).indexOf(byteElement);
    const rowIndex = Array.from(byteElement.parentNode.parentNode.children).indexOf(
      byteElement.parentNode
    );
    dragStartIndex = rowIndex * groupSize + byteIndex;
    draggingType = "byte";
    isDragging = true;
  } else if (jsonFieldElement) {
    // 处理 JSON 字段拖拽
    const fieldIndex = Array.from(jsonFieldElement.parentNode.children).indexOf(jsonFieldElement);
    dragStartIndex = fieldIndex;
    draggingType = "jsonField";
    isDragging = true;
  }
}

function dragging(event) {
  if (!isDragging || dragStartIndex === null) return;

  const byteElement = event.target.closest(".array-item");
  const jsonFieldElement = event.target.closest(".json-container span");

  if (draggingType === "byte" && byteElement) {
    // 处理字节拖拽
    const byteIndex = Array.from(byteElement.parentNode.children).indexOf(byteElement);
    const rowIndex = Array.from(byteElement.parentNode.parentNode.children).indexOf(
      byteElement.parentNode
    );
    dragEndIndex = rowIndex * groupSize + byteIndex;
    highlightRange(dragStartIndex, dragEndIndex);
  } else if (draggingType === "jsonField" && jsonFieldElement) {
    // 处理 JSON 字段拖拽
    const fieldIndex = Array.from(jsonFieldElement.parentNode.children).indexOf(jsonFieldElement);
    dragEndIndex = fieldIndex;
    highlightRange(dragStartIndex + 1, dragEndIndex + 1);
  }
}

function endDrag() {
  isDragging = false;
  dragStartIndex = null;
  dragEndIndex = null;
  draggingType = null;
}

function highlightRange(startIndex, endIndex) {
  const minIndex = Math.min(startIndex, endIndex) - 1;
  const maxIndex = Math.max(startIndex, endIndex) - 1;

  highlightedIndexes.value = [];
  for (let i = minIndex; i <= maxIndex; i++) {
    highlightedIndexes.value.push(i);
  }

  updateHighlightedRowsAndFields(minIndex, maxIndex);
}

function updateHighlightedRowsAndFields(minIndex, maxIndex) {
  highlightedRows.value = [];
  highlightedFields.value = [];
  const jsonString = JSON.stringify(jsonData);
  const mappingIndex = minIndex % jsonString.length;

  for (let i = minIndex; i <= maxIndex; i++) {
    const rowIndex = Math.floor(i / groupSize);
    const fieldIndex = i % jsonString.length;
    if (!highlightedRows.value.includes(rowIndex)) {
      highlightedRows.value.push(rowIndex);
    }
    if (draggingType === "byte") {
      // 处理字节拖拽
      if (!highlightedRows.value.includes(rowIndex)) {
        highlightedRows.value.push(rowIndex);
      }

      // 确保字节拖拽时，JSON 字段也会高亮
      if (!highlightedFields.value.includes(fieldIndex)) {
        highlightedFields.value.push(fieldIndex);
        const jsonFieldElement = jsonFieldsRefs.value[mappingIndex];
        scrollToElement(jsonFieldElement, jsonContainer.value);
      }
    } else if (draggingType === "jsonField") {
      // 处理 JSON 字段拖拽
      if (!highlightedFields.value.includes(fieldIndex)) {
        highlightedFields.value.push(fieldIndex);
        const jsonFieldElement = bytes.value[mappingIndex];
        scrollToElement(jsonFieldElement, arrayContainer.value);
      }
    }
  }
}

function toggleHighlight(index) {
  const idx = highlightedIndexes.value.indexOf(index);
  const rowIndex = Math.floor(index / groupSize);
  highlightedIndexes.value = [];
  highlightedFields.value = [];
  highlightedRows.value = [];

  if (idx === -1) {
    highlightedIndexes.value.push(index);

    const jsonString = JSON.stringify(jsonData);
    const mappingIndex = index % jsonString.length;
    if (!highlightedFields.value.includes(mappingIndex)) {
      highlightedFields.value.push(mappingIndex);
    }

    if (!highlightedRows.value.includes(rowIndex)) {
      highlightedRows.value.push(rowIndex);
    }

    nextTick(() => {
      const jsonFieldElement = jsonFieldsRefs.value[mappingIndex];
      scrollToElement(jsonFieldElement, jsonContainer.value);
    });
  } else {
    highlightedIndexes.value.splice(idx, 1);

    const jsonString = JSON.stringify(jsonData);
    const mappingIndex = index % jsonString.length;
    const fieldIdx = highlightedFields.value.indexOf(mappingIndex);
    if (fieldIdx !== -1) {
      highlightedFields.value.splice(fieldIdx, 1);
    }

    const allBytesDeselected = groupedData.value[rowIndex].every(
      (_, byteIndex) => !highlightedIndexes.value.includes(rowIndex * groupSize + byteIndex)
    );
    if (allBytesDeselected) {
      const rowIdx = highlightedRows.value.indexOf(rowIndex);
      if (rowIdx !== -1) {
        highlightedRows.value.splice(rowIdx, 1);
      }
    }
  }
}

function toggleHighlightRow(rowIndex) {
  const idx = highlightedRows.value.indexOf(rowIndex);

  if (idx === -1) {
    highlightedRows.value.push(rowIndex);
    for (let i = 0; i < groupSize; i++) {
      const byteIndex = rowIndex * groupSize + i;
      if (!highlightedIndexes.value.includes(byteIndex)) {
        highlightedIndexes.value.push(byteIndex);
      }
    }
    const jsonString = JSON.stringify(jsonData);
    for (let i = 0; i < groupSize; i++) {
      const mappingIndex = (rowIndex * groupSize + i) % jsonString.length;
      if (!highlightedFields.value.includes(mappingIndex)) {
        highlightedFields.value.push(mappingIndex);
      }
    }

    nextTick(() => {
      const firstFieldIndex = (rowIndex * groupSize) % jsonString.length;
      const jsonFieldElement = jsonFieldsRefs.value[firstFieldIndex];
      scrollToElement(jsonFieldElement, jsonContainer.value);
    });
  } else {
    highlightedRows.value.splice(idx, 1);
    for (let i = 0; i < groupSize; i++) {
      const byteIndex = rowIndex * groupSize + i;
      const byteIdx = highlightedIndexes.value.indexOf(byteIndex);
      if (byteIdx !== -1) {
        highlightedIndexes.value.splice(byteIdx, 1);
      }
    }
    const jsonString = JSON.stringify(jsonData);
    for (let i = 0; i < groupSize; i++) {
      const mappingIndex = (rowIndex * groupSize + i) % jsonString.length;
      const fieldIdx = highlightedFields.value.indexOf(mappingIndex);
      if (fieldIdx !== -1) {
        highlightedFields.value.splice(fieldIdx, 1);
      }
    }
  }
}

function toggleJsonFieldHighlight(fieldIndex) {
  const jsonString = JSON.stringify(jsonData);
  const byteIndex = fieldIndex;

  const rowIndex = Math.floor(byteIndex / groupSize);
  const idx = highlightedFields.value.indexOf(fieldIndex);
  highlightedIndexes.value = [];
  highlightedFields.value = [];
  highlightedRows.value = [];
  if (idx === -1) {
    // 添加高亮
    highlightedFields.value.push(fieldIndex);

    if (!highlightedIndexes.value.includes(byteIndex)) {
      highlightedIndexes.value.push(byteIndex);
    }
    if (!highlightedRows.value.includes(rowIndex)) {
      highlightedRows.value.push(rowIndex);
    }

    nextTick(() => {
      const byteElement = bytes.value[byteIndex];
      scrollToElement(byteElement, arrayContainer.value);
    });
  } else {
    // 移除高亮
    highlightedFields.value.splice(idx, 1);

    const byteIdx = highlightedIndexes.value.indexOf(byteIndex);
    if (byteIdx !== -1) {
      highlightedIndexes.value.splice(byteIdx, 1);
    }

    const allFieldsDeselected = groupedData.value[rowIndex].every(
      (_, byteIndex) => !highlightedIndexes.value.includes(rowIndex * groupSize + byteIndex)
    );
    if (allFieldsDeselected) {
      const rowIdx = highlightedRows.value.indexOf(rowIndex);
      if (rowIdx !== -1) {
        highlightedRows.value.splice(rowIdx, 1);
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.array-container {
  width: 60vw;
  padding: 8px 0;
  background-color: #f4f4f4;
  // overflow-y: auto;
  overflow: scroll;
  scrollbar-width: none;
}

.row {
  display: flex;
  align-items: center;
}

.offset {
  width: 6vw;
  max-width: 13vw;
  height: 22px;
  text-align: center;
  font-weight: bold;
  line-height: 22px;
  color: #444;
  background-color: #e8e8e8;
  border-right: 1px solid #ccc;
  cursor: pointer;
}

.offset.highlighted {
  background-color: #cccbcb;
}

.array-item {
  margin: 1px;
  border-radius: 3px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  text-align: center;
  min-width: 20px;
  font-size: 16px;
  display: inline-block;
  width: 25px;
  height: 20px;
  line-height: 20px;
}

.array-item:empty {
  background-color: transparent;
  border-color: transparent;
}

.json-container {
  width: 40vw;
  padding: 8px;
  background-color: #f9f9f9;
  overflow: scroll;
  scrollbar-width: none;
  span {
    display: inline-block;
    width: 20px;
    height: 22px;
    line-height: 22px;
    text-align: center;
    cursor: pointer;
  }
}

.highlighted {
  background-color: #cccbcb;
}
</style>
