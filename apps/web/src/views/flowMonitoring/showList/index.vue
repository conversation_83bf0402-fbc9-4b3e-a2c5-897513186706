<template>
  <div class="tb-header mt-20px">
    <div>
      <el-input
        style="width: 240px"
        placeholder="请输入关键字"
        v-model="pageParams.keyword"
        clearable
      ></el-input>
      <el-button
        type="primary"
        style="margin-left: 15px"
        :disabled="tableLoading"
        :icon="Search"
        @click="search"
        >搜索</el-button
      >
    </div>
    <div>
      <el-button class="mt-10px w-80px" type="primary" @click="() => (textareaDialog = true)"
        >新 增</el-button
      >
    </div>
  </div>
  <MyTable
    class="mt-20px"
    :data="list.records"
    :total="list.total"
    v-loading="tableLoading"
    style="width: 100%"
    @sizeChange="handleSizeChange"
    @currentChange="handleCurrentChange"
  >
    <my-column property="hostname" label="主机名" />
    <my-column property="IP" label="IPV4">
      <template #default="scope">
        <div>
          {{ scope.row.network[0]?.ip }}<br />
          {{ scope.row.network[1]?.ip }}<br />
          {{ scope.row.network[2]?.ip }}
        </div>
      </template>
    </my-column>

    <my-column property="MAC" label="MAC地址">
      <template #default="scope">
        <div>
          {{ scope.row.network[0]?.mac }}<br />
          {{ scope.row.network[1]?.mac }}<br />
          {{ scope.row.network[2]?.mac }}
        </div>
      </template>
    </my-column>
    <my-column property="allTraffice" label="总流量">
      <template v-slot="{ row }">
        <span>
          {{ convertBytes(row.bytes).fixValue + " " + convertBytes(row.bytes).unit }}
        </span>
      </template>
    </my-column>
    <my-column property="allDataScore" label="总数据包"
      ><template v-slot="{ row }">
        <span> {{ row.packets }} </span>
      </template>
    </my-column>
    <my-column property="created_at" label="注册时间"> </my-column>
    <my-column label="操作" align="center" header-align="center" fixed="right" width="200">
      <template #default="scope">
        <span class="operate" @click="showIPDialog(scope.row)">查看详情 </span>
        <span> / </span>
        <span class="operate" @click="deleteTd(scope.row)">删除</span>
      </template>
    </my-column>
  </MyTable>

  <el-dialog
    destroy-on-close
    v-model="textareaDialog"
    title="新增监视IP"
    class="text-left"
    width="500"
    :close-on-click-modal="false"
    :align-center="true"
    ><el-input v-model="textarea" disabled :rows="6" type="textarea" placeholder="" /><template
      #footer
    >
      <div class="dialog-footer">
        <el-button @click="handleCopy">复制</el-button>
      </div>
    </template>
  </el-dialog>

  <el-dialog
    v-model="IPDialog"
    title="选择要查看的IP"
    class="text-left"
    width="500"
    :show-close="false"
    :close-on-click-modal="false"
    :align-center="true"
  >
    <el-select placeholder="请选择IP" v-model="IPNum" clearable filterable>
      <el-option v-for="item in IPList" :key="item.value" :label="item.label" :value="item.value" />
    </el-select>

    <template #footer>
      <el-button @click="cancelSelect">取消</el-button>
      <el-button type="primary" @click="viewDetail">确认</el-button>
    </template>
  </el-dialog>
  <!-- 删除弹窗-->
  <el-dialog
    :align-center="true"
    v-model="deleteVisible"
    title="删除"
    width="500"
    :close-on-click-modal="false"
  >
    <span>确认要删除吗？</span>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="deleteVisible = false">取消</el-button>
        <el-button type="primary" @click="delCommit"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import MyTable from "@/components/table/my-table.vue";
import MyColumn from "@/components/table/my-column.vue";
import { getHostInfo, deleteValue } from "@/api/flow/detail";
import { useRouter } from "vue-router";
import { breadcrumbStore } from "@/store/modules/breadcurmb";
import { serviceNameStore } from "@/store/modules/service";
import { convertBytes } from "@/utils/trafficStr";
import { Search } from "@element-plus/icons-vue";
const useServiceNameStore = serviceNameStore();
const useBreadcrumbStore = breadcrumbStore();
import { applicationStore } from "@/store/modules/application";
import { debounce } from "lodash-es";
const useApplicationStore = applicationStore();
const router = useRouter();
const textareaDialog = ref(false);
const IPDialog = ref(false);
const deleteVisible = ref(false);
const textarea = ref(`#!/bin/bashe
acho "正在监测IP地址..."
ping -c 4 *******
echo "监测结束"`);

const tableLoading = ref(false); //表格loading
const IPList = ref([]);
const showIPDialog = (row: any) => {
  IPList.value = row.network.map(item => {
    return {
      value: item.ip,
      label: item.ip
    };
  });
  IPDialog.value = true;
};
const IPNum = ref("");
//列表参数
const pageParams = reactive({
  page: 1,
  rows: 10,
  appid: "",
  keyword: ""
});

const fallbackCopyTextToClipboard = (text: string) => {
  const textArea = document.createElement("textarea");
  textArea.value = text;
  textArea.style.position = "fixed";
  document.body.appendChild(textArea);
  textArea.select();
  try {
    document.execCommand("copy");
    ElMessage.success("复制成功");
  } catch (err) {
    ElMessage.error("复制失败");
  }
  document.body.removeChild(textArea);
};

const handleCopy = async () => {
  if (!textarea.value) {
    return;
  }

  if (!navigator.clipboard) {
    fallbackCopyTextToClipboard(textarea.value);
    return;
  }

  try {
    await navigator.clipboard.writeText(textarea.value);
    ElMessage.success("复制成功");
    textareaDialog.value = false;
  } catch (error) {
    ElMessage.error("复制失败");
  }
};

const viewDetail = () => {
  if (IPNum.value) {
    const MACVal = list.records
      .map(item => {
        const entry = item.network.find(entry => entry.ip === IPNum.value);
        return entry ? entry.mac : null;
      })
      .find(mac => mac !== null);
    useServiceNameStore.setBreadcrumb(MACVal);
    useServiceNameStore.setServiceName(IPNum.value);
    useServiceNameStore.$patch(state => {
      state.serviceName = IPNum.value;
      state.breadcrumb = MACVal;
    });
    // useServiceNameStore.setBreadcrumb("2132131");
    router.push({ name: "flowMonitoringOverview" });
  } else {
    ElMessage.error("请选择IP");
  }
};
const deluuId = ref("");
const deleteTd = (row: any) => {
  deluuId.value = row.device_uuid;
  deleteVisible.value = true;
};

//修改每页条数
const handleSizeChange = (val: number) => {
  pageParams.rows = val;
  loadData();
};
//分页
const handleCurrentChange = (val: number) => {
  pageParams.page = val;
  loadData();
};

const list = reactive({
  records: [],
  total: 0
});
function convertUTCToBeijingTime(utcTime: string) {
  const date = new Date(utcTime);
  const beijingTime = new Date(date.getTime() + 8 * 60 * 60 * 1000);
  return beijingTime.toISOString().replace("T", " ").substring(0, 19);
}
const search = debounce(() => {
  loadData();
}, 500);
//列表数据
function loadData() {
  tableLoading.value = true;
  pageParams.appid = useApplicationStore.appId || "12345";
  getHostInfo(pageParams)
    .then(res => {
      if (res.code === 0 && res.records.length > 0) {
        list.records = res.records.map(item => {
          return {
            ...item,
            created_at: convertUTCToBeijingTime(item.created_at)
          };
        });
        list.total = Number(res.total);
      }
      tableLoading.value = false;
    })
    .catch(error => {
      console.error("数据加载失败:", error);
      tableLoading.value = false;
    });
}

const cancelSelect = () => {
  IPNum.value = "";
  IPDialog.value = false;
};
//删除数据
const delCommit = async () => {
  const params = {
    appid: useApplicationStore.appId || "12345",
    deviceUuid: deluuId.value
  };
  try {
    const res = await deleteValue(params);
    if (res.code === 0) {
      ElMessage.success("删除成功");
      loadData();
      deleteVisible.value = false;
    } else {
      ElMessage.error(res.desc);
    }
  } catch (err) {
    ElMessage.error("删除失败");
    console.error(err);
  }
};
//跳转详情
onMounted(() => {
  setTimeout(() => {
    loadData();
  }, 100);
});
</script>

<style lang="scss" scoped>
.tb-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.indicator-wrapper > *:not(:last-child) {
  margin-right: 8px;
  box-sizing: border-box;
}
.service-name {
  cursor: pointer;
  color: #0064c8;
  vertical-align: middle;
}
.operate {
  color: #0064c8;
  cursor: pointer;
}
</style>
