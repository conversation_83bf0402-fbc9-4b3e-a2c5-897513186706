<template>
  <div>
    <!-- 图表 -->
    <!-- <TitlecCom :title="echartTitle"></TitlecCom> -->
    <div ref="chartContainer" style="width: 100%; height: 350px" v-loading="chartLoading" />
    <!-- 选中数据展示 -->
    <!-- <div>
      <TitlecCom :title="tableTitle"></TitlecCom>
      <MyTable
        :data="selectedData"
        :total="totalRecords"
        pagination-layout="total ,prev, next,"
        height="310"
        class="mt-10px"
      >
        <MyColumn property="xValue" label="X轴数据" />
        <MyColumn property="yValue" label="Y轴数据" />
        <MyColumn property="xIndex" label="图表索引" />
      </MyTable>
    </div> -->

    <div>
      <div class="tb-header mt-20px">
        <div>
          <el-input
            style="width: 240px"
            placeholder="***********"
            v-model="pageParams.targetIp"
            clearable
            @blur="inputChange"
          ></el-input>
          <el-button type="primary" style="margin-left: 15px" :icon="Search" @click="search"
            >搜索</el-button
          >
        </div>
        <div>
          <el-button type="primary" @click="dialogFormVisible = true" :icon="Filter"
            >筛选</el-button
          >
        </div>
      </div>

      <MyTable
        :data="tableData"
        :total="tableTotal"
        v-loading="tableLoading"
        style="width: 100%"
        @sizeChange="handleSizeChange"
        @currentChange="handleCurrentChange"
        :default-sort="{
          prop: 'start_ts && last_ts && bytes_1to2 && bytes_2to1 && packets && bytes && packets_2to1 && packets_1to2 && syn_count && fin_count && rst_count',
          order: 'descending'
        }"
        @sort-change="handleSortChange"
      >
        <my-column
          v-for="column in tableColumns"
          :key="column.property"
          :property="column.property"
          :label="column.label"
          :sortable="column.sortable"
        />
        <my-column label="操作" align="center" header-align="center" fixed="right" width="120">
          <template #default="scope">
            <span class="operate" @click="viewIpDetail(scope.row)">查看详情</span>
          </template>
        </my-column>
      </MyTable>
    </div>
    <el-dialog
      draggable
      :align-center="true"
      v-model="dialogFormVisible"
      title="筛选列表"
      :close-on-click-modal="false"
      width="900"
    >
      <div class="column-selector">
        <el-checkbox-group v-model="selectedColumns">
          <el-checkbox
            v-for="column in columnConfig"
            :key="column.property"
            :value="column.property"
          >
            {{ column.label }}
          </el-checkbox>
        </el-checkbox-group>
      </div>
    </el-dialog>
    <el-drawer
      v-if="showIpDetail"
      v-model="showIpDetail"
      :title="activeName"
      size="80%"
      :before-close="handleDrawerClose"
    >
      <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
        <el-tab-pane label="Tcp会话" name="Tcp会话">
          <Tcp
            label="Tcp会话"
            name="Tcp会话"
            :isIpDetail="true"
            :ipDetailAddress="ipDetailAddress"
          ></Tcp>
        </el-tab-pane>
        <el-tab-pane label="Udp会话" name="Udp会话">
          <Udp
            label="Udp会话"
            name="Udp会话"
            :isIpDetail="true"
            :ipDetailAddress="ipDetailAddress"
          ></Udp>
        </el-tab-pane>
      </el-tabs>
    </el-drawer>
  </div>
</template>

<script setup>
import { onMounted, ref } from "vue";
import * as echarts from "echarts";
import { convertBytes } from "@/utils/trafficStr";
import { Search, Filter } from "@element-plus/icons-vue";
import IpDetail from "./components/IpDetail.vue";
import Tcp from "../TCP/index.vue";
import Udp from "../UDP/index.vue";
import { getIpSessions } from "@/api/flow/overview";
import { serviceTimeStore } from "@/store/modules/global";
// import type { TabsPaneContext } from "element-plus";
import { getChartTraffic } from "@/api/flow/detail";
import { formatTimestamp } from "@/utils/dateStr";
import { useRouter, useRoute } from "vue-router";
import { createChartOptions } from "../components/flowChart";
import { debounce } from "lodash-es";
import { applicationStore } from "@/store/modules/application";
import { serviceNameStore } from "@/store/modules/service";
const useApplicationStore = applicationStore();
const route = useRoute();
const useServiceNameStore = serviceNameStore();
const chartLoading = ref(false);
const serTimeStore = serviceTimeStore();
const dialogFormVisible = ref(false);
const showIpDetail = ref(false);
const activeName = ref("Tcp会话");
const ipDetailAddress = ref("");
const tableLoading = ref(false);
const handleClick = () => {
  console.log(1);
};

// const inputChange = () => {
//   const ipv4Regex =
//     /^(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}$/;
//   if (sip.value && !ipv4Regex.test(sip)) {
//     ElMessage.error("请输入正确的IP地址");
//     return;
//   }
// };

// const handleTabClick = (tab:any, event: Event) => {
//   // handleTabItem(tab.paneName as string);
// };
// 列配置，包括是否显示
const columnConfig = ref([
  { property: "ip1", label: "端点1", sortable: false, show: true },
  { property: "ip1_region", label: "端点1地址", sortable: false, show: true },
  { property: "ip2", label: "端点2", sortable: false, show: true },
  { property: "ip2_region", label: "端点2地址", sortable: false, show: true },
  { property: "service", label: "服务名", sortable: false, show: true },
  { property: "packets_1to2", label: "端点1到2包数", sortable: "custom", show: true },
  { property: "packets_2to1", label: "端点2到1包数", sortable: "custom", show: true },
  { property: "bytes_1to2", label: "端点1到2字节数", sortable: "custom", show: true },
  {
    property: "bytes_2to1",
    label: "端点2到1字节数",
    sortable: "custom",
    show: true
  },
  { property: "packets", label: "包数量", sortable: "custom", show: true },
  { property: "bytes", label: "字节数", sortable: "custom", show: true },
  { property: "protocol", label: "协议", sortable: false, show: true },
  { property: "start_ts", label: "开始时间", sortable: "custom", show: true },
  { property: "last_ts", label: "结束时间", sortable: "custom", show: true },
  {
    property: "syn_count",
    label: "SYN数量",
    sortable: "custom",
    show: true
  },
  { property: "fin_count", label: "FIN数量", sortable: "custom", show: true },
  { property: "rst_count", label: "RST数量", sortable: "custom", show: true }
]);

// 用户选择的列
const selectedColumns = ref(columnConfig.value.slice(0, 8).map(col => col.property));

// 根据用户选择更新tableColumns
const tableColumns = computed(() => {
  return columnConfig.value
    .filter(col => selectedColumns.value.includes(col.property) || col.property === "operation")
    .map(col => ({
      ...col,
      sortable: col.sortable !== undefined ? col.sortable : false
    }));
});

const viewIpDetail = row => {
  showIpDetail.value = !showIpDetail.value;
  ipDetailAddress.value = row.ip2;
};

const handleDrawerClose = () => {
  showIpDetail.value = false;
};

const tableData = ref([]);
const chartContainer = ref(null);
const selectedData = ref([]);
const totalRecords = ref(0);

const timestamps = ref([]);
const chartValues = reactive({
  tcp: [],
  inbound: [],
  outbound: [],
  ipv4: [],
  ipv6: [],
  total: [],
  udp: []
});
const initChartData = async () => {
  try {
    chartLoading.value = true;
    const params = {
      startTime: serTimeStore.serviceTimeData.start_time,
      endTime: serTimeStore.serviceTimeData.end_time,
      appid: useApplicationStore.appId
      // ip: useServiceNameStore.serviceName
    };
    const res = await getChartTraffic(params);

    if (res.code === 0) {
      timestamps.value = res.entity.tcp.map(item => formatTimestamp(item.timestamp));

      const extractValues = data => data.map(item => item.value);

      chartValues.tcp = extractValues(res.entity.tcp);
      chartValues.inbound = extractValues(res.entity.inbound);
      chartValues.ipv4 = extractValues(res.entity.ipv4);
      chartValues.ipv6 = extractValues(res.entity.ipv6);
      chartValues.outbound = extractValues(res.entity.outbound);
      chartValues.total = extractValues(res.entity.total);
      chartValues.udp = extractValues(res.entity.udp);

      initChart();
    }
    chartLoading.value = false;
  } catch (err) {
    chartLoading.value = false;
    console.error(err);
  }
};

// 初始化 ECharts 实例
const initChart = () => {
  let chartInstance;
  let isLegendChanging = false;

  chartInstance = echarts.init(chartContainer.value);
  chartInstance.setOption(createChartOptions(timestamps.value, chartValues));
  chartInstance.dispatchAction({
    type: "takeGlobalCursor",
    key: "brush",
    brushOption: {
      brushType: "lineX",
      brushMode: "single"
    }
  });
  // 监听 legend 事件
  chartInstance.on("legendselectchanged", params => {
    isLegendChanging = true;
  });

  // 监听 brush 事件
  chartInstance.on("brushSelected", params => {
    if (isLegendChanging) {
      isLegendChanging = false;
      return;
    }
    const batch = params?.batch?.[0];
    const areas = batch?.areas?.[0];
    const coordRange = areas?.coordRange;
    let xStartValue = coordRange?.[0] ?? null;
    let xEndValue = coordRange?.[1] ?? null;
    if (
      xStartValue === null ||
      xEndValue === null ||
      xStartValue >= timestamps.value.length ||
      xEndValue >= timestamps.value.length
    ) {
      getTableData();
      chartInstance.setOption({
        graphic: [
          {
            type: "text",
            style: {
              text: ``
            }
          }
        ]
      });
      selectedData.value = [];
      totalRecords.value = 0;
      return;
    }
    if (xStartValue === xEndValue) {
      if (xStartValue === 0) {
        xEndValue = xEndValue + 1;
      } else if (xEndValue === timestamps.value.length - 1) {
        xStartValue = xStartValue - 1;
      } else {
        xEndValue = xEndValue + 1;
      }
    }
    const selectedXData = timestamps.value.slice(xStartValue, xEndValue + 1);

    if (selectedXData.length < 2) {
      selectedData.value = [];
      totalRecords.value = 0;
      chartInstance.setOption({
        graphic: [
          {
            type: "text",
            style: {
              text: ``
            }
          }
        ]
      });
      return;
    }

    const timeVals = {
      start: selectedXData[0],
      end: selectedXData[selectedXData.length - 1]
    };
    chartInstance.setOption({
      graphic: [
        {
          type: "text",
          style: {
            text: `当前选取时间范围：${selectedXData[0]} - ${selectedXData[selectedXData.length - 1]}`,
            fill: "#666",
            font: "bold 15px Microsoft YaHei"
          },
          left: "center",
          top: 40
        }
      ]
    });
    getTableData(timeVals);
  });
};
const pageParams = reactive({
  startTime: "",
  endTime: "",
  page: "1",
  rows: "10",
  order: "",
  sort: "",
  // ip: 1,
  appid: useApplicationStore.appId,
  targetIp: ""
});
const tableTotal = ref(0);
async function getTableData(timeVals = undefined) {
  tableLoading.value = true;
  pageParams.targetIp = pageParams.targetIp.trim();
  pageParams.startTime = timeVals ? timeVals.start : serTimeStore.serviceTimeData.start_time;
  pageParams.endTime = timeVals ? timeVals.end : serTimeStore.serviceTimeData.end_time;
  try {
    const res = await getIpSessions(pageParams);
    if (res.code === 0) {
      tableData.value = res.records.map(item => {
        return {
          ...item,
          start_ts: formatTimestamp(item.start_ts),
          last_ts: formatTimestamp(item.last_ts),
          bytes_1to2: `${convertBytes(item.bytes_1to2).fixValue}  ${convertBytes(item.bytes_1to2).unit}`,
          bytes_2to1: `${convertBytes(item.bytes_2to1).fixValue}  ${convertBytes(item.bytes_2to1).unit}`,
          bytes: `${convertBytes(item.bytes).fixValue}  ${convertBytes(item.bytes).unit}`,
          service: item.service === "UNKNOWN" ? "未知" : item.service
        };
      });
      tableTotal.value = Number(res.total);
    }
    tableLoading.value = false;
  } catch (error) {
    tableData.value = [];
    tableTotal.value = 0;
    tableLoading.value = false;
  }
}

// 修改每页条数
const handleSizeChange = val => {
  pageParams.rows = val;
  getTableData();
};
const handleSortChange = val => {
  const order = val.order;
  const sort = val.prop;
  if (order === "ascending") {
    pageParams.order = "1";
  } else {
    pageParams.order = "0";
  }
  pageParams.sort = sort;
  getTableData();
};
// 分页
const handleCurrentChange = val => {
  pageParams.page = val;
  getTableData();
};
const inputChange = () => {
  const ipv4Regex =
    /^(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}$/;
  if (pageParams.targetIp && !ipv4Regex.test(pageParams.targetIp)) {
    ElMessage.error("请输入正确的IP地址");
    return;
  }
};
const search = debounce(() => {
  pageParams.page = "1";
  pageParams.sort = "";
  pageParams.order = "";
  getTableData();
}, 500);
// 组件挂载时初始化图表
onMounted(() => {
  if (route.query.label) {
    pageParams.targetIp = route.query.label;
  }
  initChartData();
  // getTableData();
});
</script>
<style lang="scss" scoped>
.tb-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.column-selector {
  margin-bottom: 15px;
}
.operate {
  color: #0064c8;
  cursor: pointer;
}
:deep(.el-drawer__header) {
  margin-bottom: 0px;
}
</style>
