import { convertBytes } from "@/utils/trafficStr";
import * as echarts from "echarts";
interface ChartValues {
  udp: number[];
  tcp: number[];
  ipv6: number[];
  ipv4: number[];
  outbound: number[];
  inbound: number[];
  total: number[];
}

export function createChartOptions(timestamps: Array<string>, chartValues: ChartValues) {
  return {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        lineStyle: {
          width: 1,
          color: "#008000"
        }
      },
      formatter: function (params: any) {
        let tooltipContent = "<h2>流量使用：</h2>";
        const reverseArr = params.reverse();
        reverseArr.forEach((item: any) => {
          const value = item.value;
          const converted = convertBytes(value);
          tooltipContent += `  
            <div style="display: flex; align-items: center;">  
              <span style="display: inline-block; width: 8px; height: 8px; background-color: ${item.color}; border-radius: 50%; margin-right: 5px;"></span>  
              <span>${item.seriesName}: ${converted.fixValue} ${converted.unit}</span>  
            </div>  
          `;
        });
        return tooltipContent;
      }
    },
    brush: {
      toolbox: [""],
      xAxisIndex: "all",
      brushLink: "all",
      removeOnClick: true,
      brushMode: "single",
      outOfBrush: {
        colorAlpha: 0.1
      },
      throttleDelay: 500,
      throttleType: "debounce"
    },
    dataZoom: [
      {
        type: "inside",
        xAxisIndex: [0, 1],
        start: 0,
        end: 100
      },
      {
        show: true,
        xAxisIndex: [0, 1],
        type: "slider",
        start: 0,
        end: 100
      }
    ],
    grid: {
      left: "1%",
      right: "1%",
      bottom: "14%",
      containLabel: true
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: timestamps
    },
    yAxis: {
      type: "value"
    },
    series: [
      {
        name: "udp",
        type: "line",
        stack: "Total",
        data: chartValues.udp,
        itemStyle: { color: "#D35400" },
        areaStyle: {},
        symbol: chartValues.udp.length === 1 ? "emptyCircle" : "none"
      },
      {
        name: "tcp",
        type: "line",
        stack: "Total",
        data: chartValues.tcp,
        areaStyle: {},
        itemStyle: { color: "#27AE60" },
        symbol: chartValues.tcp.length === 1 ? "emptyCircle" : "none"
      },
      {
        name: "ipv6",
        type: "line",
        stack: "Total",
        data: chartValues.ipv6,
        itemStyle: { color: "#2980B9" },
        areaStyle: {},
        symbol: chartValues.ipv6.length === 1 ? "emptyCircle" : "none"
      },
      {
        name: "ipv4",
        type: "line",
        stack: "Total",
        data: chartValues.ipv4,
        itemStyle: { color: "#F39C12" },
        areaStyle: {},
        symbol: chartValues.ipv4.length === 1 ? "emptyCircle" : "none"
      },
      {
        name: "流出",
        type: "line",
        stack: "Total",
        data: chartValues.outbound,
        itemStyle: { color: "#8E44AD" },
        areaStyle: {},
        symbol: chartValues.outbound.length === 1 ? "emptyCircle" : "none"
      },
      {
        name: "流入",
        type: "line",
        stack: "Total",
        data: chartValues.inbound,
        itemStyle: { color: "#C0392B" },
        areaStyle: {},
        symbol: chartValues.inbound.length === 1 ? "emptyCircle" : "none"
      },
      {
        name: "总流量",
        type: "line",
        stack: "Total",
        data: chartValues.total,
        areaStyle: {},
        symbol: chartValues.total.length === 1 ? "emptyCircle" : "none"
      }
    ],
    legend: {
      data: ["总流量", "流入", "流出", "ipv4", "ipv6", "tcp", "udp"],
      show: true,
      height: "auto",
      top: 10,
      itemGap: 25,
      itemWidth: 35,
      formatter: function (name: string) {
        return echarts.format.truncateText(name, 60, "14px Microsoft Yahei", "…");
      },
      tooltip: {
        show: true
      }
    }
  };
}
