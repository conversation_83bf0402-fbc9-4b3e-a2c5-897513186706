<template>
  <div v-loading="mysqlDetailLoading">
    <el-row :gutter="8" justify="start" class="indicator-wrapper">
      <el-col v-for="item in chartConfigs" :key="item.key" :xs="24" :sm="12" :md="8" class="mb-8px">
        <BaseEcharts :options="item.ref.value" width="100%" height="250px" />
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import BaseEcharts from "@/components/baseEcharts/index.vue";
import { getChartOptions } from "@/components/baseEcharts/chartsOptions";
import { getMysqlDetailAgg } from "@/api/module/database";
import { formatTime } from "@/utils/dateStr";
import { serviceNameStore } from "@/store/modules/service";
import { formatNums } from "@/utils/formatStr";
import { convertBytes } from "@/utils/trafficStr";

const useServiceNameStore = serviceNameStore();

const mysqlDetailLoading = ref(false);

const chartConfigs = [
  { key: "avgConnections", title: "平均连接数", field: "avgConnections", ref: ref({}) },
  { key: "avgThreadsRunning", title: "平均活跃线程数", field: "avgThreadsRunning", ref: ref({}) },
  { key: "sumSlowQueries", title: "慢查询数", field: "sumSlowQueries", ref: ref({}) },
  { key: "maxMaxConnections", title: "最大连接数", field: "maxMaxConnections", ref: ref({}) },
  { key: "avgAbortedConnects", title: "平均中止连接数", field: "avgAbortedConnects", ref: ref({}) },
  { key: "avgAbortedClients", title: "平均中止客户端数", field: "avgAbortedClients", ref: ref({}) },
  {
    key: "avgTableLocksWaited",
    title: "平均表锁等待数",
    field: "avgTableLocksWaited",
    ref: ref({})
  },
  {
    key: "avgTableLocksImmediate",
    title: "平均表锁立即数",
    field: "avgTableLocksImmediate",
    ref: ref({})
  },
  {
    key: "avgInnodbRowLockWaits",
    title: "平均InnoDB行锁等待数",
    field: "avgInnodbRowLockWaits",
    ref: ref({})
  },
  {
    key: "avgInnodbRowLockTime",
    title: "平均InnoDB行锁等待时间",
    field: "avgInnodbRowLockTime",
    ref: ref({})
  },
  {
    key: "avgInnodbBufferPoolReadRequests",
    title: "平均InnoDB缓冲池读取请求数",
    field: "avgInnodbBufferPoolReadRequests",
    ref: ref({})
  },
  {
    key: "avgInnodbBufferPoolReads",
    title: "平均InnoDB缓冲池读取数",
    field: "avgInnodbBufferPoolReads",
    ref: ref({})
  },
  { key: "avgBytesReceived", title: "平均接收字节数", field: "avgBytesReceived", ref: ref({}) },
  { key: "avgBytesSent", title: "平均发送字节数", field: "avgBytesSent", ref: ref({}) },
  {
    key: "avgCreatedTmpTables",
    title: "平均临时表创建数",
    field: "avgCreatedTmpTables",
    ref: ref({})
  },
  {
    key: "avgCreatedTmpDiskTables",
    title: "平均磁盘临时表创建数",
    field: "avgCreatedTmpDiskTables",
    ref: ref({})
  },
  {
    key: "avgCreatedTmpFiles",
    title: "平均临时文件创建数",
    field: "avgCreatedTmpFiles",
    ref: ref({})
  },
  {
    key: "avgSortMergePasses",
    title: "平均排序合并次数",
    field: "avgSortMergePasses",
    ref: ref({})
  },
  { key: "avgSortRange", title: "平均排序范围数", field: "avgSortRange", ref: ref({}) },
  { key: "avgSortRows", title: "平均排序行数", field: "avgSortRows", ref: ref({}) },
  { key: "avgSortScan", title: "平均排序扫描数", field: "avgSortScan", ref: ref({}) },
  { key: "maxStatusQueries", title: "区间最大累计查询数", field: "maxStatusQueries", ref: ref({}) },
  { key: "qps", title: "QPS", field: "qps", ref: ref({}) }
];

async function loadMysqlDetailData() {
  mysqlDetailLoading.value = true;
  try {
    const params = { instanceId: useServiceNameStore.serviceName };
    const res = await getMysqlDetailAgg(params);
    if (res.code === 0) {
      const data = res.records;
      updateCharts(data);
    }
  } catch (e) {
    console.error(e);
  } finally {
    mysqlDetailLoading.value = false;
  }
}

function updateCharts(data: any[]) {
  const times = data.map(item => formatTime(item.aggTime));
  chartConfigs.forEach(item => {
    let values: number[];
    let unit = "";

    if (["avgBytesReceived", "avgBytesSent"].includes(item.field)) {
      values = data.map(row => {
        const bytes = Number(row[item.field]);
        const converted = convertBytes(bytes);
        unit = converted.unit;
        return parseFloat(converted.fixValue);
      });
    } else {
      values = data.map(row => {
        const num = Number(row[item.field]);
        const formatted = formatNums(num);
        unit = formatted.unit || "";
        return parseFloat(formatted.fixValue);
      });
      switch (item.field) {
        case "avgInnodbRowLockTime":
          unit = unit ? `${unit}ms` : "ms";
          break;
        case "qps":
          unit = unit ? `${unit}次/秒` : "次/秒";
          break;
        default:
          unit = unit ? `${unit}次` : "次";
      }
    }

    const options = getChartOptions({
      name: unit,
      color: ["#5470C6"],
      titleType: item.title,
      originalTimes: times,
      seriesData: [values],
      type: "line"
    });
    item.ref.value = options;
  });
}

onMounted(loadMysqlDetailData);
</script>

<style lang="scss" scoped>
.indicator-wrapper {
  margin-bottom: 8px;
}
</style>
