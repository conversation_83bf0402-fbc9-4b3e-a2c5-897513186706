<template>
  <div class="mysql-monitor-page" v-loading="loading">
    <!-- 其他卡片分为两列 -->
    <div class="other-cards-container">
      <!-- 左侧列 -->
      <div class="card-column">
        <!-- 实例信息卡片 -->
        <el-card class="mb-4 custom-card instance-card">
          <TitlecCom :title="`MySQL 实例信息`" />
          <el-descriptions :column="3" border>
            <el-descriptions-item label="实例 ID">{{
              entity?.instanceInfo.instanceId || "-"
            }}</el-descriptions-item>
            <!-- <el-descriptions-item label="实例名称">{{
            entity?.instanceInfo.instance || "-"
          }}</el-descriptions-item> -->
            <el-descriptions-item label="版本">{{
              entity?.instanceInfo.version || "-"
            }}</el-descriptions-item>
            <el-descriptions-item label="InnoDB 版本">{{
              entity?.instanceInfo.innodbVersion || "-"
            }}</el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag
                size="large"
                :type="entity?.instanceInfo.status == '1' ? 'success' : 'danger'"
              >
                {{ entity?.instanceInfo.status == "1" ? "正常" : "异常" }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="运行时长">{{
              formatSeconds(entity?.instanceInfo.uptime)
            }}</el-descriptions-item>
            <el-descriptions-item label="最近更新时间">{{
              formatTimeWithNanoseconds(entity?.instanceInfo.lastUpdateTime)
            }}</el-descriptions-item>
          </el-descriptions>
        </el-card>
        <!-- 性能指标卡片 -->
        <el-card class="mb-4 custom-card performance-card">
          <TitlecCom :title="`性能指标`" />
          <el-row :gutter="12" class="mb-8px">
            <el-col :span="12" v-for="item in performanceMetricsList" :key="item.key">
              <el-card class="mb-8px small-card">
                <div class="text-sm text-gray-500">{{ item.label }}</div>
                <div class="text-xl font-bold mt-1">
                  {{
                    formatDisplayValue(item.key, entity?.performanceMetrics?.[item.key]).fixValue
                  }}
                  <span class="text-xs text-gray-500">
                    {{ formatDisplayValue(item.key, entity?.performanceMetrics?.[item.key]).unit }}
                  </span>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </el-card>
        <!-- 连接池状态卡片 -->
        <el-card class="mb-4 custom-card connection-pool-card">
          <TitlecCom :title="`连接池状态`" />
          <el-row :gutter="12" class="mb-8px">
            <el-col :span="12" v-for="item in connectionPoolStatusList" :key="item.key">
              <el-card class="mb-8px small-card">
                <div class="text-sm text-gray-500">{{ item.label }}</div>
                <div class="text-xl font-bold mt-1">
                  {{
                    formatDisplayValue(item.key, entity?.connectionPoolStatus?.[item.key]).fixValue
                  }}
                  <span class="text-xs text-gray-500">
                    {{
                      formatDisplayValue(item.key, entity?.connectionPoolStatus?.[item.key]).unit
                    }}
                  </span>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </el-card>
      </div>
      <!-- 右侧列 -->
      <div class="card-column">
        <!-- 健康检查信息 -->
        <el-card class="mb-4 custom-card health-check-card">
          <div class="flex justify-between items-center mb-2">
            <TitlecCom :title="`健康检查`" />
            <div class="text-sm text-gray-500">
              最近检查：
              {{
                entity?.healthCheck?.lastCheckTime
                  ? formatTimeWithNanoseconds(entity.healthCheck.lastCheckTime)
                  : "-"
              }}
            </div>
          </div>
          <el-row :gutter="12" class="mb-4">
            <el-col :span="12">
              <div class="text-sm font-bold text-gray-500">健康分数</div>
              <div class="text-2xl font-bold mt-1" :style="{ color: healthStatusColor }">
                {{ entity?.healthCheck?.healthScore ?? "-" }}
              </div>
            </el-col>
            <el-col :span="12">
              <div class="text-sm font-bold text-gray-500">健康状态</div>
              <div class="text-2xl mt-1">
                <el-tag :type="getHealthTagType(entity?.healthCheck?.healthStatus)">
                  {{ getHealthStatusText(entity?.healthCheck?.healthStatus) ?? "-" }}
                </el-tag>
              </div>
            </el-col>
          </el-row>
          <MyTable
            :data="entity?.healthCheck?.healthItems ?? []"
            v-if="(entity?.healthCheck?.healthItems ?? []).length > 0"
            style="width: 100%"
          >
            <my-column prop="name" label="检查项名称" />
            <my-column prop="status" label="状态" width="120">
              <template #default="scope">
                <el-tag :type="getHealthTagType(scope.row.status)">
                  {{ getHealthStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </my-column>
            <my-column prop="description" label="描述" />
            <my-column prop="suggestion" label="建议" />
          </MyTable>
        </el-card>
        <!-- 实时状态卡片 -->
        <el-card class="mb-4 custom-card real-time-card">
          <TitlecCom :title="`实时状态`" />
          <el-row :gutter="12" class="mb-8px">
            <el-col :span="12" v-for="item in realTimeStatusList" :key="item.key">
              <el-card class="mb-8px small-card">
                <div class="text-sm text-gray-500">{{ item.label }}</div>
                <div class="text-xl font-bold mt-1">
                  {{ formatDisplayValue(item.key, entity?.realTimeStatus?.[item.key]).fixValue }}
                  <span class="text-xs text-gray-500">
                    {{ formatDisplayValue(item.key, entity?.realTimeStatus?.[item.key]).unit }}
                  </span>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </el-card>
        <!-- InnoDB 状态卡片 -->
        <el-card class="mb-4 custom-card innodb-card">
          <TitlecCom :title="`InnoDB 状态`" />
          <el-row :gutter="12" class="mb-8px">
            <el-col :span="12" v-for="item in innodbStatusList" :key="item.key">
              <el-card class="mb-8px small-card">
                <div class="text-sm text-gray-500">{{ item.label }}</div>
                <div class="text-xl font-bold mt-1">
                  {{ formatDisplayValue(item.key, entity?.innodbStatus?.[item.key]).fixValue }}
                  <span class="text-xs text-gray-500">
                    {{ formatDisplayValue(item.key, entity?.innodbStatus?.[item.key]).unit }}
                  </span>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </el-card>
        <!-- 锁状态卡片 -->
        <el-card class="mb-4 custom-card lock-card">
          <TitlecCom :title="`锁状态`" />
          <el-row :gutter="12" class="mb-8px">
            <el-col :span="12" v-for="item in lockStatusList" :key="item.key">
              <el-card class="mb-8px small-card">
                <div class="text-sm text-gray-500">{{ item.label }}</div>
                <div class="text-xl font-bold mt-1">
                  {{ formatDisplayValue(item.key, entity?.lockStatus?.[item.key]).fixValue }}
                  <span class="text-xs text-gray-500">
                    {{ formatDisplayValue(item.key, entity?.lockStatus?.[item.key]).unit }}
                  </span>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import { ElMessage } from "element-plus";
import BaseEcharts from "@/components/baseEcharts/index.vue";
import { getMysqlDashboardData } from "@/api/module/database";
import { formatTimeWithNanoseconds, formatSeconds } from "@/utils/dateStr";
import { serviceNameStore } from "@/store/modules/service";
import TitlecCom from "@/components/TitleCom/index.vue";
import {
  formatDisplayValue,
  performanceMetricsList,
  realTimeStatusList,
  connectionPoolStatusList,
  innodbStatusList,
  lockStatusList,
  getHealthStatusText,
  getHealthTagType,
  getTrendChartsConfig
} from "./mysqlMonitorConfig";

const useServiceNameStore = serviceNameStore();

const loading = ref(false);
const entity = ref<any>(null);
const activeTrend = ref("connectionsTrend");

const healthStatusColor = computed(() => {
  const score = entity.value?.healthCheck?.healthScore ?? 0;
  if (score >= 80) return "#67C23A"; // 绿色，健康
  if (score >= 60) return "#E6A23C"; // 橙色，一般
  if (score >= 40) return "#F56C6C"; // 红色，警告
  return "#909399"; // 灰色，危险
});

const trendChartsConfig = computed(() => {
  return getTrendChartsConfig(entity.value);
});

async function loadData() {
  loading.value = true;
  try {
    const params = { instanceId: useServiceNameStore.serviceName };
    const res = await getMysqlDashboardData(params);
    if (res.code === 0) {
      entity.value = res.entity;
    } else {
      ElMessage.error("获取数据失败");
    }
  } catch (error) {
    console.error(error);
    ElMessage.error("请求异常");
  } finally {
    loading.value = false;
  }
}

onMounted(() => {
  loadData();
});
</script>

<style scoped>
.mysql-monitor-page {
  padding: 16px;
}
.mb-4 {
  margin-bottom: 16px;
}
.text-xl {
  font-size: 1.25rem;
}
.text-lg {
  font-size: 1.125rem;
}
.text-sm {
  font-size: 0.875rem;
}
.text-gray-500 {
  color: #6b7280;
}
.font-bold {
  font-weight: 600;
}
.custom-card {
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
.small-card {
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}
.other-cards-container {
  display: flex;
  gap: 20px;
}
.card-column {
  flex: 1;
}
</style>
