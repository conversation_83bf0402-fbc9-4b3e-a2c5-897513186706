// mysqlMonitorConfig.ts
import { getChartOptions } from "@/components/baseEcharts/chartsOptions";
import { formatNums } from "@/utils/formatStr";
import { convertBytes } from "@/utils/trafficStr";
import { formatMillisecondsToDays } from "@/utils/dateStr";

// 需要使用 convertBytes 展示的 key（字节字段）
const bytesFields = new Set(["bytesReceived", "bytesSent", "bufferPoolSize", "logFileSize"]);

// 需要使用 formatMillisecondsToDays 展示的 key（毫秒字段）
const millisecondsFields = new Set([
  "rowLockTime",
  "rowLockTimeAvg",
  "rowLockTimeMax",
  "innodbRowLockTime",
  "innodbRowLockTimeAvg",
  "innodbRowLockTimeMax"
]);

// 格式化显示值
export function formatDisplayValue(fieldKey: string, value: number | undefined | null) {
  if (value === undefined || value === null) {
    return { fixValue: "-", unit: "" };
  }
  if (millisecondsFields.has(fieldKey)) {
    return { fixValue: formatMillisecondsToDays(value), unit: "" };
  }
  if (bytesFields.has(fieldKey)) {
    return convertBytes(value);
  }
  return formatNums(value);
}

// 性能指标
export const performanceMetricsList = [
  { key: "totalQueries", label: "查询总数" },
  { key: "totalQuestions", label: "问题总数" },
  { key: "abortedConnects", label: "中止连接数" },
  { key: "abortedClients", label: "中止客户端数" },
  { key: "bytesReceived", label: "接收字节数" },
  { key: "bytesSent", label: "发送字节数" },
  { key: "createdTmpTables", label: "临时表创建数" },
  { key: "createdTmpDiskTables", label: "磁盘临时表创建数" },
  { key: "createdTmpFiles", label: "临时文件创建数" },
  { key: "sortMergePasses", label: "排序合并次数" },
  { key: "sortRange", label: "排序范围数" },
  { key: "sortRows", label: "排序行数" },
  { key: "sortScan", label: "排序扫描数" }
];

// 连接池状态
export const realTimeStatusList = [
  { key: "currentConnections", label: "当前连接数" },
  { key: "maxConnections", label: "最大连接数" },
  { key: "connectionUsageRate", label: "连接使用率 (%)" },
  { key: "threadsRunning", label: "活跃线程数" },
  { key: "threadsCached", label: "缓存线程数" },
  { key: "slowQueries", label: "慢查询数" },
  { key: "qps", label: "每秒查询数" },
  { key: "tps", label: "每秒事务数" }
];

// 实时状态
export const connectionPoolStatusList = [
  { key: "currentConnections", label: "当前连接数" },
  { key: "maxConnections", label: "最大连接数" },
  { key: "usageRate", label: "连接使用率 (%)" },
  { key: "threadsRunning", label: "活跃线程数" },
  { key: "threadsConnected", label: "已连接线程数" },
  { key: "threadsCached", label: "缓存线程数" },
  { key: "threadsCreated", label: "创建线程数" },
  { key: "abortedConnects", label: "中止连接数" },
  { key: "abortedClients", label: "中止客户端数" },
  { key: "maxUsedConnections", label: "最大使用连接数" }
];

export const innodbStatusList = [
  { key: "bufferPoolSize", label: "缓冲池大小" },
  { key: "bufferPoolHitRate", label: "缓冲池命中率 (%)" },
  { key: "bufferPoolReadRequests", label: "缓冲池读取请求数" },
  { key: "bufferPoolReads", label: "缓冲池读取数" },
  { key: "rowLockWaits", label: "行锁等待数" },
  { key: "rowLockTime", label: "行锁等待时间" },
  { key: "rowLockTimeAvg", label: "行锁等待平均时间" },
  { key: "rowLockTimeMax", label: "行锁等待最长时间" },
  { key: "logFileSize", label: "日志文件大小" }
];

export const lockStatusList = [
  { key: "tableLocksImmediate", label: "表锁立即数" },
  { key: "tableLocksWaited", label: "表锁等待数" },
  { key: "innodbRowLockWaits", label: "InnoDB 行锁等待数" },
  { key: "innodbRowLockTime", label: "InnoDB 行锁等待时间" },
  { key: "innodbRowLockTimeAvg", label: "InnoDB 行锁平均等待时间" },
  { key: "innodbRowLockTimeMax", label: "InnoDB 行锁最大等待时间" }
];

export function getHealthStatusText(statusCode: number) {
  switch (statusCode) {
    case 1:
      return "健康";
    case 2:
      return "一般";
    case 3:
      return "警告";
    case 4:
      return "危险";
    default:
      return "-";
  }
}

export function getHealthTagType(statusCode: number) {
  switch (statusCode) {
    case 1:
      return "success";
    case 2:
      return "warning";
    case 3:
    case 4:
      return "danger";
    default:
      return "info";
  }
}

export function getTrendChartsConfig(entity: any) {
  if (!entity?.trendCharts) return [];
  const config = [
    {
      key: "connectionsTrend",
      label: "连接数趋势",
      data: entity.trendCharts.connectionsTrend
    },
    { key: "qpsTrend", label: "QPS 趋势", data: entity.trendCharts.qpsTrend },
    {
      key: "slowQueriesTrend",
      label: "慢查询趋势",
      data: entity.trendCharts.slowQueriesTrend
    },
    {
      key: "bufferPoolHitRateTrend",
      label: "缓冲池命中率趋势",
      data: entity.trendCharts.bufferPoolHitRateTrend
    },
    { key: "lockWaitTrend", label: "锁等待趋势", data: entity.trendCharts.lockWaitTrend },
    { key: "threadsTrend", label: "线程趋势", data: entity.trendCharts.threadsTrend }
  ];
  return config.map(item => ({
    ...item,
    options: getChartOptions(item.data, item.label)
  }));
}
