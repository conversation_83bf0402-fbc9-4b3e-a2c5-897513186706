<template>
  <MyTable
    :data="list.records"
    style="width: 100%"
    v-loading="tableLoading"
    :showElPagination="false"
  >
    <my-column property="metric_name" label="指标名称" />
    <my-column property="metric_value" label="指标值" />
  </MyTable>
</template>

<script setup lang="ts">
import { onMounted } from "vue";
import { getDeviceDetail } from "@/api/module/snmp";
//loading动画
const tableLoading = ref(false);
const list = reactive({
  records: []
});
// 获取设备概览数据
function loadData() {
  tableLoading.value = true;
  getDeviceDetail({})
    .then(response => {
      if (response.code === 0) {
        list.records = response.entity.metricTrends;
        tableLoading.value = false;
      }
    })
    .catch(error => {
      tableLoading.value = false;
      console.log(error);
    });
}

onMounted(() => {
  loadData();
});
</script>

<style lang="scss" scoped></style>
