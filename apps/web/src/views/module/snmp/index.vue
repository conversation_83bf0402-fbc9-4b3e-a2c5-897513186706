<template>
  <div class="tb-header">
    <div>
      <!-- <el-select
        style="width: 240px"
        v-model="pageParams.status"
        placeholder="请选择状态"
        clearable
      >
        <el-option label="在线" value="online" />
        <el-option label="关机" value="offline" />
        <el-option label="异常" value="error" />
      </el-select> -->
      <el-input
        style="width: 240px"
        placeholder="请输入设备名称"
        v-model="pageParams.deviceName"
        clearable
      ></el-input>
      <el-input
        style="width: 240px; margin-left: 10px"
        placeholder="***********"
        v-model="pageParams.deviceHost"
        clearable
      ></el-input>
      <el-input
        style="width: 240px; margin-left: 10px"
        placeholder="请输入设备类型"
        v-model="pageParams.deviceType"
        clearable
      ></el-input>
      <el-button type="primary" style="margin-left: 10px" :icon="Search" @click="search"
        >搜索</el-button
      >
    </div>
  </div>
  <MyTable
    :data="list.records"
    :total="list.total"
    style="width: 100%"
    v-loading="tableLoading"
    @sizeChange="handleSizeChange"
    @currentChange="handleCurrentChange"
  >
    <my-column property="deviceName" label="设备名称">
      <template #default="scope">
        <span class="operate" @click="showDetail(scope.row)">{{ scope.row.deviceName }}</span>
      </template>
    </my-column>
    <my-column property="deviceHost" label="设备IP地址" />
    <my-column property="deviceType" label="设备类型" />
    <my-column property="status" label="设备状态">
      <template #default="{ row }">
        <el-tag
          :type="
            row.status === 'online'
              ? 'success'
              : row.status === 'offline'
                ? 'warning'
                : row.status === 'error'
                  ? 'danger'
                  : 'info'
          "
        >
          {{
            row.status === "online"
              ? "在线"
              : row.status === "offline"
                ? "关机"
                : row.status === "error"
                  ? "异常"
                  : "未知"
          }}
        </el-tag>
      </template>
    </my-column>
    <my-column property="lastSeen" label="最后采集时间" />
    <my-column label="操作" align="center" header-align="center" fixed="right" width="150">
      <template #default="scope">
        <span class="operate" @click="showDetail(scope.row)">查看详情</span>
        <span class="divider"> / </span>
        <span class="operate" @click="dialogFormVisible = true">编辑别名</span>
      </template>
    </my-column>
  </MyTable>
  <el-dialog
    :align-center="true"
    v-model="dialogFormVisible"
    title="编辑别名"
    width="600"
    :close-on-click-modal="false"
  >
    <el-form label-width="auto">
      <el-form-item label="别名：">
        <el-input placeholder="请输入别名" clearable></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="dialogFormVisible = false">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import { Search } from "@element-plus/icons-vue";
import { getDevicesList } from "@/api/module/snmp";
import { useRouter } from "vue-router";
import { serviceNameStore } from "@/store/modules/service";
const useServiceNameStore = serviceNameStore();
const router = useRouter();
//loading动画
const tableLoading = ref(false);
const dialogFormVisible = ref(false);
//列表参数
const pageParams = reactive({
  deviceHost: "",
  deviceType: "",
  deviceName: "",
  page: 1,
  rows: 10
});
//修改每页条数
const handleSizeChange = (val: number) => {
  pageParams.rows = val;
  loadData();
};
//分页
const handleCurrentChange = (val: number) => {
  pageParams.page = val;
  loadData();
};
//搜索
function search() {
  pageParams.page = 1;
  loadData();
}
const list = reactive({
  records: [],
  total: 0
});

//列表数据
function loadData() {
  tableLoading.value = true;
  pageParams.deviceType = pageParams.deviceType.trim();
  pageParams.deviceHost = pageParams.deviceHost.trim();
  pageParams.deviceName = pageParams.deviceName.trim();
  getDevicesList(pageParams)
    .then(response => {
      if (response.code === 0) {
        list.records = response.records;
        list.total = Number(response.total);
        tableLoading.value = false;
      }
    })
    .catch(error => {
      tableLoading.value = false;
      console.log(error);
    });
}
onMounted(() => {
  loadData();
});

const showDetail = (row: any) => {
  useServiceNameStore.setServiceName(row.deviceName);
  router.push("/module/snmp/trend");
};
</script>
<style lang="scss" scoped>
.tb-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.operate {
  color: #0064c8;
  cursor: pointer;
}
</style>
