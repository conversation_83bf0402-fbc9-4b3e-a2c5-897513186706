<template>
  <div class="tb-header">
    <div>
      <el-input
        style="width: 240px"
        placeholder="请输入主机ID"
        v-model="pageParams.hostId"
        clearable
      ></el-input>
      <el-button type="primary" style="margin-left: 15px" :icon="Search" @click="search"
        >搜索</el-button
      >
    </div>
  </div>

  <MyTable
    :data="list.records"
    :total="list.total"
    @sizeChange="handleSizeChange"
    @currentChange="handleCurrentChange"
    v-loading="StatRequestCountLoading"
  >
    <my-column property="hostId" label="主机ID">
      <template #default="scope">
        <span class="operate" @click="handleViewDetail(scope)">{{ scope.row.hostId }}</span>
      </template>
    </my-column>
    <my-column property="instance" label="实例地址" />
    <my-column property="version" label="版本" width="120" />
    <my-column property="mode" label="部署模式" />
    <my-column property="os" label="操作系统" />
    <my-column property="uptimeInDays" label="运行时长(天)" />
    <my-column property="instantaneousOpsPerSec" label="OPS" width="120" />
    <my-column property="totalCommandsProcessed" label="命令处理总数" />
    <my-column property="usedCpuSys" label="系统CPU">
      <template #default="{ row }">
        {{ row.usedCpuSys ? `${row.usedCpuSys.toFixed(2)} s` : "0.00 s" }}
      </template>
    </my-column>
    <my-column property="usedCpuUser" label="用户CPU">
      <template #default="{ row }">
        {{ row.usedCpuUser ? `${row.usedCpuUser.toFixed(2)} s` : "0.00 s" }}
      </template>
    </my-column>
    <my-column property="onlineStatus" label="状态" width="100">
      <template #default="{ row }">
        <el-tag :type="row.onlineStatus ? 'success' : 'danger'">
          {{ row.onlineStatus ? "在线" : "离线" }}
        </el-tag>
      </template>
    </my-column>
    <my-column label="操作" align="center" header-align="center" fixed="right" width="150">
      <template #default="scope">
        <span class="operate" @click="handleViewDetail(scope)">查看详情</span>
        <span class="divider"> / </span>
        <span class="operate" @click="dialogFormVisible = true">编辑别名</span>
      </template>
    </my-column>
  </MyTable>
  <el-dialog
    :align-center="true"
    v-model="dialogFormVisible"
    title="编辑别名"
    width="600"
    :close-on-click-modal="false"
  >
    <el-form label-width="auto">
      <el-form-item label="别名：">
        <el-input placeholder="请输入别名" clearable></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="dialogFormVisible = false">确定</el-button>
      </div>
    </template>
  </el-dialog>

  <el-drawer v-model="drawerVisible" :title="drawerTitle" size="55%">
    <!-- <RedisDetailCharts ref="redisChartRef" /> -->
    <!-- <BaseEcharts :options="item.ref.value" width="100%" height="250px" /> -->
    <el-row :gutter="8" justify="start" class="indicator-wrapper">
      <el-col v-for="item in [...metricsConfigs]" :key="item.key" :span="12" class="mb-8px">
        <BaseEcharts
          :options="item.ref.value"
          width="23vw"
          height="250px"
          v-loading="item.loading"
        />
      </el-col>
    </el-row>
    <MyTable :data="detailList.records" :total="detailList.total" v-loading="detailListLoading">
      <my-column property="instance" label="实例地址" />
      <my-column property="command" label="命令名称" />
      <my-column property="totalCalls" label="总调用次数" />
      <my-column property="totalUsec" label="总执行时间">
        <template #default="{ row }">
          {{ row.totalUsec ? `${(row.totalUsec / 1000).toFixed(2)} ms` : "0.00 ms" }}
        </template>
      </my-column>
      <my-column property="totalUsecPerCall" label="平均耗时">
        <template #default="{ row }">
          {{ row.totalUsecPerCall ? `${(row.totalUsecPerCall / 1000).toFixed(2)} ms` : "0.00 ms" }}
        </template>
      </my-column>
      <my-column property="totalRejectedCalls" label="被拒绝次数" />
      <my-column property="totalFailedCalls" label="失败次数" />
    </MyTable>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick, onMounted } from "vue";
import { Search } from "@element-plus/icons-vue";
import { applicationStore } from "@/store/modules/application";
import {
  getRedisList,
  getRedisCommandStats,
  getRedisDashboardDataALL,
  getRedisClientAggregation,
  getRedisMemoryAggregation,
  getRedisKeyspaceAggregation,
  getRedisReplicaAggregation,
  getRedisCommandAggregation
} from "@/api/module/cache";
import { useThrottleFn } from "@vueuse/core";
import { getChartOptions } from "@/components/baseEcharts/chartsOptions";
const useApplicationStore = applicationStore();
const StatRequestCountLoading = ref(false);
const detailListLoading = ref(false);
const dialogFormVisible = ref(false);
const pageParams = reactive({ hostId: "", page: 1, rows: 10 });
const drawerVisible = ref(false);
const drawerTitle = ref("");
const redisChartRef = ref();

const list = reactive({ records: [], total: 0 });
const detailList = reactive({ records: [], total: 0 });

const search = useThrottleFn(() => {
  pageParams.page = 1;
  loadRedisInstanceList();
}, 500);

const handleSizeChange = (val: number) => {
  pageParams.rows = val;
  loadRedisInstanceList();
};

const handleCurrentChange = (val: number) => {
  pageParams.page = val;
  loadRedisInstanceList();
};

// 图表数据定义
const metricsConfigs = [
  {
    key: "aggCalls",
    title: "总调用次数",
    field: "agg_calls",
    loading: false,
    type: "bar",
    ref: ref({})
  },
  {
    key: "aggUsec",
    title: "总执行时间(微秒)",
    field: "agg_usec",
    loading: false,
    type: "line",
    ref: ref({})
  },
  {
    key: "aggUsecPerCall",
    title: "平均每次调用耗时(微秒)",
    field: "agg_usec_per_call",
    loading: false,
    type: "line",
    ref: ref({})
  },
  {
    key: "aggRejectedCalls",
    title: "总被拒绝调用次数",
    field: "agg_rejected_calls",
    loading: false,
    type: "bar",
    ref: ref({})
  },
  {
    key: "aggFailedCalls",
    title: "总调用失败次数",
    field: "agg_failed_calls",
    loading: false,
    type: "bar",
    ref: ref({})
  },
  {
    key: "maxUsedMemory",
    title: "最大使用内存",
    field: "max_used_memory",
    loading: false,
    type: "bar",
    ref: ref({})
  },
  {
    key: "maxUsedMemoryRss",
    title: "最大使用内存(RSS)",
    field: "max_used_memory_rss",
    loading: false,
    type: "bar",
    ref: ref({})
  },
  {
    key: "avgMemFragmentationRatio",
    title: "平均内存碎片率",
    field: "avg_mem_fragmentation_ratio",
    loading: false,
    type: "line",
    ref: ref({})
  },
  {
    key: "avgConnectedClients",
    title: "平均连接客户端数",
    field: "avg_connected_clients",
    loading: false,
    type: "bar",
    ref: ref({})
  },
  {
    key: "sumCommandsProcessed",
    title: "总处理命令数",
    field: "sum_commands_processed",
    loading: false,
    type: "bar",
    ref: ref({})
  },
  {
    key: "avgInstantaneousOpsPerSec",
    title: "平均每秒操作数",
    field: "avg_instantaneous_ops_per_sec",
    loading: false,
    type: "bar",
    ref: ref({})
  },
  {
    key: "avgKeyspaceHitsRatio",
    title: "平均键空间命中比率",
    field: "avg_keyspace_hits_ratio",
    loading: false,
    type: "line",
    ref: ref({})
  },
  {
    key: "avgUsedMemoryOverhead",
    title: "平均内存开销",
    field: "avg_used_memory_overhead",
    loading: false,
    type: "line",
    ref: ref({})
  },
  {
    key: "avgUsedMemoryDataset",
    title: "平均数据集内存使用",
    field: "avg_used_memory_dataset",
    loading: false,
    type: "bar",
    ref: ref({})
  },
  {
    key: "sumConnectionsReceived",
    title: "总接收连接数",
    field: "sum_connections_received",
    loading: false,
    type: "bar",
    ref: ref({})
  },
  {
    key: "sumRejectedConnections",
    title: "总拒绝连接数",
    field: "sum_rejected_connections",
    loading: false,
    type: "line",
    ref: ref({})
  },
  {
    key: "maxBlockedClients",
    title: "最大阻塞客户端数",
    field: "max_blocked_clients",
    loading: false,
    type: "bar",
    ref: ref({})
  },
  {
    key: "maxSyncFull",
    title: "最大全量同步次数",
    field: "max_sync_full",
    loading: false,
    type: "bar",
    ref: ref({})
  },
  {
    key: "maxSyncPartialOk",
    title: "最大部分同步成功次数",
    field: "max_sync_partial_ok",
    loading: false,
    type: "bar",
    ref: ref({})
  },
  {
    key: "maxSyncPartialErr",
    title: "最大部分同步失败次数",
    field: "max_sync_partial_err",
    loading: false,
    type: "bar",
    ref: ref({})
  },
  {
    key: "sumCpuSys",
    title: "系统CPU总耗时",
    field: "sum_cpu_sys",
    loading: false,
    type: "line",
    ref: ref({})
  },
  {
    key: "sumCpuUser",
    title: "用户CPU总耗时",
    field: "sum_cpu_user",
    loading: false,
    type: "line",
    ref: ref({})
  }
];
function handleViewDetail(scope: any) {
  drawerVisible.value = true;
  drawerTitle.value = `Redis实例详情 - ${scope.row.hostId}`;
  metricsConfigs.forEach(item => (item.loading = true));
  getRedisClientAggregationFn(scope.row.hostId);
  getRedisMemoryAggregationFn(scope.row.hostId);
  getRedisKeyspaceAggregationFn(scope.row.hostId);
  getRedisReplicaAggregationFn(scope.row.hostId);
  getRedisCommandAggregationFn(scope.row.hostId);
  chartData(scope.row.hostId);
  loadRedisCommandStatsList(scope.row.hostId);
}

// 查询客户端相关聚合结果
const getRedisClientAggregationFn = async (instanceId: string) => {
  try {
    const res = await getRedisClientAggregation({
      appid: useApplicationStore.appId,
      hostId: instanceId
    });
    if (res.code === 0) {
      updateCharts(res.entity.datas);
    }
  } catch (err) {}
};

// 查询内存相关聚合结果
const getRedisMemoryAggregationFn = async (instanceId: string) => {
  try {
    const res = await getRedisMemoryAggregation({
      appid: useApplicationStore.appId,
      hostId: instanceId
    });
    if (res.code === 0) {
      updateCharts(res.entity.datas);
    }
  } catch (err) {}
};

// 查询键空间相关聚合结果
const getRedisKeyspaceAggregationFn = async (instanceId: string) => {
  try {
    const res = await getRedisKeyspaceAggregation({
      appid: useApplicationStore.appId,
      hostId: instanceId
    });
    if (res.code === 0) {
      updateCharts(res.entity.datas);
    }
  } catch (err) {}
};

// 查询副本指标相关聚合结果
const getRedisReplicaAggregationFn = async (instanceId: string) => {
  try {
    const res = await getRedisReplicaAggregation({
      appid: useApplicationStore.appId,
      hostId: instanceId
    });
    if (res.code === 0) {
      updateCharts(res.entity.datas);
    }
  } catch (err) {}
};

// 查询命令相关聚合结果
const getRedisCommandAggregationFn = async (instanceId: string) => {
  try {
    const res = await getRedisCommandAggregation({
      appid: useApplicationStore.appId,
      hostId: instanceId
    });
    if (res.code === 0) {
      updateCharts(res.entity.datas);
    }
  } catch (err) {}
};

// redis指标统计结果相关图表接口
const chartData = async (instanceId: string) => {
  try {
    const res = await getRedisDashboardDataALL({
      appid: useApplicationStore.appId,
      hostId: instanceId
    });
    if (res.code === 0 && res.entity?.datas) {
      updateCharts(res.entity.datas);
    }
  } catch (error) {
    console.error("加载Redis指标统计图表失败", error);
  }
};

// redis命令统计列表
async function loadRedisCommandStatsList(instanceId: string) {
  detailListLoading.value = true;
  try {
    const res = await getRedisCommandStats({
      appid: useApplicationStore.appId,
      hostId: instanceId
    });
    if (res.code === 0 && res.entity) {
      detailList.records = res.entity.list || [];
      detailList.total = res.entity.total || 0;
    }
    console.log("Redis命令统计列表数据:", res);
  } catch (error) {
    console.error("加载Redis命令统计列表失败", error);
  } finally {
    detailListLoading.value = false;
  }
}

// 列表数据
async function loadRedisInstanceList() {
  StatRequestCountLoading.value = true;
  try {
    const res = await getRedisList(pageParams);
    if (res.code === 0 && res.records) {
      list.records = res.records;
      list.total = Number(res.total);
    }
  } catch (error) {
    console.error("加载Redis实例列表失败", error);
  } finally {
    StatRequestCountLoading.value = false;
  }
}

function updateCharts(data: any) {
  metricsConfigs.forEach(item => {
    const fieldKey = item.field;
    const displayTitle = item.title;
    const chartType = item.type;

    if (!data[fieldKey]) {
      console.warn(`未找到字段 ${fieldKey} 对应的数据，跳过`);
      return;
    }

    const instanceDataMap = data[fieldKey];

    const instances = Object.keys(instanceDataMap);
    if (instances.length === 0) {
      console.warn(`字段 ${fieldKey} 无实例数据，跳过`);
      return;
    }

    const firstInstance = instances[0];
    const originalTimes = instanceDataMap[firstInstance].map((entry: any) => entry.timestamp);
    const seriesData = instances.map(instance => {
      return instanceDataMap[instance].map(entry => {
        const fieldNamePascal = item.key;
        const value = entry[fieldNamePascal];
        return Number(value ?? 0);
      });
    });

    const names = instances;

    const defaultColors = [
      "#78bf75",
      "#4ea3ef",
      "#ff9a3e",
      "#ef4e4e",
      "#4e9aff",
      "#a979d1",
      "#61d0e8",
      "#f1c40f",
      "#2ecc71",
      "#e67e22",
      "#1abc9c",
      "#8e44ad"
    ];
    const colors = defaultColors.slice(0, names.length);

    if (item.ref && item.ref.value) {
      item.ref.value = getChartOptions({
        names,
        color: colors,
        titleType: displayTitle,
        originalTimes,
        seriesData,
        type: chartType,
        legend: { show: true }
      });
    }
    item.loading = false;
  });
}

onMounted(() => {
  loadRedisInstanceList();
});
</script>

<style scoped lang="scss">
.tb-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.operate {
  color: #0064c8;
  cursor: pointer;
  user-select: none;
}
</style>
