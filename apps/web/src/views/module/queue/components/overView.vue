<template>
  <div v-loading="loading">
    <div class="flex justify-between indicator-wrapper">
      <Indicator :value="dashboardData.topicCount" :unit="'主题数'"></Indicator>
      <Indicator :value="dashboardData.groupCount" :unit="'消费者分组数'"></Indicator>
      <Indicator :value="dashboardData.brokerCount" :unit="'节点数'"></Indicator>
      <Indicator :value="dashboardData.totalPartitions" :unit="'总分区数'"></Indicator>
      <Indicator :value="dashboardData.totalMessages" :unit="'总消息数'"></Indicator>
    </div>
    <div class="flex justify-between indicator-wrapper mt-10px">
      <Ranking
        class="w-33%"
        :title="'主题（Top10）'"
        :rankingList="topTopicsData"
        @itemClick="handleTopicClick"
      />
      <Ranking
        class="w-33%"
        :title="'消费者分组（Top10）'"
        :rankingList="topGroupsData"
        @itemClick="handleGroupClick"
      />
      <div class="w-33%">
        <el-card class="performance-card">
          <TitlecCom :title="'性能指标'" />
          <el-row :gutter="12" class="mb-8px">
            <el-col :span="12" v-for="item in performanceMetricsList" :key="item.key">
              <el-card class="mb-8px small-card">
                <div class="text-sm text-gray-500">{{ item.label }}</div>
                <div class="text-xl font-bold mt-1">
                  {{ formatDisplayValue(item.key, performanceMetricsData[item.key]).fixValue }}
                  <span class="text-xs text-gray-500">
                    {{ formatDisplayValue(item.key, performanceMetricsData[item.key]).unit }}
                  </span>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </el-card>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { serviceNameStore } from "@/store/modules/service";
import { getMetricsDashboard } from "@/api/module/queue";
import { formatNums } from "@/utils/formatStr";
import TitlecCom from "@/components/TitleCom/index.vue";
import { useRouter } from "vue-router";

const router = useRouter();
const useServiceNameStore = serviceNameStore();

// 加载状态
const loading = ref(false);

// 仪表板数据
const dashboardData = reactive({
  topicCount: "0",
  groupCount: "0",
  brokerCount: "0",
  totalPartitions: "0",
  totalMessages: "0"
});

// 主题数据
const topTopicsData = ref([]);

// 消费者分组数据
const topGroupsData = ref([]);

// 性能指标数据
const performanceMetricsData = ref<any>({});

// 性能指标列表
const performanceMetricsList = [
  { key: "messageThroughput", label: "消息吞吐量" },
  { key: "partitionCount", label: "分区总数" },
  { key: "totalLag", label: "总Lag" },
  { key: "activePartitionCount", label: "活跃分区数" },
  { key: "replicaPartitionCount", label: "副本分区数" },
  { key: "underReplicatedPartitionCount", label: "Under Replicated Partition数" }
];

// 格式化显示值
const formatDisplayValue = (key: string, value: any) => {
  if (!value) return { fixValue: "0", unit: "" };

  const numValue = parseFloat(value);
  if (isNaN(numValue)) return { fixValue: value, unit: "" };

  const formatted = formatNums(numValue);
  return {
    fixValue: formatted.fixValue,
    unit: formatted.unit || ""
  };
};

const pageParams = reactive({
  clusterName: ""
});
function loadData() {
  loading.value = true;
  pageParams.clusterName = useServiceNameStore.serviceName;
  getMetricsDashboard(pageParams)
    .then(response => {
      if (response.code === 0 && response.entity) {
        // 更新仪表板数据
        if (response.entity.kafkaClusterResp) {
          const clusterData = response.entity.kafkaClusterResp;
          dashboardData.topicCount = clusterData.topicCount || "0";
          dashboardData.groupCount = clusterData.groupCount || "0";
          dashboardData.brokerCount = clusterData.brokerCount || "0";
          dashboardData.totalPartitions = clusterData.totalPartitions || "0";
          dashboardData.totalMessages = clusterData.totalMessages || "0";
        }

        // 更新主题数据
        if (response.entity.topTopics) {
          const totalLag = response.entity.topTopics.reduce(
            (sum: number, item: any) => sum + parseInt(item.messageCount),
            0
          );
          topTopicsData.value = response.entity.topTopics.map((item: any) => ({
            name: item.topic,
            proportion: totalLag > 0 ? (parseInt(item.messageCount) / totalLag) * 100 : 0,
            totalScore: parseInt(item.messageCount),
            unit: "",
            color: "#445fde"
          }));
        }

        // 更新消费者分组数据
        if (response.entity.topGroups) {
          const totalLag = response.entity.topGroups.reduce(
            (sum: number, item: any) => sum + parseInt(item.lag),
            0
          );
          topGroupsData.value = response.entity.topGroups.map((item: any) => ({
            name: item.consumergroup,
            proportion: totalLag > 0 ? (parseInt(item.lag) / totalLag) * 100 : 0,
            totalScore: parseInt(item.lag),
            unit: "",
            color: "#445fde"
          }));
        }

        //更新性能指标数据
        if (response.entity.performanceMetrics) {
          const metrics = response.entity.performanceMetrics;
          performanceMetricsData.value = {
            messageThroughput: metrics.messageThroughput,
            partitionCount: metrics.partitionCount,
            totalLag: metrics.totalLag,
            activePartitionCount: metrics.activePartitionCount,
            replicaPartitionCount: metrics.replicaPartitionCount,
            underReplicatedPartitionCount: metrics.underReplicatedPartitionCount
          };
        }
      }
    })
    .catch(error => {
      console.error("获取消息队列仪表板数据失败:", error);
    })
    .finally(() => {
      loading.value = false;
    });
}
onMounted(() => {
  loadData();
});

// 跳转到主题信息页面并带上name参数
function handleTopicClick(item: any) {
  router.push({
    path: "/module/queue/topic",
    query: { topic: item.name }
  });
}
// 跳转到消费者分组信息页面并带上name参数
function handleGroupClick(item: any) {
  router.push({
    path: "/module/queue/group",
    query: { consumergroup: item.name }
  });
}
</script>
<style lang="scss" scoped>
.indicator-wrapper > *:not(:last-child) {
  margin-right: 8px;
  box-sizing: border-box;
}

.performance-card {
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.small-card {
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.text-xl {
  font-size: 1.25rem;
}

.text-sm {
  font-size: 0.875rem;
}

.text-xs {
  font-size: 0.75rem;
}

.text-gray-500 {
  color: #6b7280;
}

.font-bold {
  font-weight: 600;
}

.mb-8px {
  margin-bottom: 8px;
}

.mt-1 {
  margin-top: 4px;
}
</style>
