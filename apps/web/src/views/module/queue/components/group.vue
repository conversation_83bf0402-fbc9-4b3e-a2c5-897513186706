<template>
  <div class="tb-header">
    <div>
      <el-input
        style="width: 240px"
        placeholder="请输入消费者分组名称"
        v-model="pageParams.consumergroup"
        clearable
      ></el-input>
      <el-button type="primary" style="margin-left: 15px" :icon="Search" @click="search"
        >搜索</el-button
      >
    </div>
  </div>
  <MyTable
    :data="list.records"
    :total="list.total"
    style="width: 100%"
    v-loading="tableLoading"
    @sizeChange="handleSizeChange"
    @currentChange="handleCurrentChange"
    :default-sort="{
      prop: 'topicCount && totalLag && avgLag && maxLag && lastReportTime',
      order: 'descending'
    }"
    @sort-change="handleSortChange"
  >
    <my-column property="consumergroup" label="消费者分组名称">
      <template #default="scope">
        <span class="operate" @click="showDetail(scope.row)">{{ scope.row.consumergroup }}</span>
      </template>
    </my-column>
    <my-column property="clusterName" label="集群名" />
    <my-column property="topicCount" label="关联主题数量" sortable="custom">
      <template v-slot="{ row }">
        <span>
          {{ formatNums(row.topicCount).fixValue }}{{ formatNums(row.topicCount).unit || "" }}
        </span>
      </template>
    </my-column>
    <my-column property="totalLag" label="总Lag" sortable="custom" />
    <my-column property="avgLag" label="平均Lag" sortable="custom">
      <template v-slot="{ row }">
        {{ row.avgLag.toFixed(2) }}
      </template>
    </my-column>
    <my-column property="maxLag" label="最大Lag" sortable="custom" />
    <my-column property="lastReportTime" label="最近上报时间" sortable="custom">
      <template #default="{ row }">
        {{ formatTime(row.lastReportTime) }}
      </template>
    </my-column>
    <my-column label="操作" align="center" header-align="center" fixed="right" width="120">
      <template #default="scope">
        <span class="operate" @click="showDetail(scope.row)">查看详情</span>
      </template>
    </my-column>
  </MyTable>
  <div>
    <el-drawer v-model="detailVisible" :title="detailTitle" size="55%">
      <div v-loading="detailLoading">
        <div class="flex justify-between indicator-wrapper">
          <div v-if="!hasCurrentOffsetTrendData" class="ranking-wrapper w-100%">
            <TitlecCom :title="'消费进度趋势'"></TitlecCom>
            <el-empty description="暂无数据" />
          </div>
          <BaseEcharts v-else :options="currentOffsetTrendChart" width="22vw" height="250px" />
          <div v-if="!hasLagTrendData" class="ranking-wrapper w-100%">
            <TitlecCom :title="'Lag趋势'"></TitlecCom>
            <el-empty description="暂无数据" />
          </div>
          <BaseEcharts v-else :options="lagTrendChart" width="22vw" height="250px" />
        </div>
        <div class="indicator-wrapper w-49.5%">
          <div v-if="!hasMembersTrendData" class="ranking-wrapper w-100%">
            <TitlecCom :title="'成员数趋势'"></TitlecCom>
            <el-empty description="暂无数据" />
          </div>
          <BaseEcharts v-else :options="membersTrendChart" width="22vw" height="250px" />
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { Search } from "@element-plus/icons-vue";
import { formatTime } from "@/utils/dateStr";
import { formatNums } from "@/utils/formatStr";
import { getGroupsPage, getGroupMultiTrend } from "@/api/module/queue";
import { serviceNameStore } from "@/store/modules/service";
import BaseEcharts from "@/components/baseEcharts/index.vue";
import { getChartOptions } from "@/components/baseEcharts/chartsOptions";
import TitlecCom from "@/components/TitleCom/index.vue";
import { useRoute } from "vue-router";
const route = useRoute();
const useServiceNameStore = serviceNameStore();
//loading动画
const tableLoading = ref(false);
const detailLoading = ref(false);

// 图表配置
const currentOffsetTrendChart = ref({});
const lagTrendChart = ref({});
const membersTrendChart = ref({});

// 数据判断变量
const hasCurrentOffsetTrendData = ref(false);
const hasLagTrendData = ref(false);
const hasMembersTrendData = ref(false);
const pageParams = reactive({
  page: 1,
  rows: 10,
  clusterName: "",
  consumergroup: "",
  sort: "",
  order: ""
});

const handleSortChange = (val: any) => {
  const order = val.order;
  const sort = val.prop;
  if (order === "ascending") {
    pageParams.order = "0";
  } else {
    pageParams.order = "1";
  }
  pageParams.sort = sort;
  loadData();
};

//修改每页条数
const handleSizeChange = (val: number) => {
  pageParams.rows = val;
  loadData();
};

//分页
const handleCurrentChange = (val: number) => {
  pageParams.page = val;
  loadData();
};

//搜索
function search() {
  pageParams.page = 1;
  loadData();
}

const list = reactive({
  records: [],
  total: 0
});
function loadData() {
  tableLoading.value = true;
  pageParams.clusterName = useServiceNameStore.serviceName;
  pageParams.consumergroup = pageParams.consumergroup.trim();
  getGroupsPage(pageParams)
    .then(response => {
      if (response.code === 0) {
        list.records = response.records;
        list.total = Number(response.total);
        tableLoading.value = false;
      }
    })
    .catch(error => {
      tableLoading.value = false;
      console.log(error);
    });
}
const detailVisible = ref(false); //抽屉是否显示
const detailTitle = ref(""); //抽屉标题
const showDetail = (row: any) => {
  detailVisible.value = true;
  detailTitle.value = "指标信息 【" + row.consumergroup + "】";
  loadTopicTrendData(row);
};
const loadTopicTrendData = (row: any) => {
  detailLoading.value = true;
  const params = {
    consumergroup: row.consumergroup,
    clusterName: useServiceNameStore.serviceName
  };
  getGroupMultiTrend(params)
    .then(response => {
      if (response.code === 0) {
        if (response.entity && response.entity.multiTrend) {
          updateCharts(response.entity.multiTrend);
          detailLoading.value = false;
        }
      }
    })
    .catch(error => {
      console.error("获取消费者分组趋势数据失败:", error);
      detailLoading.value = false;
    })
    .finally(() => {
      detailLoading.value = false;
    });
};

// 更新图表数据
const updateCharts = (multiTrend: any) => {
  // 重置数据判断变量
  hasCurrentOffsetTrendData.value = false;
  hasLagTrendData.value = false;
  hasMembersTrendData.value = false;

  // 消费进度趋势图
  if (
    multiTrend.kafka_consumergroup_current_offset_sum &&
    multiTrend.kafka_consumergroup_current_offset_sum.length > 0
  ) {
    const times = multiTrend.kafka_consumergroup_current_offset_sum.map((item: any) =>
      formatTime(item.ts)
    );
    const sumValues = multiTrend.kafka_consumergroup_current_offset_sum.map(
      (item: any) => item.sumValue
    );
    const avgValues = multiTrend.kafka_consumergroup_current_offset_sum.map(
      (item: any) => item.avgValue
    );

    currentOffsetTrendChart.value = getChartOptions({
      name: "",
      color: ["#5470C6", "#91CC75"],
      titleType: "消费进度趋势",
      originalTimes: times,
      seriesData: [sumValues, avgValues],
      names: ["合计值", "平均值"],
      type: "line",
      legend: { show: true }
    });
    hasCurrentOffsetTrendData.value = true;
  }

  // Lag趋势图
  if (multiTrend.kafka_consumergroup_lag_sum && multiTrend.kafka_consumergroup_lag_sum.length > 0) {
    const times = multiTrend.kafka_consumergroup_lag_sum.map((item: any) => formatTime(item.ts));
    const sumValues = multiTrend.kafka_consumergroup_lag_sum.map((item: any) => item.sumValue);
    const avgValues = multiTrend.kafka_consumergroup_lag_sum.map((item: any) =>
      item.avgValue.toFixed(2)
    );

    lagTrendChart.value = getChartOptions({
      name: "",
      color: ["#5470C6", "#91CC75"],
      titleType: "Lag趋势",
      originalTimes: times,
      seriesData: [sumValues, avgValues],
      names: ["合计值", "平均值"],
      type: "line",
      legend: { show: true }
    });
    hasLagTrendData.value = true;
  }

  // 成员数趋势图
  if (multiTrend.kafka_consumergroup_members && multiTrend.kafka_consumergroup_members.length > 0) {
    const times = multiTrend.kafka_consumergroup_members.map((item: any) => formatTime(item.ts));
    const sumValues = multiTrend.kafka_consumergroup_members.map((item: any) => item.sumValue);
    const avgValues = multiTrend.kafka_consumergroup_members.map((item: any) => item.avgValue);

    membersTrendChart.value = getChartOptions({
      name: "",
      color: ["#5470C6", "#91CC75"],
      titleType: "成员数趋势",
      originalTimes: times,
      seriesData: [sumValues, avgValues],
      names: ["合计值", "平均值"],
      type: "line",
      legend: { show: true }
    });
    hasMembersTrendData.value = true;
  }
};
onMounted(() => {
  // 如果路由带有consumergroup参数，自动填充并搜索
  if (route.query.consumergroup) {
    pageParams.consumergroup = String(route.query.consumergroup);
    search();
  } else {
    loadData();
  }
});
</script>

<style lang="scss" scoped>
.tb-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.operate {
  color: #0064c8;
  cursor: pointer;
}
:deep(.el-drawer__header) {
  margin: 5px !important;
}
.indicator-wrapper > *:not(:last-child) {
  margin-right: 8px;
  box-sizing: border-box;
}
.ranking-wrapper {
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 2px;
  margin-bottom: 10px;
}
</style>
