<template>
  <TitlecCom :title="`用户管理`" />
  <div class="top-container">
    <div class="search-container">
      <el-cascader
        placeholder="请选择所属单位"
        :props="props1"
        :options="treesData"
        v-model="organIdSearch"
        filterable
        clearable
        style="width: 200px; margin-right: 15px"
      />
      <SearchInput @search="performSearch" @searchChange="searchChange" />
    </div>
    <el-button type="primary" class="topBtn" @click="openDialog(1, null)"> 新增 </el-button>
  </div>
  <MyTable
    :data="tableData"
    :total="totalSize"
    style="width: 100%"
    v-loading="tableLoading1"
    @sizeChange="handleSizeChange"
    @currentChange="handleCurrentChange"
  >
    <MyColumn prop="id" label="用户ID" />
    <MyColumn prop="name" label="用户姓名" />
    <!-- <MyColumn label="用户类型">
          <template #default="scope">
            <span>{{ scope.row.type === 0 ? "管理员" : "普通用户" }}</span>
          </template>
        </MyColumn> -->
    <MyColumn prop="mobile" label="手机号" />
    <MyColumn prop="email" label="邮箱" />
    <MyColumn prop="organName" label="所属单位" />
    <MyColumn label="锁定状态" width="150">
      <template #default="scope">
        <el-tag :type="scope.row.lockStatus === 0 ? 'success' : 'danger'">
          {{ scope.row.lockStatus === 0 ? "未锁定" : "已锁定" }}
        </el-tag>
      </template>
    </MyColumn>
    <MyColumn prop="lastLoginIp" label="最近登录IP" />
    <MyColumn prop="lastLoginTime" label="最近登录时间" />
    <MyColumn prop="createdAt" label="添加时间" />
    <MyColumn label="可用时间">
      <template #default="scope">
        <span>
          {{
            scope.row.startTime && scope.row.endTime
              ? `${scope.row.startTime} ~ ${scope.row.endTime}`
              : "-"
          }}
        </span>
      </template>
    </MyColumn>
    <!-- <MyColumn prop="remark" label="备注" /> -->
    <MyColumn label="操作" width="300" fixed="right" align="center" header-align="center">
      <template #default="scope">
        <span class="action-link" @click="openDialog(5, scope.row)">
          {{ scope.row.lockStatus === 0 ? "锁定" : "解锁" }}
        </span>
        <span> / </span>
        <span class="action-link" @click="openDialog(2, scope.row)">编辑</span>
        <span> / </span>
        <span class="action-link" @click="openDialog(4, scope.row)">删除</span>
        <span> / </span>
        <span class="action-link" @click="openDialog(3, scope.row)">修改密码</span>
        <span> / </span>
        <span class="action-link" @click="openLimitTimeDialog(scope.row)">可用时间</span>
        <!-- <span class="action-link" @click="openDialog(6, scope.row)">已绑定应用</span> -->
      </template>
    </MyColumn>
  </MyTable>

  <el-dialog
    v-model="dialogFormVisible"
    :title="dialogTitle"
    width="700"
    :close-on-click-modal="false"
    :before-close="closeDialog"
    :align-center="true"
  >
    <el-form :model="formData" label-width="120px" ref="formRef" :rules="validationRules">
      <el-row>
        <el-col :span="12">
          <el-form-item v-if="inputType !== 6 && inputType !== 3" label="用户名：">
            <el-input v-model="formData.name" />
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="inputType !== 6 && inputType !== 3">
          <el-form-item label="所属单位" :span="12" v-if="inputType !== 6 && inputType !== 3">
            <el-cascader
              placeholder="请选择所属单位"
              :props="props1"
              :options="treesData"
              v-model="formData.organId"
              clearable
              filterable
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item v-if="inputType !== 6 && inputType !== 3" label="手机：">
            <el-input v-model="formData.mobile" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item v-if="inputType !== 6 && inputType !== 3" label="邮箱：">
            <el-input v-model="formData.email" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row class="center-row">
        <el-col :span="12" v-if="inputType === 3 || inputType === 1">
          <el-form-item :label="passwordLabel" :prop="inputType === 1 ? '' : 'password'">
            <el-input
              v-model="formData.password"
              :style="{ width: inputType === 3 ? '220px' : '' }"
              type="password"
              clearable
              show-password
              :placeholder="passwordPlaceholder"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="inputType === 1">
          <el-form-item label="确认密码：" :prop="inputType === 1 ? '' : 'confirmPassword'">
            <el-input v-model="formData.confirmPassword" type="password" clearable show-password />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row class="center-row">
        <el-col :span="12" v-if="inputType === 3">
          <el-form-item label="确认密码：" prop="confirmPassword">
            <el-input
              v-model="formData.confirmPassword"
              type="password"
              :style="{ width: inputType === 3 ? '220px' : '' }"
              clearable
              show-password
              placeholder="请再次输入密码"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item v-if="inputType !== 3 && inputType !== 6" label="描述：">
            <el-input type="textarea" v-model="formData.remark" :rows="3" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="handleFormSubmit">确定</el-button>
      </div>
    </template>
  </el-dialog>
  <el-dialog
    :align-center="true"
    v-model="AppVisible1"
    draggable
    :title="dialogTitle"
    width="900"
    :close-on-click-modal="false"
    v-if="AppVisible1"
  >
    <div class="search-container">
      <el-cascader
        placeholder="请选择所属单位"
        :props="props1"
        :options="treesData"
        v-model="organId"
        clearable
        filterable
        style="width: 200px; margin-right: 15px"
      />
      <SearchInput @search="AppPerformSearch" />
    </div>
    <MyTable
      ref="multipleTableRef"
      :data="AppData"
      :total="AppTotalSize"
      style="width: 100%"
      height="300"
      v-loading="tableLoading3"
      :showSelect="true"
      @selection-change="handleSelectionChange"
      @sizeChange="AppHandleSizeChange"
      @currentChange="AppHandleCurrentChange"
    >
      <my-column property="name" label="应用名称" />
      <my-column property="organName" label="单位名称" />
      <my-column property="createdAt" label="添加时间" />
    </MyTable>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeDialog1(2)">取消</el-button>
        <el-button type="primary" @click="handleFormSubmit"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
  <el-dialog
    v-model="deleteVisible"
    title="删除用户"
    width="500"
    :close-on-click-modal="false"
    :align-center="true"
  >
    <span>确认要删除吗？</span>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="deleteVisible = false">取消</el-button>
        <el-button type="primary" @click="delUser"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
  <el-dialog
    :align-center="true"
    v-model="lockVisible"
    :title="lockStatus === 1 ? '解锁用户' : '锁定用户'"
    width="500"
    :close-on-click-modal="false"
  >
    <span>{{ `确认要${lockStatus === 1 ? "解锁" : "锁定"}吗？` }}</span>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="lockVisible = false">取消</el-button>
        <el-button type="primary" @click="updateLockUser"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
  <el-dialog
    :align-center="true"
    v-model="AppVisible"
    draggable
    :title="dialogTitle"
    width="1100"
    :close-on-click-modal="false"
  >
    <div class="top-container">
      <div class="search-container">
        <el-cascader
          placeholder="请选择所属单位"
          :props="props1"
          :options="treesData"
          v-model="organAppId"
          clearable
          filterable
          style="width: 200px; margin-right: 15px"
        />
        <SearchInput @search="AppPerformSearch1" />
      </div>
      <!-- <el-button type="primary" @click="openDialog(7)">添加绑定</el-button> -->
    </div>

    <MyTable
      :data="AppData1"
      :total="AppTotalSize1"
      height="300"
      style="width: 100%"
      v-loading="tableLoading2"
      @sizeChange="handleSizeChange1"
      @currentChange="handleCurrentChange1"
    >
      <my-column property="name" label="应用名称" width="300" />
      <my-column property="organName" label="单位名称" />
      <my-column property="contact" label="联系人" />
      <my-column property="mobile" label="联系人手机" />
      <my-column property="createdAt" label="添加时间" />
      <!-- <my-column label="操作" align="center" header-align="center" fixed="right" width="100">
        <template #default="scope">
          <span class="operate" @click="unbind(scope)">解绑</span>
        </template>
      </my-column> -->
    </MyTable>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeDialog1(1)">取消</el-button>
        <el-button type="primary" @click="closeDialog1(1)"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
  <el-dialog
    :align-center="true"
    v-model="unbindVisible"
    title="解绑应用"
    width="500"
    :close-on-click-modal="false"
  >
    <span>确认要解除绑定吗？</span>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="unbindVisible = false">取消</el-button>
        <el-button type="primary" @click="submitBind"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
  <el-dialog
    v-model="limitTimeVisible"
    title="设置可用时间"
    width="500"
    :close-on-click-modal="false"
    :align-center="true"
  >
    <div class="limit-time-options">
      <el-checkbox
        v-model="limitTimeOptions.recentMonth"
        @change="handleMonthChange"
        style="margin-right: 20px"
      >
        一个月
      </el-checkbox>
      <el-checkbox
        v-model="limitTimeOptions.recentThreeMonth"
        @change="handleThreeMonthChange"
        style="margin-right: 20px"
      >
        三个月
      </el-checkbox>
      <el-checkbox
        v-model="limitTimeOptions.recentHalfYear"
        @change="handleHalfYearChange"
        style="margin-right: 20px"
      >
        半年
      </el-checkbox>
      <el-checkbox v-model="limitTimeOptions.recentYear" @change="handleYearChange">
        一年
      </el-checkbox>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="limitTimeVisible = false">取消</el-button>
        <el-button type="primary" @click="handleLimitTimeSubmit">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import TitlecCom from "@/components/TitleCom/index.vue";
import { ref, reactive, computed, onMounted } from "vue";
import { useRouter } from "vue-router";
import {
  userList,
  addUser,
  detailUser,
  updateUser,
  deleteUser,
  changePassword,
  lockUser,
  unlockUser,
  getApp,
  bindApp,
  getTrees
} from "@/api/system/index";
import { getOrganList } from "@/api/system/organ";
import { getOrganEntries, listApp, unBindUser } from "@/api/system/app";
import SearchInput from "@/components/SearchInput/index.vue";
import MyTable from "@/components/table/my-table.vue";
import MyColumn from "@/components/table/my-column.vue";
import type { FormInstance, ElTable } from "element-plus";
import { sm4, sm2 } from "sm-crypto";
import { v4 as uuidv4 } from "uuid";
import { ElMessage } from "element-plus";
// import DateBackDate from '@/layout/components/DateBack/components/Date.vue';
import { setUserLimitTime } from "@/api/system/users";

// 定义用户数据类型
interface User {
  id?: number;
  type?: number;
  name?: string;
  mobile?: string;
  email: string;
  remark?: string;
  createdAt?: string;
  lockStatus?: number;
  password?: string;
  confirmPassword?: string;
  organId?: string;
  startTime?: string;
  endTime?: string;
}
const handleSelectionChange = (selectedRows: any) => {
  let appIdsArr = [];
  appIdsArr = selectedRows.map((item: any) => item.id);
  appIds.value = appIdsArr.join(",");
};
const props1 = {
  checkStrictly: true
};
const userOpstion = { 0: "管理员", 1: "普通用户" };
const entriesOpstion = ref(null);
const appIds = ref(null);
const formRef = ref<FormInstance>();
const router = useRouter();
const tableData = ref<User[]>([]);
const totalSize = ref(0);
const AppData = ref<User[]>([]);
const AppData1 = ref<User[]>([]);
const AppTotalSize = ref(0);
const AppTotalSize1 = ref(0);
const dialogFormVisible = ref(false);
const deleteVisible = ref(false);
const AppVisible = ref(false);
const AppVisible1 = ref(false);
const lockVisible = ref(false);
const deleteUserId = ref(0);
const lockUserId = ref(0);
const AppId = ref(0);
const userId = ref(null);
const lockStatus = ref<number>();
const changePasswordId = ref(0);
const inputType = ref(1);
const tableLoading1 = ref(true);
const tableLoading2 = ref(true);
const tableLoading3 = ref(true);
const searchKeywords = ref("");
const searchKeywords1 = ref("");
const organId = ref("");
const organAppId = ref("");
const userName = ref("");
const multipleTableRef = ref<InstanceType<typeof ElTable>>();
const pageParams = reactive({
  page: 1,
  rows: 10
});
const AppPageParams = reactive({
  page: 1,
  rows: 10
});
const AppPageParams1 = reactive({
  page: 1,
  rows: 10
});
const formData = ref<User>({
  id: undefined,
  type: undefined,
  name: "",
  mobile: "",
  email: "",
  remark: "",
  lockStatus: undefined,
  password: "",
  confirmPassword: "",
  organId: ""
});

// 表单校验规则，仅在修改密码时进行验证
const validationRules = computed(() =>
  inputType.value === 3
    ? {
        password: [{ required: true, message: "请输入密码", trigger: "blur" }],
        confirmPassword: [
          { required: true, message: "请再次输入密码", trigger: "blur" },
          {
            validator: (rule: any, value: any, callback: any) => {
              if (value !== formData.value.password) {
                callback(new Error("两次密码输入不一致"));
              } else {
                callback();
              }
            },
            trigger: "blur"
          }
        ]
      }
    : {}
);

// 对话框标题
const dialogTitle = computed(() => {
  if (inputType.value === 1) return "新增用户";
  if (inputType.value === 2) return "编辑用户";
  if (inputType.value === 3) return "修改密码";
  if (inputType.value === 6) return `【${userName.value}】已绑定的应用`;
  if (inputType.value === 7) return "绑定应用";
  return "";
});
const passwordLabel = computed(() => (inputType.value === 1 ? "密码：" : "新密码："));
const passwordPlaceholder = computed(() => (inputType.value === 1 ? "" : "请填写新密码"));

// 获取用户列表
const organIdSearch = ref("");
const fetchUserList = async (keywords: string = ""): Promise<void> => {
  tableLoading1.value = true;
  try {
    const res = await userList({
      page: pageParams.page,
      rows: pageParams.rows,
      name: keywords || searchKeywords.value,
      type: userId.value || "",
      organId: Array.isArray(organIdSearch.value)
        ? organIdSearch.value.slice(-1)[0]
        : organIdSearch.value
    });
    tableData.value = res.records;
    totalSize.value = Number(res.total);
    tableLoading1.value = false;
    organIdSearch.value = "";
  } catch (error) {
    tableLoading1.value = false;
    console.error("获取用户列表 ===>>>", error);
  }
};

// 获取应用列表
const fetchAppList = async (keywords: string = ""): Promise<void> => {
  tableLoading3.value = true;
  try {
    const res = await listApp({
      page: AppPageParams.page,
      rows: AppPageParams.rows,
      name: keywords || searchKeywords.value,
      organId: Array.isArray(organId.value) ? organId.value.slice(-1)[0] : organId.value
    });
    AppData.value = res.records;
    AppTotalSize.value = Number(res.total);
    tableLoading3.value = false;
  } catch (error) {
    tableLoading3.value = false;
    console.error("获取用户列表 ===>>>", error);
  }
};

// 获取应用列表
const getUserAppList = async (keywords: string = ""): Promise<void> => {
  tableLoading2.value = true;
  try {
    const res = await getApp({
      page: AppPageParams1.page,
      rows: AppPageParams1.rows,
      name: keywords || searchKeywords1.value,
      organId: Array.isArray(organAppId.value) ? organAppId.value.slice(-1)[0] : organAppId.value,
      userId: AppId.value
    });
    AppData1.value = res.records;
    AppTotalSize1.value = Number(res.total);
    tableLoading2.value = false;
    AppVisible.value = true;
  } catch (error) {
    tableLoading2.value = false;
    console.error("获取用户已绑定列表 ===>>>", error);
  }
};

// 应用id查询
const getEntries = async () => {
  try {
    const res = await getOrganEntries({});
    console.log("单位id查询 ===>>>", res);
    entriesOpstion.value = res.records;
  } catch (error) {
    console.error("单位id查询 ===>>>", error);
  }
};

// 解绑
const unbindVisible = ref(false);
const unbindId = ref("");
const unbind = (scope: any) => {
  unbindVisible.value = true;
  unbindId.value = scope.row.id;
};
const submitBind = () => {
  unBindUser(AppId.value, unbindId.value).then(response => {
    if (response.code === 0) {
      getUserAppList();
      ElMessage.success("解绑成功");
      unbindVisible.value = false;
    } else {
      ElMessage.error(response.desc);
    }
  });
};
const limitTimeVisible = ref(false);
const limitTimeRange = ref<string[]>([]);
const limitTimeUserId = ref<number | null>(null);
const limitTimeOptions = reactive({
  recentMonth: false,
  recentThreeMonth: false,
  recentHalfYear: false,
  recentYear: false
});

const handleMonthChange = (checked: any) => {
  if (checked) {
    limitTimeOptions.recentThreeMonth = false;
    limitTimeOptions.recentHalfYear = false;
    limitTimeOptions.recentYear = false;
    const start = new Date();
    const end = new Date();
    end.setMonth(start.getMonth() + 1);
    limitTimeRange.value = [formatDateTime(start), formatDateTime(end)];
  } else {
    limitTimeRange.value = [];
  }
};

const handleThreeMonthChange = (checked: any) => {
  if (checked) {
    limitTimeOptions.recentMonth = false;
    limitTimeOptions.recentHalfYear = false;
    limitTimeOptions.recentYear = false;
    const start = new Date();
    const end = new Date();
    end.setMonth(start.getMonth() + 3);
    limitTimeRange.value = [formatDateTime(start), formatDateTime(end)];
  } else {
    limitTimeRange.value = [];
  }
};

const handleHalfYearChange = (checked: any) => {
  if (checked) {
    limitTimeOptions.recentMonth = false;
    limitTimeOptions.recentThreeMonth = false;
    limitTimeOptions.recentYear = false;
    const start = new Date();
    const end = new Date();
    end.setMonth(start.getMonth() + 6);
    limitTimeRange.value = [formatDateTime(start), formatDateTime(end)];
  } else {
    limitTimeRange.value = [];
  }
};

const handleYearChange = (checked: any) => {
  if (checked) {
    limitTimeOptions.recentMonth = false;
    limitTimeOptions.recentThreeMonth = false;
    limitTimeOptions.recentHalfYear = false;
    const start = new Date();
    const end = new Date();
    end.setFullYear(start.getFullYear() + 1);
    limitTimeRange.value = [formatDateTime(start), formatDateTime(end)];
  } else {
    limitTimeRange.value = [];
  }
};

// 格式化日期时间函数
const formatDateTime = (date: Date) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

const openLimitTimeDialog = (user: User) => {
  limitTimeUserId.value = user.id || null;
  // 重置选项状态
  limitTimeOptions.recentMonth = false;
  limitTimeOptions.recentThreeMonth = false;
  limitTimeOptions.recentHalfYear = false;
  limitTimeOptions.recentYear = false;
  // 回显startTime和endTime
  if (user.startTime && user.endTime) {
    limitTimeRange.value = [user.startTime, user.endTime];
    const startTime = new Date(user.startTime);
    const endTime = new Date(user.endTime);
    // 计算月份差
    const monthDiff =
      (endTime.getFullYear() - startTime.getFullYear()) * 12 +
      (endTime.getMonth() - startTime.getMonth());
    // 计算天数差
    const dayDiff = Math.round((endTime.getTime() - startTime.getTime()) / (1000 * 60 * 60 * 24));
    // 允许一天误差
    if (monthDiff === 1 && (dayDiff === 30 || dayDiff === 31)) {
      limitTimeOptions.recentMonth = true;
    } else if (monthDiff === 3 && dayDiff >= 89 && dayDiff <= 92) {
      limitTimeOptions.recentThreeMonth = true;
    } else if (monthDiff === 6 && dayDiff >= 180 && dayDiff <= 184) {
      limitTimeOptions.recentHalfYear = true;
    } else if (monthDiff === 12 && dayDiff >= 364 && dayDiff <= 366) {
      limitTimeOptions.recentYear = true;
    }
  } else {
    limitTimeRange.value = [];
  }
  limitTimeVisible.value = true;
};

const handleLimitTimeSubmit = async () => {
  if (!limitTimeRange.value || limitTimeRange.value.length !== 2) {
    ElMessage.error("请选择时间范围");
    return;
  }
  try {
    await setUserLimitTime(limitTimeUserId.value!, {
      startTime: limitTimeRange.value[0],
      endTime: limitTimeRange.value[1]
    });
    ElMessage.success("限制时间设置成功");
    limitTimeVisible.value = false;
    fetchUserList();
  } catch (error) {
    console.log(error);
  }
};
onMounted(() => {
  fetchUserList();
  fetchAppList();
  getEntries();
  getOrganTree();
});

// 搜索框改变
const searchChange = (keywords: string) => {
  searchKeywords.value = keywords;
};

// 搜索用户
const performSearch = (keywords: string) => {
  pageParams.page = 1;
  searchKeywords.value = keywords;
  fetchUserList(keywords);
};

// 分页大小改变时处理
const handleSizeChange = (size: number) => {
  pageParams.rows = size;
  fetchUserList();
};

// 当前页码改变时处理
const handleCurrentChange = (page: number) => {
  pageParams.page = page;
  fetchUserList();
};

// 搜索应用
const AppPerformSearch = (keywords: string) => {
  AppPageParams.page = 1;
  searchKeywords.value = keywords;
  fetchAppList(keywords);
};

// 应用分页大小改变时处理
const AppHandleSizeChange = (size: number) => {
  AppPageParams.rows = size;
  fetchAppList();
};

// 应用当前页码改变时处理
const AppHandleCurrentChange = (page: number) => {
  AppPageParams.page = page;
  fetchAppList();
};

//已绑定应用修改每页条数
const handleSizeChange1 = (val: number) => {
  pageParams.rows = val;
  getUserAppList();
};

//已绑定应用分页
const handleCurrentChange1 = (val: number) => {
  pageParams.page = val;
  getUserAppList();
};

// 搜索已绑定应用
const AppPerformSearch1 = (keywords: string) => {
  AppPageParams.page = 1;
  searchKeywords1.value = keywords;
  getUserAppList(keywords);
};

// 打开对话框并设置类型
const openDialog = async (type: number, user: User | null) => {
  inputType.value = type;
  dialogFormVisible.value = false;

  if (type === 4) {
    // 删除用户
    deleteVisible.value = true;
    deleteUserId.value = user?.id ?? 0;
  } else if (type === 5) {
    // 改变用户状态
    lockVisible.value = true;
    lockStatus.value = user?.lockStatus;
    lockUserId.value = user?.id ?? 0;
  } else if (type === 6) {
    AppId.value = user?.id ?? 0;
    userName.value = user?.name ?? "";
    getUserAppList();
  } else if (type === 7) {
    AppVisible1.value = true;
  } else {
    dialogFormVisible.value = true;
    if (type === 2 && user?.id) {
      // 请求用户详细信息
      try {
        const res = await detailUser(user.id);
        if (res.code === 0) {
          formData.value = { ...res.entity };
          formData.value.type = formData.value.type === 0 ? "管理员" : "普通用户";
        }
      } catch (err) {
        console.error("Failed to fetch user details:", err);
      }
    } else {
      if (user) {
        formData.value = { ...user };
        if (type === 3) {
          // 修改密码
          changePasswordId.value = user.id!;
          formData.value.password = "";
          formData.value.confirmPassword = "";
        }
      } else {
        formData.value = {
          id: undefined,
          type: undefined,
          name: "",
          mobile: "",
          email: "",
          remark: "",
          lockStatus: undefined,
          password: "",
          confirmPassword: ""
        };
      }
    }
  }
};

// 提交表单
const handleFormSubmit = async () => {
  if (inputType.value === 7) {
    // 绑定应用
    try {
      const res = await bindApp({ id: AppId.value, appIds: appIds.value });
      if (res.code === 0) {
        ElMessage.success("绑定成功");
        AppVisible1.value = false;
        organId.value = "";
        getUserAppList();
      }
    } catch (error) {
      console.error("获取用户列表 ===>>>", error);
    }
  }
  if (!formRef.value) return;

  formRef.value.validate(async valid => {
    if (!valid) return;

    try {
      if (inputType.value === 1) {
        // 新增用户
        if (formData.value.password) {
          if (formData.value.password !== formData.value.confirmPassword) {
            ElMessage.error("两次密码输入不一致");
            return;
          }
        } else {
          ElMessage.error("请输入密码");
          return;
        }
        formData.value.organId = Array.isArray(formData.value.organId)
          ? formData.value.organId.slice(-1)[0]
          : formData.value.organId;
        const res = await addUser(formData.value);
        if (res.code === 0) {
          ElMessage.success("新增用户成功");
        }
      } else if (inputType.value === 2) {
        // 编辑用户
        const typeNum = formData.value.type == 0 || formData.value.type == 1;

        const parameter = {
          ...formData.value,
          type: typeNum ? formData.value.type : formData.value.type === "管理员" ? 0 : 1,
          organId: Array.isArray(formData.value.organId)
            ? formData.value.organId.slice(-1)[0]
            : formData.value.organId
        };
        const res = await updateUser(parameter);
        if (res.code === 0) {
          ElMessage.success("编辑用户成功");
        }
      } else if (inputType.value === 3) {
        // 修改密码
        // 生成 16 字节的 SM4 密钥
        const sm4Key = uuidv4().replace(/-/g, "");
        // 用 SM2 加密 SM4 密钥
        const sm2PublicKey =
          "04D969AAF5ECCFFFC381674E6C3E46A0F81B761F162A6EED0795136F0200C7C750273BBC393BADC723685F81E1137D5BCC270B7341D01574E6C4943BD56AF11E55";
        const encryptedPassword = sm4.encrypt(formData.value.password!.trim(), sm4Key);
        const desUuid = sm2.doEncrypt(sm4Key, sm2PublicKey, 1);

        const res = await changePassword({
          id: changePasswordId.value,
          password: encryptedPassword,
          aesKey: desUuid
        });
        if (res.code === 0) {
          ElMessage.success("修改密码成功");
        }
      }
      dialogFormVisible.value = false;
      fetchUserList();
    } catch (error) {
      console.error("提交 ===>>>", error);
    }
  });
};

// 关闭对话框
const closeDialog = () => {
  dialogFormVisible.value = false;
};
const closeDialog1 = (type: number) => {
  if (type === 1) {
    AppVisible.value = false;
    organAppId.value = "";
  } else if (type === 2) {
    AppVisible1.value = false;
    organId.value = "";
  }
  searchKeywords1.value = "";
};

// 删除用户
const delUser = async () => {
  try {
    const res = await deleteUser(deleteUserId.value);
    if (res.code === 0) {
      ElMessage.success("删除用户成功");
    }
    deleteVisible.value = false;
    fetchUserList();
  } catch (error) {
    console.error("删除用户 ===>>>", error);
  }
};

// 锁定或解锁用户
const updateLockUser = async () => {
  try {
    if (lockStatus.value === 1) {
      const res = await unlockUser(lockUserId.value);
      if (res.code === 0) {
        ElMessage.success("已解锁");
      }
    } else {
      const res = await lockUser(lockUserId.value);
      if (res.code === 0) {
        ElMessage.success("已锁定");
      }
    }
    lockVisible.value = false;
    fetchUserList();
  } catch (error) {
    console.error(`${lockStatus.value === 1 ? "解锁" : "锁定"} 用户 ===>>>`, error);
  }
};
const treesData = ref([]);
interface Tree {
  label: string;
  children?: Tree[];
}

// 获取组织结构
const getOrganTree = async () => {
  const params = {
    name: "",
    status: 1
  };
  try {
    const res = await getOrganList(params);
    if (res.code === 0) {
      const processTreeData = (data: any[]): Tree[] => {
        return data.map(item => {
          const node: Tree = {
            label: item.name,
            value: item.id,
            id: item.id,
            children: item.children ? processTreeData(item.children) : []
          };
          return node;
        });
      };

      treesData.value = processTreeData(res.records || []);
    }
  } catch (error) {
    console.error("获取组织结构 ===>>>", error);
  }
};
</script>

<style scoped>
.top-container {
  display: flex;
  justify-content: space-between;
}
.search-container {
  display: flex;
  margin-bottom: 15px;
}
.topBtn {
  margin-left: 10px;
}

.action-link {
  color: #0064c8;
  cursor: pointer;
}

.dialog-footer {
  text-align: right;
}
.title {
  font-size: 16px;
  font-weight: 550;
  padding-bottom: 16px;
  display: flex;
  align-items: center;
}
.mark {
  margin-right: 10px;
  width: 4px;
  height: 15px;
  background: #0064c8;
}
.center-row {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}
.operate {
  color: #0064c8;
  cursor: pointer;
}
.show-container {
  display: flex;
  justify-content: space-between;
}
.left-container {
  width: 19%;
  /* border: 1px solid #000; */
  /* padding: 20px 10px; */
}
.right-container {
  width: 80%;
}
.limit-time-options {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}
</style>
