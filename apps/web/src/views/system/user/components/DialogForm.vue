<template>
  <el-dialog
    v-model="isDialogVisible"
    :title="isEditMode"
    width="500"
    :before-close="closeDialog"
    :align-center="true"
  >
    <el-form @submit.prevent="handleSubmit">
      <el-form-item
        label="用户名"
        :rules="[{ required: true, message: '请输入用户名' }]"
        v-if="!changePassword"
      >
        <el-input v-model="formData.name" placeholder="请输入用户名" />
      </el-form-item>
      <el-form-item
        label="手机号"
        :rules="[{ required: true, message: '请输入手机号' }]"
        v-if="!changePassword"
      >
        <el-input v-model="formData.mobile" placeholder="请输入手机号" />
      </el-form-item>
      <el-form-item
        label="邮箱"
        :rules="[{ required: true, message: '请输入邮箱' }]"
        v-if="!changePassword"
      >
        <el-input v-model="formData.email" placeholder="请输入邮箱" />
      </el-form-item>
      <el-form-item
        label="状态"
        :rules="[{ required: true, message: '请选择状态' }]"
        v-if="!changePassword"
      >
        <el-select v-model="formData.lockStatus" placeholder="选择状态">
          <el-option label="启用" value="1"></el-option>
          <el-option label="禁用" value="0"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="描述" v-if="!changePassword">
        <el-input type="textarea" v-model="formData.remark" placeholder="请输入描述" />
      </el-form-item>
      <el-form-item label="新密码" v-if="changePassword">
        <el-input type="password" v-model="formData.password" placeholder="请输入新密码" />
      </el-form-item>
      <el-form-item label="确认密码" v-if="changePassword">
        <el-input type="password" v-model="formData.confirmPassword" placeholder="请再次输入密码" />
      </el-form-item>
      <div class="dialog-footer">
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="handleSubmit">提交</el-button>
      </div>
    </el-form>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, PropType, watch } from "vue";

interface FormData {
  name: string;
  mobile: string;
  email: string;
  lockStatus: string;
  remark?: string;
  password?: string;
  confirmPassword?: string;
}

const props = defineProps({
  visible: {
    type: Boolean,
    required: true
  },
  isEditMode: {
    type: String
  },
  changePassword: {
    type: Boolean,
    default: false
  },
  initialData: {
    type: Object as PropType<FormData>,
    default: () => ({
      name: "",
      mobile: "",
      email: "",
      lockStatus: "",
      remark: "",
      password: "",
      confirmPassword: ""
    })
  }
});

const emit = defineEmits<{
  (e: "update:visible", visible: boolean): void;
  (e: "submit", formData: FormData): void;
}>();

const isDialogVisible = ref(props.visible);
const formData = ref<FormData>({ ...props.initialData });
const changePassword = ref(props.changePassword);

watch(
  () => props.visible,
  newVal => {
    isDialogVisible.value = newVal;
  }
);

watch(
  () => props.initialData,
  newVal => {
    formData.value = { ...newVal };
  }
);

const handleSubmit = () => {
  emit("submit", formData.value);
  closeDialog();
};

const closeDialog = () => {
  isDialogVisible.value = false;
  emit("update:visible", false);
  resetFormData();
};

const resetFormData = () => {
  formData.value = { ...props.initialData };
};
</script>
