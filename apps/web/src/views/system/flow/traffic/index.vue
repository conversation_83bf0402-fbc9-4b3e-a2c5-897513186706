<template>
  <div class="title">
    <div class="mark"></div>
    <div>流量设置</div>
  </div>
  <div class="tb-header">
    <div>
      <el-select
        style="width: 240px"
        placeholder="请选择所属单位"
        v-model="pageParams.organId"
        clearable
        filterable
      >
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-select
        style="width: 240px; margin-left: 15px"
        v-model="pageParams.appid"
        clearable
        filterable
        placeholder="请选择应用"
      >
        <el-option v-for="app in apps" :key="app.appid" :label="app.name" :value="app.appid" />
      </el-select>
      <el-button type="primary" style="margin-left: 15px" :icon="Search" @click="search"
        >搜索</el-button
      >
    </div>
    <div>
      <el-button type="primary" @click="addIp">新增</el-button>
    </div>
  </div>
  <MyTable
    :data="list.records"
    :total="list.total"
    style="width: 100%"
    v-loading="tableLoading"
    @sizeChange="handleSizeChange"
    @currentChange="handleCurrentChange"
  >
    <my-column property="organName" label="单位名称" />
    <my-column property="appid" label="应用ID" />
    <my-column property="appName" label="应用名称" />
    <my-column property="ip" label="IP/网段" width="245" />
    <my-column property="remark" label="备注" />
    <my-column property="createTime" label="添加时间" />
    <my-column property="isShunt" label="是否分流">
      <template #default="scope">
        <el-tag :type="scope.row.isShunt === 0 ? 'danger' : 'success'">
          {{ scope.row.isShunt === 0 ? "否" : "是" }}
        </el-tag>
      </template>
    </my-column>
    <my-column label="操作" align="center" header-align="center" fixed="right" width="150">
      <template #default="scope">
        <span class="operate" @click="editIp(scope.row)">编辑</span>
        <span class="divider"> / </span>
        <span class="operate" @click="deleteIp(scope.row.id)">删除</span>
      </template>
    </my-column>
  </MyTable>
  <!-- 删除弹窗-->
  <el-dialog
    :align-center="true"
    v-model="deleteVisible"
    title="温馨提示"
    width="500"
    :close-on-click-modal="false"
  >
    <span>确认要删除吗？</span>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="deleteVisible = false">取消</el-button>
        <el-button type="primary" @click="submitBind"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
  <!-- 新增编辑弹窗-->
  <el-dialog
    v-model="dialogFormVisible"
    :align-center="true"
    :title="title"
    width="500"
    :close-on-click-modal="false"
  >
    <el-form :model="state.form" label-width="auto">
      <el-form-item label="所属单位：" required>
        <el-select
          v-model="state.form.organId"
          clearable
          filterable
          :disabled="isEditMode"
          placeholder="请选择所属单位"
        >
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="应用：" required>
        <el-select
          v-model="state.form.appid"
          clearable
          filterable
          :disabled="isEditMode"
          placeholder="请选择应用"
        >
          <el-option v-for="app in apps" :key="app.appid" :label="app.name" :value="app.appid">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="IP/网段：" required>
        <el-input
          type="textarea"
          v-model="state.form.ip"
          clearable
          :rows="3"
          placeholder="请输入单个IP或网段(例如:***********或***********/24)"
        />
      </el-form-item>
      <el-form-item label="是否分流：" required>
        <el-radio-group v-model="state.form.isShunt">
          <el-radio :value="1">是</el-radio>
          <el-radio :value="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="state.form.isShunt === 1" label="优先级" required>
        <el-input-number
          v-model="state.form.priority"
          placeholder="请输入优先级（1-100）"
          :min="1"
          :max="100"
          clearable
        />
      </el-form-item>
      <el-form-item v-if="state.form.isShunt === 1" label="协议类型" required>
        <el-select v-model="state.form.agreementType" placeholder="请选择">
          <el-option label="VXLAN" value="VXLAN" />
          <el-option label="GRE" value="GRE" />
        </el-select>
      </el-form-item>
      <el-form-item
        v-if="state.form.isShunt === 1 && state.form.agreementType === 'VXLAN'"
        label="VNI"
        required
      >
        <el-input
          v-model="state.form.vni"
          placeholder="请输入 VNI（只能是数字）"
          clearable
          type="number"
        />
      </el-form-item>
      <el-form-item
        v-if="state.form.isShunt === 1 && state.form.agreementType === 'VXLAN'"
        label="远程 VTEP"
        required
      >
        <el-input v-model="state.form.vtep" placeholder="192.168.1.2:9990" clearable />
      </el-form-item>
      <el-form-item
        v-if="state.form.isShunt === 1 && state.form.agreementType === 'GRE'"
        label="远程端点"
        required
      >
        <el-input v-model="state.form.remoteEndpoint" placeholder="192.168.1.2" clearable />
      </el-form-item>
      <el-form-item
        v-if="state.form.isShunt === 1 && state.form.agreementType === 'GRE'"
        label="GRE KEY"
        required
      >
        <el-input
          v-model="state.form.greKey"
          placeholder="请输入 KEY（只能是数字）"
          clearable
          type="number"
        />
      </el-form-item>
      <el-form-item v-if="state.form.isShunt === 1" label="描述" required>
        <el-input
          v-model="state.form.description"
          placeholder="输入描述（最多40个字）"
          :maxlength="40"
          clearable
        />
      </el-form-item>
      <el-form-item label="备注：">
        <el-input
          v-model="state.form.remark"
          type="textarea"
          clearable
          :rows="3"
          placeholder="请输入备注信息"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="formCommit">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { Search } from "@element-plus/icons-vue";
import MyTable from "@/components/table/my-table.vue";
import MyColumn from "@/components/table/my-column.vue";
import {
  ipConfigList,
  ipConfigDelete,
  appName,
  ipConfigDetail,
  addIpConfig,
  editIpConfig
} from "@/api/system/flow";
import { getOrganEntries } from "@/api/system/app";
//所属单位
const options = ref([] as any[]);
//删除弹窗是否显示
const deleteVisible = ref(false);
//新增编辑弹窗是否显示
const dialogFormVisible = ref(false);
//新增编辑弹窗标题
const title = ref("");
//loading动画
const tableLoading = ref(true);
//列表参数
const pageParams = reactive({
  page: 1,
  rows: 10,
  appid: "",
  organId: ""
});
//修改每页条数
const handleSizeChange = (val: number) => {
  pageParams.rows = val;
  loadData();
};
//分页
const handleCurrentChange = (val: number) => {
  pageParams.page = val;
  loadData();
};
//搜索
function search() {
  pageParams.page = 1;
  loadData();
}
const list = reactive({
  records: [],
  total: 0
});
//列表数据
function loadData() {
  tableLoading.value = true;
  ipConfigList(pageParams)
    .then(response => {
      if (response.code === 0) {
        list.records = response.records;
        list.total = Number(response.total);
        tableLoading.value = false;
      }
    })
    .catch(error => {
      tableLoading.value = false;
      console.log(error);
    });
}
//新增编辑删除数据
const state = reactive({
  delId: null as any,
  form: {
    appid: "",
    ip: "",
    remark: ""
  }
} as any);
//点击删除
function deleteIp(id: any) {
  state.delId = id;
  deleteVisible.value = true;
}
//删除数据
function submitBind() {
  deleteVisible.value = false;
  ipConfigDelete(state.delId).then(response => {
    if (response.code === 0) {
      ElMessage.success("删除成功");
      loadData();
    } else {
      ElMessage.error(response.desc);
    }
  });
}
//获取应用
const apps = ref([]);
const fetchApps = async () => {
  try {
    const response = await appName();
    if (response.code === 0) {
      apps.value = response.records;
    }
  } catch (error) {
    console.error("获取应用数据异常:", error);
  }
};
//应用下拉框是否禁用
const isEditMode = ref(false);
//点击新增
const addIp = () => {
  dialogFormVisible.value = true;
  title.value = "新增流量设置";
  isEditMode.value = false;
  state.form = {
    appid: "",
    ip: "",
    remark: "",
    isShunt: 0,
    priority: "",
    agreementType: "VXLAN",
    vni: "",
    vtep: "",
    remoteEndpoint: "",
    greKey: "",
    description: "",
    organId: ""
  };
};
//点击删除
const editIp = (row: any) => {
  dialogFormVisible.value = true;
  title.value = "编辑流量设置";
  isEditMode.value = true; // 编辑时禁用状态为true
  ipConfigDetail(row.id).then(res => {
    state.form = res.entity;
  });
};
//新增编辑数据
function formCommit() {
  if (state.form.id) {
    editIpConfig(state.form).then(response => {
      if (response.code === 0) {
        ElMessage.success("修改成功");
        dialogFormVisible.value = false;
        loadData();
      } else {
        ElMessage.error(response.desc);
      }
    });
  } else
    addIpConfig({ ...state.form }).then(response => {
      if (response.code === 0) {
        ElMessage.success("新增成功");
        dialogFormVisible.value = false;
        loadData();
      } else {
        ElMessage.error(response.desc);
      }
    });
}
//获取单位条目
const OrganEntries = async () => {
  try {
    const res = await getOrganEntries({});
    options.value = res.records;
  } catch (err) {
    console.log(err);
  }
};
onMounted(() => {
  fetchApps();
  loadData();
  OrganEntries();
});
</script>

<style lang="scss" scoped>
.tb-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.title {
  font-size: 16px;
  font-weight: 550;
  padding-bottom: 16px;
  display: flex;
  align-items: center;
}
.mark {
  margin-right: 10px;
  width: 4px;
  height: 15px;
  background: #0064c8;
}
.operate {
  color: #0064c8;
  cursor: pointer;
}
</style>
