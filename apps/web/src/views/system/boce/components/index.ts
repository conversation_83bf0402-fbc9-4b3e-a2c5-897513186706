export const descriptionItems = [
  { label: "检测点", field: "hostName" },
  { label: "IP地址", field: "ip" },
  { label: "发送次数", field: "sent" },
  { label: "接收次数", field: "recv" },
  { label: "丢包率", field: "packetLoss", unit: "%" },
  { label: "HTTP状态码", field: "statusCode" },
  { label: "最小响应时间", field: "minTime", unit: "ms" },
  { label: "最大响应时间", field: "maxTime", unit: "ms" },
  { label: "平均响应时间", field: "avgTime", unit: "ms" },
  { label: "DNS检测结果", field: "dnsStatus" },
  { label: "详细信息", field: "pingOutput", special: true }
];

// 处理状态码
export const getStatusTag = (status: number) => {
  const map = {
    1: { label: "创建中", type: "info" },
    2: { label: "运行中", type: "success" },
    3: { label: "运行异常", type: "danger" },
    4: { label: "暂停中", type: "warning" },
    5: { label: "暂停异常", type: "danger" },
    6: { label: "任务暂停", type: "warning" },
    7: { label: "任务删除中", type: "info" },
    8: { label: "任务删除异常", type: "danger" },
    9: { label: "任务已删除", type: "default" },
    10: { label: "定时任务暂停中", type: "warning" }
  };
  return map[status] || { label: "未知状态", type: "default" };
};

// 处理IP类型
export const formatNodeIpType = (type: number | null | undefined): string => {
  const map = {
    0: "不限",
    1: "IPv4",
    2: "IPv6"
  };
  return map[type ?? 0] || "未知类型";
};

// 拨测类型
export const TaskTypeMap = [
  { value: 0, label: "状态未指定" },
  { value: 1, label: "已调度" },
  { value: 2, label: "分发待定" },
  { value: 3, label: "已完成" },
  { value: 4, label: "分发失败" }
];
// ip类型
export const ipType = [
  { value: 0, label: "自动" },
  { value: 1, label: "ipv4" },
  { value: 2, label: "ipv6" }
];

export const netType = [
  { value: 0, label: "不启用" },
  { value: 1, label: "启用" }
];

// 处理拨测类型返回值
export const formatTaskType = (type: number): string => {
  const task = TaskTypeMap.find(item => item.value == type);
  return task ? task.label : "未知类型";
};

// 处理表单提交
export function formatCreateFormData(form: any) {
  console.log(form);

  const defaultFormData = {
    batchTasks: [
      {
        name: "",
        targetAddress: ""
      }
    ],
    taskType: 4,
    nodes: form.nodes,
    interval: 5,
    parameters: JSON.stringify({
      ipType: 0,
      grabBag: 0,
      netIcmpOn: 1,
      netIcmpActivex: 0,
      netIcmpTimeout: 20,
      netIcmpInterval: 0.5,
      netIcmpNum: 20,
      netIcmpSize: 32,
      netIcmpDataCut: 1
    }),
    taskCategory: 2,
    cron: "* 0-5 * * *",
    tag: [],
    probeType: null,
    pluginSource: null,
    clientNum: null,
    nodeIpType: null,
    subSyncFlag: null
  };

  return {
    ...defaultFormData,
    batchTasks: [
      {
        name: form.name,
        targetAddress: form.targetAddress
      }
    ],
    taskType: form.taskType ?? defaultFormData.taskType,
    interval: form.interval ?? defaultFormData.interval,
    tag:
      form.tagKey && form.tagValue
        ? [
            {
              tagKey: form.tagKey,
              tagValue: form.tagValue
            }
          ]
        : []
  };
}
