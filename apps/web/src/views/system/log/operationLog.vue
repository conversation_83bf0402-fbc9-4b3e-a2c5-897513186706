<template>
  <div class="title">
    <div class="mark"></div>
    <div>操作日志</div>
  </div>
  <div class="tb-header">
    <div>
      <el-select
        style="width: 240px"
        v-model="pageParams.httpMethod"
        placeholder="请选择HTTP方式"
        clearable
        filterable
      >
        <el-option label="GET" value="GET"></el-option>
        <el-option label="POST" value="POST"></el-option>
        <el-option label="PUT" value="PUT"></el-option>
        <el-option label="DELETE" value="DELETE"></el-option>
      </el-select>
      <el-select
        style="width: 240px; margin-left: 15px"
        v-model="pageParams.platform"
        placeholder="请选择操作平台"
        clearable
        filterable
      >
        <el-option label="Mac" value="Mac"></el-option>
        <el-option label="Windows" value="Windows"></el-option>
        <el-option label="Android" value="Android"></el-option>
        <el-option label="iPhone" value="iPhone"></el-option>
        <el-option label="Linux" value="Linux"></el-option>
        <el-option label="Java" value="Java"></el-option>
      </el-select>
      <el-select
        style="width: 240px; margin-left: 15px"
        v-model="pageParams.status"
        placeholder="请选择状态"
        clearable
        filterable
      >
        <el-option label="正常" :value="true"></el-option>
        <el-option label="异常" :value="false"></el-option>
      </el-select>
      <el-button type="primary" style="margin-left: 15px" :icon="Search" @click="search"
        >搜索</el-button
      >
    </div>
  </div>
  <MyTable
    :data="list.records"
    :total="list.total"
    style="width: 100%"
    v-loading="tableLoading"
    @sizeChange="handleSizeChange"
    @currentChange="handleCurrentChange"
  >
    <my-column property="ip" label="IP" />
    <my-column property="location" label="登录地" />
    <my-column property="action" label="请求方法" />
    <my-column property="module" label="模块" />
    <my-column property="description" label="描述信息" />
    <my-column property="status" label="状态">
      <template #default="{ row }">
        <el-tag :type="row.status == true ? 'success' : 'danger'">
          {{ row.status == true ? "正常" : "异常" }}
        </el-tag>
      </template>
    </my-column>
    <my-column property="httpMethod" label="HTTP方式" />
    <my-column property="platform" label="操作平台" />
    <my-column property="os" label="操作系统" />
    <my-column property="engine" label="引擎类型" />
    <my-column property="browser" label="浏览器" />
    <my-column property="createdName" label="操作人" />
    <my-column property="startTime" label="开始时间">
      <template #default="{ row }">
        <span>{{ dayjs(row.startTime).format("YYYY-MM-DD HH:mm:ss") }}</span>
      </template>
    </my-column>
    <my-column property="endTime" label="结束时间">
      <template #default="{ row }">
        <span>{{ dayjs(row.endTime).format("YYYY-MM-DD HH:mm:ss") }}</span>
      </template>
    </my-column>
    <my-column property="duration" label="耗时">
      <template #default="{ row }">
        <span>{{ row.duration }} ms</span>
      </template>
    </my-column>
    <my-column label="操作" align="center" header-align="center" fixed="right" width="80">
      <template #default="scope">
        <span class="operate" @click="viewDetail(scope.row)">查看</span>
      </template>
    </my-column>
  </MyTable>
  <div>
    <el-drawer v-model="logVisible" title="查看详情" size="50%">
      <div class="attr-panel">
        <div class="tab-content">
          <table class="detail-table">
            <tbody>
              <tr>
                <td class="label">IP</td>
                <td width="35%">{{ detailList.ip }}</td>
                <td class="label">登录地</td>
                <td width="35%">{{ detailList.location }}</td>
              </tr>
              <tr>
                <td class="label">模块</td>
                <td width="35%">{{ detailList.module }}</td>
                <td class="label">状态</td>
                <td width="35%">
                  <el-tag :type="detailList.status == true ? 'success' : 'danger'">
                    {{ detailList.status == true ? "正常" : "异常" }}
                  </el-tag>
                </td>
              </tr>
              <tr>
                <td class="label">描述信息</td>
                <td width="35%">{{ detailList.description }}</td>
                <td class="label">请求方法</td>
                <td width="35%">{{ detailList.action }}</td>
              </tr>
              <tr>
                <td class="label">HTTP方式</td>
                <td width="35%">{{ detailList.httpMethod }}</td>
                <td class="label">操作平台</td>
                <td width="35%">{{ detailList.platform }}</td>
              </tr>
              <tr>
                <td class="label">操作系统</td>
                <td width="35%">{{ detailList.os }}</td>
                <td class="label">引擎类型</td>
                <td width="35%">{{ detailList.engine }}</td>
              </tr>
              <tr>
                <td class="label">浏览器</td>
                <td width="35%">{{ detailList.browser }}</td>
                <td class="label">操作人</td>
                <td width="35%">{{ detailList.createdName }}</td>
              </tr>
              <tr>
                <td class="label">开始时间</td>
                <td width="35%">{{ dayjs(detailList.startTime).format("YYYY-MM-DD HH:mm:ss") }}</td>
                <td class="label">结束时间</td>
                <td width="35%">{{ dayjs(detailList.endTime).format("YYYY-MM-DD HH:mm:ss") }}</td>
              </tr>
              <tr>
                <td class="label">耗时</td>
                <td width="35%">{{ detailList.duration }} ms</td>
                <td class="label">请求路径</td>
                <td width="35%">{{ detailList.uri }}</td>
              </tr>
              <tr>
                <td class="label">请求参数</td>
                <td colspan="3">{{ detailList.request }}</td>
              </tr>
              <tr>
                <td class="label">响应结果</td>
                <td colspan="3">{{ detailList.response }}</td>
              </tr>
              <tr v-if="detailList.status == false">
                <td class="label">异常信息</td>
                <td colspan="3">
                  <div style="white-space: pre-wrap; word-break: break-word">
                    {{ detailList.message }}
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </el-drawer>
  </div>
</template>
<script setup lang="ts">
import { optLogs } from "@/api/system/log";
import dayjs from "dayjs";
import { Search } from "@element-plus/icons-vue";
const logVisible = ref(false);
const detailList = ref({
  ip: "",
  location: "",
  module: "",
  status: true,
  description: "",
  action: "",
  httpMethod: "",
  platform: "",
  os: "",
  engine: "",
  browser: "",
  createdName: "",
  startTime: "",
  endTime: "",
  duration: "",
  uri: "",
  request: "",
  response: "",
  message: ""
});
function viewDetail(row: any) {
  detailList.value = row;
  logVisible.value = true;
}
const pageParams = reactive({
  page: 1,
  rows: 10,
  httpMethod: "",
  platform: "",
  status: ""
});
const tableLoading = ref(false);
const handleSizeChange = (val: number) => {
  pageParams.rows = val;
  loadData();
};
const handleCurrentChange = (val: number) => {
  pageParams.page = val;
  loadData();
};
function search() {
  pageParams.page = 1;
  loadData();
}
const list = reactive({
  records: [],
  total: 0
});
function loadData() {
  tableLoading.value = true;
  optLogs(pageParams)
    .then(response => {
      if (response.code === 0) {
        list.records = response.records;
        list.total = Number(response.total);
        tableLoading.value = false;
      }
    })
    .catch(error => {
      tableLoading.value = false;
      console.log(error);
    });
}
onMounted(() => {
  loadData();
});
</script>
<style lang="scss" scoped>
.title {
  font-size: 16px;
  font-weight: 550;
  padding-bottom: 16px;
  display: flex;
  align-items: center;
}
.mark {
  margin-right: 10px;
  width: 4px;
  height: 15px;
  background: #0064c8;
}
.tb-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.operate {
  color: #0064c8;
  cursor: pointer;
}
.detail-table {
  width: 100%;
  color: #606266;
  font-size: 14px;
  border: 1px solid #ebeef5;
  border-collapse: collapse;

  tr {
    td {
      border: 1px solid #ebeef5;
      word-break: break-all;
      padding: 15px;
    }

    td.label {
      background: #f5f7fa;
      width: 120px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}
:deep(.el-drawer__header) {
  margin: 5px !important;
}
:deep(.attr-panel) {
  margin: 0 !important;
}
</style>
