<template>
  <div class="title">
    <div class="mark"></div>
    <div>登录日志</div>
  </div>
  <div class="tb-header">
    <div>
      <el-input
        style="width: 240px"
        v-model="pageParams.principal"
        placeholder="请输入账号"
        clearable
        filterable
      >
      </el-input>
      <el-input
        style="width: 240px; margin-left: 15px"
        v-model="pageParams.createdName"
        placeholder="请输入登录人"
        clearable
        filterable
      >
      </el-input>
      <el-select
        style="width: 240px; margin-left: 15px"
        v-model="pageParams.platform"
        placeholder="请选择操作平台"
        clearable
        filterable
      >
        <el-option label="Mac" value="Mac"></el-option>
        <el-option label="Windows" value="Windows"></el-option>
        <el-option label="Android" value="Android"></el-option>
        <el-option label="iPhone" value="iPhone"></el-option>
        <el-option label="Linux" value="Linux"></el-option>
        <el-option label="Java" value="Java"></el-option>
      </el-select>
      <el-button type="primary" style="margin-left: 15px" :icon="Search" @click="search"
        >搜索</el-button
      >
    </div>
  </div>
  <MyTable
    :data="list.records"
    :total="list.total"
    style="width: 100%"
    v-loading="tableLoading"
    @sizeChange="handleSizeChange"
    @currentChange="handleCurrentChange"
  >
    <my-column property="principal" label="账号" />
    <my-column property="createdName" label="登录人" />
    <my-column property="createdTime" label="登录时间">
      <template #default="{ row }">
        <span>{{ dayjs(row.createdTime).format("YYYY-MM-DD HH:mm:ss") }}</span>
      </template>
    </my-column>
    <my-column property="loginType" label="登录类型" />
    <my-column property="ip" label="IP" />
    <my-column property="location" label="登录地" />
    <my-column property="platform" label="操作平台" />
    <my-column property="os" label="操作系统" />
    <my-column property="engine" label="引擎类型" />
    <my-column property="browser" label="浏览器" />
  </MyTable>
</template>
<script setup lang="ts">
import dayjs from "dayjs";
import { Search } from "@element-plus/icons-vue";
import { loginLogs } from "@/api/system/log";
const tableLoading = ref(false);
const pageParams = reactive({
  page: 1,
  rows: 10,
  principal: "",
  createdName: "",
  platform: ""
});
//修改每页条数
const handleSizeChange = (val: number) => {
  pageParams.rows = val;
  loadData();
};
//分页
const handleCurrentChange = (val: number) => {
  pageParams.page = val;
  loadData();
};
function search() {
  pageParams.page = 1;
  loadData();
}
const list = reactive({
  records: [],
  total: 0
});
function loadData() {
  tableLoading.value = true;
  pageParams.createdName = pageParams.createdName.trim();
  pageParams.platform = pageParams.platform.trim();
  loginLogs(pageParams)
    .then(response => {
      if (response.code === 0) {
        list.records = response.records;
        list.total = Number(response.total);
        tableLoading.value = false;
      }
    })
    .catch(error => {
      tableLoading.value = false;
      console.log(error);
    });
}
onMounted(() => {
  loadData();
});
</script>
<style lang="scss" scoped>
.title {
  font-size: 16px;
  font-weight: 550;
  padding-bottom: 16px;
  display: flex;
  align-items: center;
}
.mark {
  margin-right: 10px;
  width: 4px;
  height: 15px;
  background: #0064c8;
}
.tb-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.operate {
  color: #0064c8;
  cursor: pointer;
}
</style>
