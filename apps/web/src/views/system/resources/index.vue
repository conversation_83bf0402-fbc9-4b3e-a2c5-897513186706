<template>
  <TitlecCom :title="`菜单管理`" />
  <div class="top-container">
    <div class="search-container">
      <el-input
        style="width: 240px; margin-right: 15px"
        placeholder="请输入名称"
        v-model="pageParams.name"
        clearable
        aria-label="Search input"
      ></el-input>
      <el-button type="primary" @click="performSearch" :icon="Search" aria-label="Search button">
        搜索
      </el-button>
    </div>
    <el-button type="primary" class="topBtn" @click="addDialog"> 新增 </el-button>
  </div>
  <MyTable
    :data="roleData"
    style="width: 100%"
    v-loading="loading"
    row-key="id"
    height="calc(100vh - 210px)"
    default-expand-all
    :header-cell-style="{ background: '#f9f9f9', color: '#333', fontWeight: 'normal' }"
  >
    <el-table-column prop="id" label="ID" width="200">
      <template #default="scope">
        <el-tooltip :content="scope.row.id" placement="top">
          <span class="ellipsis">{{ scope.row.id }}</span>
        </el-tooltip>
      </template>
    </el-table-column>

    <el-table-column prop="title" show-overflow-tooltip label="名称" />
    <el-table-column prop="path" show-overflow-tooltip label="路径" />
    <el-table-column prop="component" show-overflow-tooltip label="组件" />
    <el-table-column prop="status" label="资源状态" width="100">
      <template #default="scope">
        <el-tag :type="scope.row.status ? 'success' : 'info'" disable-transitions>
          {{ scope.row.status ? "启用" : "未启用" }}
        </el-tag>
      </template>
    </el-table-column>
    <el-table-column prop="type" label="类型" width="100">
      <template #default="scope">
        <span v-if="scope.row.type === 'menu'">菜单</span>
        <span v-else-if="scope.row.type === 'button'">按钮</span>
        <span v-else-if="scope.row.type === 'directory'">目录</span>
        <span v-else-if="scope.row.type === 'iframe'">内嵌</span>
        <span v-else-if="scope.row.type === 'link'">外链</span>
      </template>
    </el-table-column>
    <el-table-column prop="description" show-overflow-tooltip label="描述" />
    <el-table-column prop="createdTime" show-overflow-tooltip label="创建时间">
      <template #default="scope">
        <span>{{ formatTime(scope.row.createdTime) }}</span>
      </template>
    </el-table-column>
    <el-table-column label="操作" width="200" fixed="right" align="center" header-align="center">
      <template #default="scope">
        <span class="action-link" @click="editDialog(scope.row)">编辑</span>
        <span> / </span>
        <span class="action-link" @click="handleDelete(scope.row)">删除</span>
        <template v-if="scope.row.parentId != 0">
          <span> / </span>
          <span class="action-link" @click="handleInterface(scope.row)">配置接口权限</span>
        </template>
      </template>
    </el-table-column>
  </MyTable>

  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle ? '新增资源' : '编辑资源'"
    width="800px"
    @closed="handleClose"
    :close-on-click-modal="false"
  >
    <el-form ref="formRef" :model="formData" label-width="120px" label-position="right">
      <el-row :gutter="20">
        <!-- 第一列 -->
        <el-col :span="12">
          <el-form-item label="标题" prop="title">
            <el-input v-model="formData.title" placeholder="请输入标题" clearable />
          </el-form-item>

          <el-form-item label="路由名称" prop="path">
            <el-input v-model="formData.name" placeholder="请输入路由名称" clearable />
          </el-form-item>
          <el-form-item label="组件" prop="path">
            <el-input v-model="formData.component" placeholder="请输入组件" clearable />
          </el-form-item>
          <el-form-item label="菜单类型" prop="type">
            <el-select v-model="formData.menuType" placeholder="请选择菜单类型" clearable>
              <el-option label="一级菜单" value="0" />
              <el-option label="二级菜单" value="10" />
            </el-select>
          </el-form-item>
          <el-form-item label="父级菜单" prop="parentMenuType" v-if="formData.menuType === '10'">
            <el-select v-model="formData.parentMenuType" placeholder="请选择父级菜单" clearable>
              <el-option
                v-for="item in menuTypeData"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="排序" prop="sequence">
            <el-input
              type="number"
              v-model="formData.sequence"
              placeholder="请输入排序"
              clearable
            />
          </el-form-item>
          <el-form-item label="类型" prop="visible">
            <el-select v-model="formData.type" placeholder="请选择类型" clearable>
              <el-option label="目录" value="directory" />
              <el-option label="菜单" value="menu" />
              <el-option label="内嵌" value="iframe" />
              <el-option label="外链" value="link" />
            </el-select>
          </el-form-item>
        </el-col>

        <!-- 第二列 -->
        <el-col :span="12">
          <el-form-item label="路径" prop="path">
            <el-input v-model="formData.path" placeholder="请输入路径" clearable />
          </el-form-item>
          <el-form-item label="菜单图标" prop="code">
            <el-input v-model="formData.icon" placeholder="请输入菜单图标路径" clearable />
          </el-form-item>

          <el-form-item label="资源编码" prop="permission">
            <el-input v-model="formData.permission" placeholder="请输入资源编码" clearable />
          </el-form-item>
          <!-- <el-form-item label="公共菜单" prop="global">
            <el-radio-group v-model="formData.global">
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
          </el-form-item> -->
          <el-form-item label="菜单状态" prop="status">
            <el-radio-group v-model="formData.status">
              <el-radio :label="true">启用</el-radio>
              <el-radio :label="false">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="页面缓存" prop="keepAlive">
            <el-radio-group v-model="formData.keepAlive">
              <el-radio :label="true">开启</el-radio>
              <el-radio :label="false">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="是否可见" prop="visible">
            <el-radio-group v-model="formData.visible">
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入菜单简介"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="handleSubmit(dialogTitle)" :loading="submitting">
        提交
      </el-button>
    </template>
  </el-dialog>
  <div>
    <el-drawer
      v-model="drawerVisible"
      title="资源列表"
      direction="rtl"
      size="50%"
      :with-header="true"
      :close-on-click-modal="false"
    >
      <div class="btn-container">
        <el-button type="primary" class="topBtn" @click="addDialog1"> 新增 </el-button>
      </div>

      <MyTable
        :data="list.records"
        :total="list.total"
        style="width: 100%"
        v-loading="tableLoading"
        @sizeChange="handleSizeChange"
        @currentChange="handleCurrentChange"
      >
        <my-column property="title" label="标题" />
        <my-column property="permission" label="编码" />
        <my-column property="description" label="描述"> </my-column>
        <my-column label="操作" align="center" header-align="center" fixed="right" width="200">
          <template #default="scope">
            <span class="action-link" @click="addDialog2(scope.row)">编辑 </span>
            <span> / </span>
            <span class="action-link" @click="handleDelete(scope.row)">删除</span>
          </template>
        </my-column>
      </MyTable>
    </el-drawer>
  </div>
  <el-dialog
    v-model="drawerDialog"
    :title="dialogTitle ? `新增资源` : `编辑资源`"
    class="text-left"
    width="500"
    :show-close="false"
    :close-on-click-modal="false"
    :align-center="true"
    @closed="handleClose"
  >
    <el-form :model="formData" ref="formRef">
      <el-form-item label="资源名称" prop="title">
        <el-input v-model="formData.title" placeholder="请输入资源名称" clearable />
      </el-form-item>
      <el-form-item label="资源编码" prop="permission">
        <el-input v-model="formData.permission" placeholder="请输入资源编码" clearable />
      </el-form-item>
      <el-form-item label="描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入菜单简介"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="drawerDialog = false">取消</el-button>
      <el-button type="primary" @click="drawerSubmit(dialogTitle)" :loading="submitting"
        >提交</el-button
      >
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import TitlecCom from "@/components/TitleCom/index.vue";
import MyTable from "@/components/table/my-table.vue";
import MyColumn from "@/components/table/my-column.vue";

import { ref, reactive, onMounted } from "vue";
import {
  getResourceList,
  updateResource,
  getResourceMenu,
  createResource,
  deleteResource
} from "@/api/system/resources";
import { ElMessage, type FormInstance, type FormRules } from "element-plus";
import { Search } from "@element-plus/icons-vue";
import { formatTime } from "@/utils/dateStr";
// 定义用户数据类型
interface FormData {
  title: string;
  component: string;
  menuType: string;
  path: string;
  icon: string;
  permission: string;
  parentMenuType: string;
  status: boolean;
  sequence: number;
  keepAlive: boolean;
  visible: boolean;
  type: string;
  description: string;
  // global: boolean;
  name: string;
}

const formData = reactive<FormData>({
  title: "",
  component: "",
  menuType: "0",
  path: "",
  icon: "",
  permission: "",
  parentMenuType: "0",
  status: true,
  sequence: 0,
  keepAlive: true,
  visible: true,
  type: "menu",
  description: "",
  // global: true,
  name: ""
});
const list = reactive({
  records: [],
  total: 0
});
const drawerDialog = ref(false);
const tableLoading = ref(false);
const dialogVisible = ref(false);
const dialogTitle = ref(true); // true: 新增, false: 编辑
const formRef = ref<FormInstance>();
const pageParams = reactive({
  page: 1,
  rows: 10,
  name: "",
  code: ""
  // type: 0,
  // status: 0,
  // industry: 0,
});
const pageParams1 = reactive({
  page: 1,
  rows: 10,
  parentId: "",
  type: "button",
  id: ""
  // industry: 0,
});

// 搜索用户
const performSearch = () => {
  pageParams.page = 1;
  fetchmenuList();
};

const roleData = ref([]);
const loading = ref(false); // 表格加载状态
const submitting = ref(false); // 提交状态

// 弹窗关闭
const handleClose = () => {
  formRef.value?.resetFields();
  // selectedRegion.value = [];
  Object.assign(formData, {
    title: "",
    component: "",
    menuType: "0",
    path: "",
    icon: "",
    permission: "",
    parentMenuType: "0",
    status: true,
    // global: true,
    keepAlive: true,
    visible: true,
    type: "menu",
    description: "",
    name: ""
  });
};

// 新增租户
const handleSubmit = async (val: boolean) => {
  if (val) {
    // 新增租户
    try {
      submitting.value = true;
      await formRef.value?.validate();
      const params = {
        ...formData,
        parentId: formData.menuType === "0" ? "0" : formData.parentMenuType,
        extJson: JSON.stringify(formData.name)
      };
      const res = await createResource(params);
      if (res.code === 0) {
        ElMessage.success("新增成功");
        dialogVisible.value = false;
        fetchmenuList();
      }
    } catch (error) {
      console.error("表单验证失败:", error);
    } finally {
      submitting.value = false;
    }
  } else {
    // 编辑租户
    try {
      const params = {
        ...formData,
        parentId: formData.menuType === "0" ? "0" : formData.parentMenuType,
        extJson: JSON.stringify(formData.name)
      };
      submitting.value = true;
      await formRef.value?.validate();
      const res = await updateResource(params);
      if (res.code === 0) {
        ElMessage.success("编辑成功");
        dialogVisible.value = false;
        fetchmenuList();
      }
    } catch (error) {
      console.error("表单验证失败:", error);
    } finally {
      submitting.value = false;
    }
  }
};
const menuTypeData = ref([]);
// 获取资源列表
const fetchmenuList = async () => {
  loading.value = true;

  const params = {
    page: pageParams.page,
    rows: pageParams.rows
  };

  try {
    const res = await getResourceMenu(params);
    if (res.code === 0) {
      roleData.value = res.records || [];
      menuTypeData.value = res.records.map((item: any) => {
        return {
          value: item.id,
          label: item.title
        };
      });
    }
  } catch (error) {
    console.error("API 请求异常:", error);
  } finally {
    loading.value = false;
  }
};

// 新增弹窗
const addDialog = () => {
  dialogTitle.value = true;
  dialogVisible.value = true;
};

// 编辑弹窗
const editDialog = (row: any) => {
  dialogTitle.value = false;
  dialogVisible.value = true;
  Object.assign(formData, row);
  formData.menuType = row.parentId === "0" ? "0" : "10";
  formData.parentMenuType = row.parentId;
  formData.name = JSON.parse(row.extJson);
};

// 删除资源
const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm(`确定要删除「${row.title}」吗？`, "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    });
    const res = await deleteResource(row);
    if (res.code === 0) {
      ElMessage.success("删除成功");
      fetchmenuList();
      fetchResourceList();
    } else {
    }
  } catch {
    ElMessage.info("已取消删除");
  }
};

const drawerVisible = ref(false);
// 配置接口权限
const handleInterface = (row: any) => {
  pageParams1.parentId = row.id;
  fetchResourceList();
  drawerVisible.value = true;
};
//修改每页条数
const handleSizeChange = (val: number) => {
  pageParams.rows = val;
  fetchResourceList();
};
//分页
const handleCurrentChange = (val: number) => {
  pageParams.page = val;
  fetchResourceList();
};

// 获取资源列表
const fetchResourceList = async () => {
  tableLoading.value = true;

  try {
    const res = await getResourceList(pageParams1);
    if (res.code === 0) {
      // roleData.value = res.records || [];
      list.records = res.records;
      list.total = Number(res.total);
    }
  } catch (error) {
    console.error("API 请求异常:", error);
  } finally {
    tableLoading.value = false;
  }
};

const addDialog1 = () => {
  dialogTitle.value = true;
  drawerDialog.value = true;
};
const addDialog2 = (val: any) => {
  dialogTitle.value = false;
  pageParams1.parentId = val.parentId;
  formData.description = val.description;
  formData.permission = val.permission;
  formData.title = val.title;
  pageParams1.type = val.type;
  pageParams1.id = val.id;
  drawerDialog.value = true;
};
const drawerSubmit = async (val: boolean) => {
  if (val) {
    try {
      submitting.value = true;
      await formRef.value?.validate();
      const params = {
        parentId: pageParams1.parentId,
        type: pageParams1.type,
        title: formData.title,
        description: formData.description,
        permission: formData.permission
      };
      const res = await createResource(params);
      if (res.code === 0) {
        ElMessage.success("新增成功");
        fetchResourceList();
        drawerDialog.value = false;
      }
    } catch (error) {
      console.error("新增失败:", error);
    } finally {
      submitting.value = false;
    }
  } else {
    try {
      submitting.value = true;
      await formRef.value?.validate();
      const params = {
        parentId: pageParams1.parentId,
        type: pageParams1.type,
        title: formData.title,
        description: formData.description,
        permission: formData.permission,
        id: pageParams1.id
      };
      const res = await updateResource(params);
      if (res.code === 0) {
        ElMessage.success("编辑成功");
        fetchResourceList();
        drawerDialog.value = false;
      }
    } catch (error) {
      console.error("表单验证失败:", error);
    } finally {
      submitting.value = false;
    }
  }
};
onMounted(() => {
  fetchmenuList();
});
</script>

<style scoped>
.top-container {
  display: flex;
  justify-content: space-between;
}
.search-container {
  display: flex;
  margin-bottom: 15px;
}
.topBtn {
  margin-left: 10px;
}

.action-link {
  color: #0064c8;
  cursor: pointer;
}

.ellipsis {
  display: inline-block;
  max-width: 70%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  height: 18px;
}
:deep(.el-drawer__header) {
  margin: 5px !important;
}
.btn-container {
  display: flex;
  justify-content: end;
  margin-bottom: 10px;
}
</style>
