<template>
  <div class="title">
    <div class="mark"></div>
    <div>syslog设置</div>
  </div>
  <div class="tb-header">
    <div>
      <el-input
        style="width: 240px"
        placeholder="请输入主机名称"
        clearable
        filterable
        v-model="pageParams.hostName"
      >
      </el-input>
      <el-button type="primary" style="margin-left: 15px" :icon="Search" @click="search"
        >搜索</el-button
      >
    </div>
    <div>
      <el-button type="primary" @click="handleAdd">新增</el-button>
    </div>
  </div>
  <MyTable
    :data="list.records"
    :total="list.total"
    style="width: 100%"
    v-loading="tableLoading"
    @sizeChange="handleSizeChange"
    @currentChange="handleCurrentChange"
  >
    <my-column property="hostIp" label="主机IP" />
    <my-column property="appName" label="应用名称" />
    <my-column property="hostName" label="主机名称" />
    <my-column property="typeName" label="日志类型" />
    <my-column property="operatorName" label="操作用户名称" />
    <my-column property="createdAt" label="创建时间" />
    <my-column property="updatedAt" label="修改时间" />
    <my-column label="操作" align="center" header-align="center" fixed="right" width="100">
      <template #default="scope">
        <span class="operate" @click="handleEdit(scope.row)">编辑 </span>
        <span class="divider"> / </span>
        <span class="operate" @click="handleDelete(scope.row)">删除 </span>
      </template>
    </my-column>
  </MyTable>
  <el-dialog
    :align-center="true"
    v-model="dialogFormVisible"
    :title="title"
    width="500"
    :close-on-click-modal="false"
  >
    <el-form :model="state.form" label-width="auto">
      <el-form-item label="应用名称：">
        <el-select
          v-model="state.form.appId"
          placeholder="请选择应用名称"
          clearable
          filterable
          style="width: 100%"
        >
          <el-option
            v-for="app in appOptions"
            :key="app.appid"
            :label="app.name"
            :value="app.appid"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="主机IP：">
        <el-select
          filterable
          allow-create
          default-first-option
          v-model="state.form.hostIp"
          placeholder="请选择主机IP"
          clearable
          style="width: 100%"
          @change="handleHostIpChange"
        >
          <el-option
            v-for="(hostNames, hostIp) in hostMapping"
            :key="hostIp"
            :label="hostIp"
            :value="hostIp"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="主机名称：">
        <el-select
          filterable
          allow-create
          default-first-option
          v-model="state.form.hostName"
          placeholder="请选择主机名称"
          clearable
          style="width: 100%"
          :disabled="!state.form.hostIp"
        >
          <el-option
            v-for="hostName in hostMapping[state.form.hostIp] || []"
            :key="hostName"
            :label="hostName"
            :value="hostName"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="日志类型：">
        <el-select
          v-model="state.form.logType"
          placeholder="请选择日志类型"
          clearable
          style="width: 100%"
        >
          <el-option
            v-for="item in logTypeOptions"
            :key="item.type"
            :label="item.name"
            :value="item.type"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm">确定</el-button>
      </div>
    </template>
  </el-dialog>
  <el-dialog
    :align-center="true"
    v-model="deleteVisible"
    title="温馨提示"
    width="500"
    :close-on-click-modal="false"
  >
    <span>确定要删除吗？</span>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="deleteVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmDelete">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import { Search } from "@element-plus/icons-vue";
import {
  syslogList,
  addSyslog,
  deleteSyslog,
  editSyslog,
  getSyslogDetail,
  getLogTypeList,
  getDefaultMapping,
  getAllApp
} from "@/api/log/syslog";
import { ElMessage } from "element-plus";

interface LogType {
  id: string;
  type: string;
  name: string;
  desc: string;
  created_at: string;
}

const tableLoading = ref(false); //列表loading
const dialogFormVisible = ref(false); //新增编辑弹窗是否显示
const deleteVisible = ref(false); //删除弹窗是否显示
const currentId = ref(""); // 删除ID

const state = reactive({
  form: {
    id: "",
    hostIp: "",
    hostName: "",
    logType: "",
    appId: ""
  }
});
const title = ref(""); //弹窗标题
//列表参数
const pageParams = reactive({
  hostName: "",
  page: 1,
  rows: 10
});
const list = reactive({
  records: [],
  total: 0
});
const logTypeOptions = ref<LogType[]>([]); //日志类型
const hostMapping = ref<Record<string, string[]>>({}); //主机IP和主机名称
const appOptions = ref<{ appid: string; name: string }[]>([]); //应用名称

// 获取日志类型列表
const getLogTypes = async () => {
  try {
    const res = await getLogTypeList();
    if (res.code === 0) {
      logTypeOptions.value = res.records;
    }
  } catch (error) {
    console.error(error);
  }
};

//修改每页条数
const handleSizeChange = (val: number) => {
  pageParams.rows = val;
  loadData();
};
//分页
const handleCurrentChange = (val: number) => {
  pageParams.page = val;
  loadData();
};
//搜索
function search() {
  pageParams.page = 1;
  loadData();
}
//获取列表数据
function loadData() {
  tableLoading.value = true;
  pageParams.hostName = pageParams.hostName.trim();
  syslogList(pageParams)
    .then(response => {
      if (response.code === 0) {
        list.records = response.records;
        list.total = Number(response.total);
        tableLoading.value = false;
      }
    })
    .catch(error => {
      console.log(error);
      tableLoading.value = false;
    });
}

// 打开新增弹窗
const handleAdd = () => {
  title.value = "新增syslog设置";
  state.form = {
    id: "",
    hostIp: "",
    hostName: "",
    logType: "",
    appId: ""
  };
  dialogFormVisible.value = true;
};

// 打开编辑弹窗
const handleEdit = async (row: any) => {
  title.value = "编辑syslog设置";
  try {
    const res = await getSyslogDetail(row.id);
    if (res.code === 0) {
      state.form = res.entity;
      dialogFormVisible.value = true;
    }
  } catch (error) {
    console.error(error);
  }
};

// 提交表单
const submitForm = async () => {
  try {
    const { id, ...formData } = state.form;
    const res = await (id ? editSyslog({ id, ...formData }) : addSyslog(formData));
    if (res.code === 0) {
      ElMessage.success(id ? "编辑成功" : "新增成功");
      dialogFormVisible.value = false;
      loadData();
    }
  } catch (error) {
    console.error(error);
  }
};

// 删除
const handleDelete = async (row: any) => {
  currentId.value = row.id;
  deleteVisible.value = true;
};

// 确认删除
const confirmDelete = async () => {
  try {
    const res = await deleteSyslog(currentId.value);
    if (res.code === 0) {
      ElMessage.success("删除成功");
      deleteVisible.value = false;
      loadData();
    }
  } catch (error) {
    console.error(error);
  }
};
//切换主机IP
const handleHostIpChange = () => {
  state.form.hostName = "";
};
//获取主机IP和主机名称
const defaultMapping = async () => {
  try {
    const res = await getDefaultMapping();
    if (res.code === 0) {
      hostMapping.value = res.entity;
    }
  } catch (error) {
    console.log(error);
  }
};
//获取应用名称
const getApp = async () => {
  try {
    const response = await getAllApp();
    if (response.code === 0) {
      appOptions.value = response.entity;
    }
  } catch (error) {
    console.log(error);
  }
};
onMounted(() => {
  loadData();
  getLogTypes();
  defaultMapping();
  getApp();
});
</script>
<style lang="scss" scoped>
.title {
  font-size: 16px;
  font-weight: 550;
  padding-bottom: 16px;
  display: flex;
  align-items: center;
}
.mark {
  margin-right: 10px;
  width: 4px;
  height: 15px;
  background: #0064c8;
}
.tb-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.operate {
  color: #0064c8;
  cursor: pointer;
}
</style>
