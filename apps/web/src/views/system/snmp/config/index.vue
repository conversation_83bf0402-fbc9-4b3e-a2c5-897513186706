<template>
  <div class="title">
    <div class="mark"></div>
    <div>snmp设置</div>
  </div>
  <div class="tb-header">
    <div>
      <el-input
        style="width: 240px"
        placeholder="请输入主机名称"
        clearable
        filterable
        v-model="pageParams.hostName"
      ></el-input>
      <el-button type="primary" class="ml-15px" :icon="Search" @click="search">搜索</el-button>
    </div>
    <div>
      <el-button type="primary" @click="openAddDialog">新增</el-button>
    </div>
  </div>
  <MyTable
    :data="list.records"
    :total="list.total"
    style="width: 100%"
    v-loading="tableLoading"
    @sizeChange="handleSizeChange"
    @currentChange="handleCurrentChange"
  >
    <my-column property="appName" label="应用名称" />
    <my-column property="hostIp" label="主机IP" />
    <my-column property="hostName" label="主机名称" />
    <my-column property="hostPort" label="主机端口" />
    <my-column property="version" label="snmp版本" />
    <my-column property="oids" label="oid列表" :show-overflow-tooltip="false">
      <template #default="{ row }">
        <div class="ip-cell">
          <el-tooltip placement="top" popper-class="ip-tooltip">
            <template #content>
              <div v-html="formatOids(row.oids)"></div>
            </template>
            <span class="ip-text">{{ row.oids }}</span>
          </el-tooltip>
        </div>
      </template>
    </my-column>
    <!-- <my-column property="collectId" label="CollecterId" /> -->
    <!-- <my-column property="operator" label="用户名" /> -->
    <my-column property="createdAt" label="创建时间" />
    <my-column property="updatedAt" label="修改时间" />
    <my-column label="操作" align="center" header-align="center" fixed="right" width="150">
      <template #default="scope">
        <span class="operate" @click="handleEdit(scope.row)">编辑 </span>
        <span class="divider"> / </span>
        <span class="operate" @click="promptDelete(scope.row.id)">删除 </span>
        <span class="divider"> / </span>
        <span class="operate" @click="query(scope.row)">查询 </span>
      </template>
    </my-column>
  </MyTable>
  <el-dialog
    :align-center="true"
    v-model="dialogFormVisible"
    :title="title"
    width="500"
    :close-on-click-modal="false"
  >
    <el-form :model="state.form" label-width="auto">
      <el-form-item label="应用名称：">
        <el-select v-model="state.form.appId" placeholder="请选择应用名称" clearable filterable>
          <el-option
            v-for="app in appOptions"
            :key="app.appid"
            :label="app.name"
            :value="app.appid"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="主机IP：">
        <el-input v-model="state.form.hostIp" placeholder="请输入主机IP" clearable></el-input>
      </el-form-item>
      <el-form-item label="主机名称：">
        <el-input v-model="state.form.hostName" placeholder="请输入主机名称" clearable></el-input>
      </el-form-item>
      <el-form-item label="主机端口：">
        <el-input v-model="state.form.hostPort" placeholder="请输入主机端口" clearable></el-input>
      </el-form-item>
      <el-form-item label="版本：">
        <el-select v-model="state.form.version" placeholder="请选择版本" clearable filterable>
          <el-option label="v1" value="v1"></el-option>
          <el-option label="v2c" value="v2c"></el-option>
          <el-option label="v3" value="v3"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="共同体：">
        <el-input v-model="state.form.community" placeholder="请输入共同体" clearable></el-input>
      </el-form-item>
      <el-form-item label="用户名：" v-if="state.form.version === 'v3'">
        <el-input v-model="state.form.userName" placeholder="请输入用户名" clearable></el-input>
      </el-form-item>
      <el-form-item label="auth加密方式：" v-if="state.form.version === 'v3'">
        <el-select
          v-model="state.form.authProto"
          placeholder="请选择auth加密方式："
          clearable
          filterable
        >
          <el-option label="md5" value="md5"></el-option>
          <el-option label="sha" value="sha"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="auth密码：" v-if="state.form.version === 'v3'">
        <el-input v-model="state.form.authPass" placeholder="请输入auth密码" clearable></el-input>
      </el-form-item>
      <el-form-item label="priv加密方式：" v-if="state.form.version === 'v3'">
        <el-select
          v-model="state.form.privProto"
          placeholder="请选择priv加密方式："
          clearable
          filterable
        >
          <el-option label="des" value="des"></el-option>
          <el-option label="3des" value="3des"></el-option>
          <el-option label="aes" value="aes"></el-option>
          <el-option label="aes128" value="aes128"></el-option>
          <el-option label="aes192" value="aes192"></el-option>
          <el-option label="aes256" value="aes256"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="priv密码：" v-if="state.form.version === 'v3'">
        <el-input v-model="state.form.privPass" placeholder="请输入priv密码" clearable></el-input>
      </el-form-item>
      <el-form-item label="oid列表：">
        <el-select
          v-model="state.form.oids"
          placeholder="请选择oid列表"
          clearable
          filterable
          multiple
        >
          <el-option v-for="oid in oidOptions" :key="oid.oid" :label="oid.name" :value="oid.oid" />
        </el-select>
      </el-form-item>
      <el-form-item label="collectId：">
        <el-select
          v-model="state.form.collectId"
          placeholder="请选择collectId"
          clearable
          filterable
        >
          <el-option
            v-for="collect in collectOptions"
            :key="collect.id"
            :label="collect.hostName"
            :value="collect.id"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm">确定</el-button>
      </div>
    </template>
  </el-dialog>
  <el-dialog
    :align-center="true"
    v-model="deleteVisible"
    title="温馨提示"
    width="500"
    :close-on-click-modal="false"
  >
    <span>确定要删除吗？</span>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="deleteVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmDelete">确定</el-button>
      </div>
    </template>
  </el-dialog>
  <div>
    <el-drawer v-model="queryVisible" :title="queryTitle" size="60%">
      <MyTable
        :data="lists.records"
        :total="lists.total"
        style="width: 100%"
        v-loading="tableLoading"
        @sizeChange="handlesizeChange"
        @currentChange="handlecurrentChange"
      >
        <my-column property="oid" label="命令" />
        <my-column property="name" label="名称" />
        <my-column property="desc" label="描述" />
        <my-column property="value" label="结果" />
      </MyTable>
    </el-drawer>
  </div>
</template>
<script setup lang="ts">
import {
  snmpList,
  oidList,
  collectList,
  getAllApp,
  addSnmp,
  editSnmp,
  deleteSnmp,
  detailSnmp,
  snmpHostList
} from "@/api/log/snmp";
import { Search } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
const tableLoading = ref(false);
const dialogFormVisible = ref(false); //新增编辑弹窗是否显示
const deleteVisible = ref(false); //删除弹窗是否显示
const queryVisible = ref(false);
const appOptions = ref<{ appid: string; name: string }[]>([]); //应用名称
const title = ref("");
const queryTitle = ref("");
const oidOptions = ref<{ oid: string; name: string }[]>([]); // oid列表选项
const collectOptions = ref<{ id: string; hostName: string }[]>([]); // collectId选项
const formatOids = (ipString: string) => {
  return ipString.split(",").join("<br>");
};
//修改每页条数
const handleSizeChange = (val: number) => {
  pageParams.rows = val;
  loadData();
};
//分页
const handleCurrentChange = (val: number) => {
  pageParams.page = val;
  loadData();
};
const pageParams = reactive({
  hostName: "",
  page: 1,
  rows: 10
});
const list = reactive({
  records: [],
  total: 0
});
//搜索
function search() {
  pageParams.page = 1;
  loadData();
}
function loadData() {
  tableLoading.value = true;
  pageParams.hostName = pageParams.hostName.trim();
  snmpList(pageParams)
    .then(res => {
      if (res.code === 0) {
        list.records = res.records;
        list.total = Number(res.total);
        tableLoading.value = false;
      }
    })
    .catch(error => {
      console.log(error);
      tableLoading.value = false;
    });
}
//获取应用名称
const getApp = async () => {
  try {
    const response = await getAllApp();
    if (response.code === 0) {
      appOptions.value = response.entity;
    }
  } catch (error) {
    console.log(error);
  }
};
//获取oid列表
const getOid = async () => {
  try {
    const response = await oidList();
    if (response.code === 0) {
      oidOptions.value = response.entity;
    }
  } catch (error) {
    console.log(error);
  }
};
//查询Collect所在主机信息
const getCollect = async () => {
  try {
    const response = await collectList();
    if (response.code === 0) {
      collectOptions.value = response.entity;
    }
  } catch (error) {
    console.log(error);
  }
};
const state = reactive({
  form: {
    id: "",
    appId: "",
    hostIp: "",
    hostName: "",
    hostPort: "",
    version: "v1",
    community: "public",
    userName: "",
    authProto: "",
    authPass: "",
    privProto: "",
    privPass: "",
    oids: "" as string | string[],
    collectId: ""
  }
});
const handleEdit = async (row: any) => {
  title.value = "编辑snmp设置";
  try {
    const res = await detailSnmp(row.id);
    if (res.code === 0) {
      state.form = res.entity;
      if (typeof state.form.oids === "string") {
        state.form.oids = state.form.oids.split(",");
      }
      dialogFormVisible.value = true;
    }
  } catch (error) {
    console.error(error);
  }
};

const submitForm = async () => {
  try {
    const { id, ...formData } = state.form;
    if (Array.isArray(formData.oids)) {
      formData.oids = formData.oids.join(",");
    }
    const res = await (id ? editSnmp({ id, ...formData }) : addSnmp(formData));
    if (res.code === 0) {
      ElMessage.success(id ? "编辑成功" : "新增成功");
      dialogFormVisible.value = false;
      loadData();
    }
  } catch (error) {
    console.error(error);
  }
};

const handleDelete = async (id: string) => {
  try {
    const response = await deleteSnmp(id);
    if (response.code === 0) {
      ElMessage({ message: "删除成功", type: "success" });
      loadData();
    }
  } catch (error) {
    console.log(error);
  }
};

const openAddDialog = () => {
  Object.assign(state.form, {
    appId: "",
    hostIp: "",
    hostName: "",
    hostPort: "",
    version: "v1",
    community: "public",
    userName: "",
    authProto: "",
    authPass: "",
    privProto: "",
    privPass: "",
    oids: "",
    collectId: ""
  });
  title.value = "新增snmp设置";
  dialogFormVisible.value = true;
};

const confirmDelete = () => {
  handleDelete(currentDeleteId);
  deleteVisible.value = false;
};

let currentDeleteId = "";

const promptDelete = (id: string) => {
  currentDeleteId = id;
  deleteVisible.value = true;
};
//详情参数
const dataToSend = reactive({
  hostIp: "",
  hostName: "",
  hostPort: "",
  appId: "",
  version: "",
  community: "",
  oid: "",
  collectId: "",
  page: 1,
  rows: 10
});
const query = (row: any) => {
  queryVisible.value = true;
  queryTitle.value = "查询结果 【" + row.hostName + "】";
  dataToSend.appId = row.appId;
  dataToSend.hostIp = row.hostIp;
  dataToSend.hostName = row.hostName;
  dataToSend.hostPort = row.hostPort;
  dataToSend.version = row.version;
  dataToSend.community = row.community;
  dataToSend.oid = row.oids;
  dataToSend.collectId = row.collectId;
  detailData();
};
const lists = reactive({
  records: [],
  total: 0
});
//修改每页条数
const handlesizeChange = (val: number) => {
  dataToSend.rows = val;
  detailData();
};
//分页
const handlecurrentChange = (val: number) => {
  dataToSend.page = val;
  detailData();
};
function detailData() {
  snmpHostList(dataToSend)
    .then(response => {
      if (response.code === 0) {
        lists.records = response.records;
        lists.total = Number(response.total);
      }
    })
    .catch(error => {
      console.log(error);
    });
}
onMounted(() => {
  loadData();
  getApp();
  getOid();
  getCollect();
});
</script>
<style lang="scss" scoped>
.ip-cell {
  position: relative;
  width: 100%;
  overflow: hidden;
}

.ip-text {
  display: inline-block;
  width: 100%;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.ip-tooltip.el-tooltip__popper {
  max-width: 300px;
  white-space: normal;
  word-break: break-all;
  line-height: 1.5;
}
.title {
  font-size: 16px;
  font-weight: 550;
  padding-bottom: 16px;
  display: flex;
  align-items: center;
}
.mark {
  margin-right: 10px;
  width: 4px;
  height: 15px;
  background: #0064c8;
}
.tb-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.operate {
  color: #0064c8;
  cursor: pointer;
}
:deep(.el-drawer__header) {
  margin: 5px !important;
}
</style>
