import { ref } from "vue";
import android from "@/image/Android.png";
import h5 from "@/image/h5.png";
import java from "@/image/java.png";
import MiniProgram from "@/image/MiniProgram.png";
import php from "@/image/php.png";
import Python from "@/image/Python.png";
import IOS from "@/image/IOS.png";

export const cards = ref([
  {
    title: "APM_SDK_Connection_Guide--Android",
    desc: "通过 Apm-Agent 对 Android 应用进行埋点，实现链路数据采集与上报，支持页面性能、接口性能、错误追踪、用户分析等全链路可视化监控。",
    img: android
  },
  {
    title: "APM_SDK_Connection_Guide--H5",
    desc: "为 H5 应用集成 Apm-Agent 后，即可实时上报链路数据，提供页面加载、接口请求、错误分析等关键指标的智能可视化监控能力。",
    img: h5
  },
  {
    title: "APM_SDK_Connection_Guide--Java",
    desc: "Java 应用通过 Apm-Agent 自动或手动埋点，可实现拓扑分析、调用链追踪、异常事务监控与 SQL 性能分析等核心功能，助力后端诊断。",
    img: java
  },
  {
    title: "APM_SDK_Connection_Guide--MiniProgram",
    desc: "通过 Apm-Agent 集成小程序监控能力，支持页面加载、请求性能、异常上报与用户行为追踪，帮助开发者全面掌握小程序运行状况。",
    img: MiniProgram
  },
  {
    title: "APM_SDK_Connection_Guide--PHP",
    desc: "PHP 应用接入 Apm-Agent 后，可进行性能数据采集与链路上报，快速定位接口瓶颈、异常错误和用户行为轨迹，实现高效运维。",
    img: php
  },
  {
    title: "APM_SDK_Connection_Guide--Python",
    desc: "通过 Apm-Agent 为 Python 应用接入可观测链路系统，支持接口性能分析、异常追踪与调用链监控，提升系统稳定性与可维护性。",
    img: Python
  },
  {
    title: "APM_SDK_Connection_Guide--IOS",
    desc: "通过 Apm-Agent 接入 iOS 应用，可实现页面加载、接口请求、异常崩溃等关键链路数据的采集与分析，全面提升移动端性能监控与用户体验洞察能力。",
    img: IOS
  }
]);
