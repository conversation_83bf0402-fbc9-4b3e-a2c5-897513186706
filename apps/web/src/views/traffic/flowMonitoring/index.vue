<template>
  <div class="tb-header">
    <div>
      <el-input
        style="width: 240px"
        placeholder="***********"
        v-model="pageParams.outsideIp"
        clearable
      ></el-input>
      <el-button type="primary" style="margin-left: 15px" :icon="Search" @click="search"
        >搜索</el-button
      >
    </div>
  </div>
  <MyTable
    :data="list.records"
    :total="list.total"
    style="width: 100%"
    v-loading="tableLoading"
    @sizeChange="handleSizeChange"
    @currentChange="handleCurrentChange"
    :default-sort="{
      prop: 'totalBytes && retRate && rstRate && errorCount',
      order: 'descending'
    }"
    @sort-change="handleSortChange"
  >
    <my-column property="outsideIp" label="IP地址" />
    <my-column property="region" label="地理位置">
      <template v-slot="{ row }">
        {{ row.region ? row.region : "-" }}
      </template>
    </my-column>
    <my-column property="totalBytes" label="总流量" sortable="custom">
      <template v-slot="{ row }">
        <span>
          {{ convertBytes(row.totalBytes).fixValue }}{{ convertBytes(row.totalBytes).unit || "" }}
        </span>
      </template>
    </my-column>
    <my-column property="totalPack" label="总数据包" sortable="custom">
      <template v-slot="{ row }">
        <span>
          {{ row.totalPack }}
        </span>
      </template>
    </my-column>
    <my-column property="retRate" label="重传包占比" sortable="custom">
      <template v-slot="{ row }">
        <span :style="{ color: row.retRate > 0 ? '#f56c6c' : '' }"> {{ row.retRate }}% </span>
      </template>
    </my-column>
    <my-column property="rstRate" label="重置包占比" sortable="custom">
      <template v-slot="{ row }">
        <span :style="{ color: row.rstRate > 0 ? '#f56c6c' : '' }"> {{ row.rstRate }}% </span>
      </template>
    </my-column>
    <my-column property="errorCount" label="连接失败数（次）" sortable="custom">
      <template v-slot="{ row }">
        <span :style="{ color: row.errorCount > 0 ? '#f56c6c' : '' }">{{ row.errorCount }}</span>
      </template>
    </my-column>
  </MyTable>
</template>

<script setup lang="ts">
import { getStatOutsideIp } from "@/api/traffic/ip";
import { Search } from "@element-plus/icons-vue";
import MyTable from "@/components/table/my-table.vue";
import MyColumn from "@/components/table/my-column.vue";
import { applicationStore } from "@/store/modules/application";
import { convertBytes } from "@/utils/trafficStr";
import { serviceNameStore } from "@/store/modules/service";
const useServiceNameStore = serviceNameStore();
const useApplicationStore = applicationStore();

const tableLoading = ref(true);
//列表参数
const pageParams = reactive({
  appid: "",
  ip: "",
  page: 1,
  rows: 10,
  sort: "",
  order: "",
  outsideIp: ""
});
const handleSortChange = (val: any) => {
  const order = val.order;
  const sort = val.prop;
  if (order === "ascending") {
    pageParams.order = "0";
  } else {
    pageParams.order = "1";
  }
  pageParams.sort = sort;
  loadData();
};
//修改每页条数
const handleSizeChange = (val: number) => {
  pageParams.rows = val;
  loadData();
};
//分页
const handleCurrentChange = (val: number) => {
  pageParams.page = val;
  loadData();
};
//搜索
function search() {
  pageParams.page = 1;
  loadData();
}
const list = reactive({
  records: [],
  total: 0
});
//列表数据
function loadData() {
  tableLoading.value = true;
  pageParams.ip = useServiceNameStore.serviceName;
  pageParams.outsideIp = pageParams.outsideIp.trim();
  pageParams.appid = useApplicationStore.appId;
  getStatOutsideIp(pageParams).then(response => {
    if (response.code === 0) {
      list.records = response.records;
      list.total = Number(response.total);
      tableLoading.value = false;
    } else {
      tableLoading.value = false;
    }
  });
}

onMounted(() => {
  loadData();
});
</script>

<style lang="scss" scoped>
.indicator-wrapper > *:not(:last-child) {
  margin-right: 8px;
  box-sizing: border-box;
}
.tb-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.operate {
  color: #0064c8;
  cursor: pointer;
}
:deep(.el-drawer__header) {
  margin: 5px !important;
}
</style>
