<template>
  <div class="tb-header">
    <div>
      <el-input
        style="width: 240px"
        placeholder="***********"
        v-model="pageParams.ip"
        clearable
      ></el-input>
      <el-button type="primary" style="margin-left: 15px" :icon="Search" @click="search"
        >搜索</el-button
      >
    </div>
  </div>
  <MyTable
    v-loading="tableLoading"
    :data="list.records"
    :total="list.total"
    style="width: 100%"
    @sizeChange="handleSizeChange"
    @currentChange="handleCurrentChange"
  >
    <my-column property="ip" label="IP地址" width="150">
      <template #default="scope">
        <span @click="showHost(scope.row)" class="operate">{{ scope.row.ip }}</span>
      </template>
    </my-column>
    <my-column property="hostName" label="主机名" />
    <my-column property="os" label="操作系统" />
    <my-column property="cpuBrand" label="处理器（CPU）" width="400">
      <template v-slot="{ row }">
        <span>{{ row.cpuNum }}核 {{ row.cpuBrand }}</span>
      </template>
    </my-column>
    <my-column property="memoryTotal" label="可用内存">
      <template v-slot="{ row }">
        <span>
          {{ convertBytes(row.memoryTotal).fixValue }}{{ convertBytes(row.memoryTotal).unit || "" }}
        </span>
      </template>
    </my-column>
    <my-column property="disks" label="存储" align="center" header-align="center">
      <template #default="scope">
        <span class="operate" @click="shouDetail(scope.row.disks, scope.row.ip)">详情</span>
      </template>
    </my-column>
    <my-column property="cpuUsage" label="CPU使用率" width="200">
      <template #default="{ row }">
        <div class="demo-progress">
          <el-progress
            :color="customColor"
            class="progress-bar"
            :stroke-width="8"
            :percentage="row.cpuUsage / row.cpuNum"
            stroke-linecap="butt"
          ></el-progress>
          <span class="progress-percent">{{ row.cpuUsage }}%</span>
        </div>
      </template>
    </my-column>
    <my-column property="memoryUsage" label="内存使用率" width="200">
      <template #default="{ row }">
        <div class="demo-progress">
          <el-progress
            :color="customColor"
            class="progress-bar"
            :stroke-width="8"
            :percentage="row.memoryUsage !== null ? row.memoryUsage : 0"
            stroke-linecap="butt"
          />
          <span class="progress-percent">{{ row.memoryUsage }}%</span>
        </div>
      </template>
    </my-column>
    <my-column property="" label="状态" header-align="center" :show-overflow-tooltip="false">
      <template #default="{ row }">
        <div style="text-align: center">
          <el-tooltip placement="top">
            <template #content>
              <span>最后上报时间：{{ row.timestamp }}</span>
            </template>
            <el-tag :type="row.onlineStatus == true ? 'success' : 'info'">
              {{ row.onlineStatus == true ? "运行中" : "未启动" }}
            </el-tag>
          </el-tooltip>
        </div>
      </template>
    </my-column>
    <my-column label="操作" align="center" header-align="center" fixed="right" width="120">
      <template #default="scope">
        <span class="operate" @click="showHost(scope.row)">查看详情</span>
      </template>
    </my-column>
  </MyTable>
  <div>
    <el-drawer v-model="diskVisible" :title="title" size="55%">
      <MyTable
        :data="drawerDisks"
        :total="drawerDisks.length"
        style="width: 100%"
        pagination-layout=""
      >
        <my-column property="device" label="设备" width="250" />
        <my-column property="device_type" label="类型" width="150" />
        <my-column property="fs" label="文件系统" width="150" />
        <my-column property="total" label="总容量" width="150">
          <template v-slot="{ row }">
            <span>
              {{ convertBytes(row.total).fixValue }}{{ convertBytes(row.total).unit || "" }}
            </span>
          </template>
        </my-column>
        <my-column property="mount_point" label="挂载点" />
      </MyTable>
    </el-drawer>
  </div>
  <div>
    <el-drawer v-model="hostVisible" :title="hostTitle" size="55%">
      <div class="flex justify-between indicator-wrapper">
        <BaseEcharts
          :options="statCpuUsage"
          width="22vw"
          height="250px"
          v-loading="statCpuUsageLoading"
        />
        <BaseEcharts
          :options="statMemoryUsage"
          width="22vw"
          height="250px"
          v-loading="statMemoryUsageLoading"
        />
      </div>
      <div class="flex justify-between indicator-wrapper">
        <BaseEcharts
          :options="statTcpConns"
          width="22vw"
          height="250px"
          v-loading="statTcpConnsLoading"
        />
        <BaseEcharts
          :options="statAvgBytes"
          width="22vw"
          height="250px"
          v-loading="statAvgBytesLoading"
        />
      </div>
      <div class="flex justify-between indicator-wrapper">
        <div v-if="showStatDiskReadSize" class="ranking-wrapper w-100%">
          <TitlecCom :title="statDiskReadSizeTitle"></TitlecCom>
          <el-empty description="暂无数据" />
        </div>
        <BaseEcharts
          v-else
          :options="statDiskReadSize"
          width="22vw"
          height="250px"
          v-loading="statDiskReadSizeLoading"
        />
        <div v-if="showStatDiskWriteSize" class="ranking-wrapper w-100%">
          <TitlecCom :title="statDiskWriteSizeTitle"></TitlecCom>
          <el-empty description="暂无数据" />
        </div>
        <BaseEcharts
          v-else
          :options="statDiskWriteSize"
          width="22vw"
          height="250px"
          v-loading="statDiskWriteSizeLoading"
        />
      </div>
      <div class="flex justify-between indicator-wrapper">
        <div v-if="showstatDiskReadIops" class="ranking-wrapper w-100%">
          <TitlecCom :title="statDiskReadIopsTitle"></TitlecCom>
          <el-empty description="暂无数据" />
        </div>
        <BaseEcharts
          v-else
          :options="statDiskReadIops"
          width="22vw"
          height="250px"
          v-loading="statDiskReadIopsLoading"
        />
        <div v-if="showStatDiskWriteIops" class="ranking-wrapper w-100%">
          <TitlecCom :title="statDiskWriteIopsTitle"></TitlecCom>
          <el-empty description="暂无数据" />
        </div>
        <BaseEcharts
          v-else
          :options="statDiskWriteIops"
          width="22vw"
          height="250px"
          v-loading="statDiskWriteIopsLoading"
        />
      </div>
      <div class="indicator-wrapper w-49.5%">
        <div v-if="showStatdiskFreeSpace" class="ranking-wrapper w-100%">
          <TitlecCom :title="statdiskFreeSpaceTitle"></TitlecCom>
          <el-empty description="暂无数据" />
        </div>
        <BaseEcharts
          v-else
          :options="statdiskFreeSpace"
          width="22vw"
          height="250px"
          v-loading="statdiskFreeSpaceLoading"
        />
      </div>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import TitlecCom from "@/components/TitleCom/index.vue";
import {
  getHostList,
  getStatCpuUsage,
  getStatMemoryUsage,
  getStatTcpConns,
  getStatDiskReadSize,
  getStatDiskWriteSize,
  getStatDiskReadIops,
  getStatDiskWriteIops,
  getStatdiskFreeSpace,
  getStatAvgBytes
} from "@/api/host/hostList";
import BaseEcharts from "@/components/baseEcharts/index.vue";
import { getChartOptions } from "@/components/baseEcharts/chartsOptions";
import { Search } from "@element-plus/icons-vue";
import MyTable from "@/components/table/my-table.vue";
import MyColumn from "@/components/table/my-column.vue";
import { convertBytes } from "@/utils/trafficStr";
import { applicationStore } from "@/store/modules/application";
const useApplicationStore = applicationStore();
const title = ref(""); //存储标题
const diskVisible = ref(false); //存储抽屉是否显示
const customColor = ref("#445fde");
const hostVisible = ref(false); //基础监测抽屉是否显示
const hostTitle = ref(""); //基础监测标题
const ip = ref(""); //基础监测ip参数
//loading动画
const tableLoading = ref(true);
//列表参数
const pageParams = reactive({
  appid: "",
  ip: "",
  page: 1,
  rows: 10
});
const handleSizeChange = (val: number) => {
  pageParams.rows = val;
  loadData();
};
//分页
const handleCurrentChange = (val: number) => {
  pageParams.page = val;
  loadData();
};
//搜索
function search() {
  // pageParams.page = 1;
  loadData();
}
const list = reactive({
  records: [],
  total: 0
});
//列表数据
function loadData() {
  tableLoading.value = true;
  pageParams.ip = pageParams.ip.trim();
  pageParams.appid = useApplicationStore.appId;
  getHostList(pageParams)
    .then(response => {
      if (response.code === 0) {
        response.records.forEach((record: { disks: string }) => {
          record.disks = JSON.parse(record.disks);
        });
        list.records = response.records;
        list.total = Number(response.total);
        tableLoading.value = false;
      }
    })
    .catch(error => {
      tableLoading.value = false;
      console.log(error);
    });
}
const drawerDisks = ref([]);
const shouDetail = (disks: any, ip: string) => {
  drawerDisks.value = disks;
  diskVisible.value = true;
  title.value = "存储详情 【" + ip + "】";
};
const showHost = (row: any) => {
  ip.value = row.ip;
  hostTitle.value = "基础监测 【" + row.ip + "】";
  hostVisible.value = true;
  StatCpuUsage();
  StatMemoryUsage();
  StatTcpConns();
  StatDiskReadSize();
  StatAvgBytes();
  StatDiskWriteSize();
  StatDiskReadIops();
  StatDiskWriteIops();
  StatdiskFreeSpace();
};
//监测传参
const dataTosed = reactive({
  appid: useApplicationStore.appId,
  ip: ip
});
function getTimeUnit(granularity: any) {
  switch (granularity) {
    case 0:
      return "按秒";
    case 1:
      return "按分钟";
    case 2:
      return "按小时";
    case 3:
      return "按天";
    case 4:
      return "按月";
    default:
      return undefined;
  }
}
//CPU监测（折线图）
const statCpuUsage = ref({}); //CPU监测（折线图）
const statCpuUsageLoading = ref(false); //CPU监测Loading
function StatCpuUsage() {
  statCpuUsageLoading.value = true;
  dataTosed.appid = useApplicationStore.appId;

  getStatCpuUsage(dataTosed)
    .then(res => {
      if (res.code === 0 && res.entity && res.entity.datas) {
        const originalTimes = res.entity.datas.map((data: { time: any }) => data.time);
        const seriesData = [res.entity.datas.map((data: { value: any }) => data.value)];
        const params = {
          name: "%",
          typ: res.entity.granularity,
          color: ["#5470C6"],
          titleType: "CPU使用率",
          originalTimes: originalTimes,
          seriesData: seriesData,
          type: "line"
        };
        statCpuUsage.value = getChartOptions(params);
        statCpuUsageLoading.value = false;
      }
    })
    .catch(error => {
      statCpuUsageLoading.value = false;
      console.log(error);
    });
}
//内存监测（折线图）
const statMemoryUsage = ref({}); //内存监测（折线图）
const statMemoryUsageLoading = ref(false); //内存监测Loading
function StatMemoryUsage() {
  statMemoryUsageLoading.value = true;
  dataTosed.appid = useApplicationStore.appId;

  getStatMemoryUsage(dataTosed)
    .then(res => {
      if (res.code === 0 && res.entity && res.entity.datas) {
        const originalTimes = res.entity.datas.map((data: { time: any }) => data.time);
        const seriesData = [res.entity.datas.map((data: { value: any }) => data.value)];
        const params = {
          name: "%",
          typ: res.entity.granularity,
          color: ["#5470C6"],
          titleType: "内存使用率",
          originalTimes: originalTimes,
          seriesData: seriesData,
          type: "line"
        };
        statMemoryUsage.value = getChartOptions(params);
        statMemoryUsageLoading.value = false;
      }
    })
    .catch(error => {
      statMemoryUsageLoading.value = false;
      console.log(error);
    });
}
//TCP连接数监测（折线图）
const statTcpConns = ref({}); //TCP连接数监测（折线图）
const statTcpConnsLoading = ref(false); //TCP连接数监测Loading
function StatTcpConns() {
  statTcpConnsLoading.value = true;
  dataTosed.appid = useApplicationStore.appId;

  getStatTcpConns(dataTosed)
    .then(res => {
      if (res.code === 0 && res.entity && res.entity.datas) {
        const originalTimes = res.entity.datas.map((data: { time: any }) => data.time);
        const seriesData = [res.entity.datas.map((data: { value: any }) => data.value)];
        const params = {
          name: "个",
          typ: res.entity.granularity,
          color: ["#5470C6"],
          titleType: "网络连接数",
          originalTimes: originalTimes,
          seriesData: seriesData,
          type: "line"
        };
        statTcpConns.value = getChartOptions(params);
        statTcpConnsLoading.value = false;
      }
    })
    .catch(error => {
      statTcpConnsLoading.value = false;
      console.log(error);
    });
}
//磁盘读速率（折线图）
const statDiskReadSize = ref({}); //磁盘读速率（折线图）
const statDiskReadSizeLoading = ref(false); //磁盘读速率Loading
const showStatDiskReadSize = ref(false); //磁盘读速率是否有数据
const statDiskReadSizeTitle = ref(""); //磁盘读速率标题
function StatDiskReadSize() {
  statDiskReadSizeLoading.value = true;
  dataTosed.appid = useApplicationStore.appId;

  getStatDiskReadSize(dataTosed)
    .then(res => {
      if (res.code === 0 && res.entity && res.entity.datas) {
        if (Object.keys(res.entity.datas).length === 0) {
          showStatDiskReadSize.value = true;
          statDiskReadSizeLoading.value = false;
          statDiskReadSizeTitle.value = `磁盘读带宽 （${getTimeUnit(res.entity.granularity)}）`;
          return;
        }
        const firstKey = Object.keys(res.entity.datas)[0];
        const originalTimes = res.entity.datas[firstKey].map((data: { time: any }) => data.time);
        const seriesData = Object.keys(res.entity.datas).map(key =>
          res.entity.datas[key].map((data: { value: any }) => data.value)
        );
        const defaultColors = ["#78bf75", "#4ea3ef", "#ff9a3e", "#ef4e4e", "#4e9aff"];
        const names = Object.keys(res.entity.datas).map(key => `${key}`);
        const colors = defaultColors.slice(0, names.length);
        const params = {
          names: names,
          color: colors,
          name: "KB/s",
          typ: res.entity.granularity,
          titleType: "磁盘读带宽",
          originalTimes: originalTimes,
          seriesData: seriesData,
          type: "line",
          legend: { show: true }
        };
        statDiskReadSize.value = getChartOptions(params);
        showStatDiskReadSize.value = false;
        statDiskReadSizeLoading.value = false;
      }
    })
    .catch(error => {
      statDiskReadSizeLoading.value = false;
      console.log(error);
    });
}
//磁盘写速率（折线图）
const statDiskWriteSize = ref({}); //磁盘写速率（折线图）
const statDiskWriteSizeLoading = ref(false); //磁盘写速率Loading
const showStatDiskWriteSize = ref(false); //磁盘写速率是否有数据
const statDiskWriteSizeTitle = ref(""); //磁盘写速率标题
function StatDiskWriteSize() {
  statDiskWriteSizeLoading.value = true;
  dataTosed.appid = useApplicationStore.appId;

  getStatDiskWriteSize(dataTosed)
    .then(res => {
      if (res.code === 0 && res.entity && res.entity.datas) {
        if (Object.keys(res.entity.datas).length === 0) {
          showStatDiskWriteSize.value = true;
          statDiskWriteSizeLoading.value = false;
          statDiskWriteSizeTitle.value = `磁盘写带宽 （${getTimeUnit(res.entity.granularity)}）`;
          return;
        }
        const firstKey = Object.keys(res.entity.datas)[0];
        const originalTimes = res.entity.datas[firstKey].map((data: { time: any }) => data.time);
        const seriesData = Object.keys(res.entity.datas).map(key =>
          res.entity.datas[key].map((data: { value: any }) => data.value)
        );
        const defaultColors = ["#78bf75", "#4ea3ef", "#ff9a3e", "#ef4e4e", "#4e9aff"];
        const names = Object.keys(res.entity.datas).map(key => `${key}`);
        const colors = defaultColors.slice(0, names.length);
        const params = {
          names: names,
          color: colors,
          name: "KB/s",
          typ: res.entity.granularity,
          titleType: "磁盘写带宽",
          originalTimes: originalTimes,
          seriesData: seriesData,
          type: "line",
          legend: { show: true }
        };
        statDiskWriteSize.value = getChartOptions(params);
        showStatDiskWriteSize.value = false;
        statDiskWriteSizeLoading.value = false;
      }
    })
    .catch(error => {
      statDiskWriteSizeLoading.value = false;
      console.log(error);
    });
}
// 磁盘读操作速率（折线图）
const statDiskReadIops = ref({}); //磁盘读操作速率（折线图）
const statDiskReadIopsLoading = ref(false); //磁盘读操作速率Loading
const showstatDiskReadIops = ref(false); //磁盘读操作速率是否有数据
const statDiskReadIopsTitle = ref(""); //磁盘读操作速率标题
function StatDiskReadIops() {
  statDiskReadIopsLoading.value = true;
  dataTosed.appid = useApplicationStore.appId;

  getStatDiskReadIops(dataTosed)
    .then(res => {
      if (res.code === 0 && res.entity && res.entity.datas) {
        if (Object.keys(res.entity.datas).length === 0) {
          showstatDiskReadIops.value = true;
          statDiskReadIopsLoading.value = false;
          statDiskReadIopsTitle.value = `磁盘读IOPS （${getTimeUnit(res.entity.granularity)}）`;
          return;
        }
        const firstKey = Object.keys(res.entity.datas)[0];
        const originalTimes = res.entity.datas[firstKey].map((data: { time: any }) => data.time);
        const seriesData = Object.keys(res.entity.datas).map(key =>
          res.entity.datas[key].map((data: { value: any }) => data.value)
        );
        const defaultColors = ["#78bf75", "#4ea3ef", "#ff9a3e", "#ef4e4e", "#4e9aff"];
        const names = Object.keys(res.entity.datas).map(key => `${key}`);
        const colors = defaultColors.slice(0, names.length);
        const params = {
          names: names,
          color: colors,
          name: "请求/s",
          typ: res.entity.granularity,
          titleType: "磁盘读IOPS",
          originalTimes: originalTimes,
          seriesData: seriesData,
          type: "line",
          legend: { show: true }
        };
        statDiskReadIops.value = getChartOptions(params);
        showstatDiskReadIops.value = false;
        statDiskReadIopsLoading.value = false;
      }
    })
    .catch(error => {
      statDiskReadIopsLoading.value = false;
      console.log(error);
    });
}
// 磁盘写操作速率（折线图）
const statDiskWriteIops = ref({}); //磁盘写操作速率（折线图）
const statDiskWriteIopsLoading = ref(false); //磁盘写操作速率Loading
const showStatDiskWriteIops = ref(false); //磁盘写操作速率是否有数据
const statDiskWriteIopsTitle = ref(""); //磁盘写操作速率标题
function StatDiskWriteIops() {
  statDiskWriteIopsLoading.value = true;
  dataTosed.appid = useApplicationStore.appId;

  getStatDiskWriteIops(dataTosed)
    .then(res => {
      if (res.code === 0 && res.entity && res.entity.datas) {
        if (Object.keys(res.entity.datas).length === 0) {
          showStatDiskWriteIops.value = true;
          statDiskWriteIopsLoading.value = false;
          statDiskWriteIopsTitle.value = `磁盘写IOPS （${getTimeUnit(res.entity.granularity)}）`;
          return;
        }
        const firstKey = Object.keys(res.entity.datas)[0];
        const originalTimes = res.entity.datas[firstKey].map((data: { time: any }) => data.time);
        const seriesData = Object.keys(res.entity.datas).map(key =>
          res.entity.datas[key].map((data: { value: any }) => data.value)
        );
        const defaultColors = ["#78bf75", "#4ea3ef", "#ff9a3e", "#ef4e4e", "#4e9aff"];
        const names = Object.keys(res.entity.datas).map(key => `${key}`);
        const colors = defaultColors.slice(0, names.length);
        const params = {
          names: names,
          color: colors,
          name: "请求/s",
          typ: res.entity.granularity,
          titleType: "磁盘写IOPS",
          originalTimes: originalTimes,
          seriesData: seriesData,
          type: "line",
          legend: { show: true }
        };
        statDiskWriteIops.value = getChartOptions(params);
        showStatDiskWriteIops.value = false;
        statDiskWriteIopsLoading.value = false;
      }
    })
    .catch(error => {
      statDiskWriteIopsLoading.value = false;
      console.log(error);
    });
}
//磁盘剩余空间（折线图）
const statdiskFreeSpace = ref({}); //磁盘剩余空间（折线图）
const statdiskFreeSpaceLoading = ref(false); //磁盘剩余空间Loading
const showStatdiskFreeSpace = ref(false); //磁盘剩余空间是否有数据
const statdiskFreeSpaceTitle = ref(""); //磁盘剩余空间标题
function StatdiskFreeSpace() {
  statdiskFreeSpaceLoading.value = true;
  dataTosed.appid = useApplicationStore.appId;

  getStatdiskFreeSpace(dataTosed)
    .then(res => {
      if (res.code === 0 && res.entity && res.entity.datas) {
        if (Object.keys(res.entity.datas).length === 0) {
          showStatdiskFreeSpace.value = true;
          statdiskFreeSpaceLoading.value = false;
          statdiskFreeSpaceTitle.value = `磁盘剩余空间 （${getTimeUnit(res.entity.granularity)}）`;
          return;
        }
        const firstKey = Object.keys(res.entity.datas)[0];
        const originalTimes = res.entity.datas[firstKey].map((data: { time: any }) => data.time);
        const seriesData = Object.keys(res.entity.datas).map(key =>
          res.entity.datas[key].map((data: { value: any }) => data.value)
        );
        const defaultColors = ["#78bf75", "#4ea3ef", "#ff9a3e", "#ef4e4e", "#4e9aff"];
        const names = Object.keys(res.entity.datas).map(key => `${key}`);
        const colors = defaultColors.slice(0, names.length);
        const params = {
          names: names,
          color: colors,
          name: "GB",
          typ: res.entity.granularity,
          titleType: "磁盘剩余空间",
          originalTimes: originalTimes,
          seriesData: seriesData,
          type: "line",
          legend: { show: true }
        };
        statdiskFreeSpace.value = getChartOptions(params);
        showStatdiskFreeSpace.value = false;
        statdiskFreeSpaceLoading.value = false;
      }
    })
    .catch(error => {
      statdiskFreeSpaceLoading.value = false;
      console.log(error);
    });
}
const statAvgBytes = ref({}); //平均流量统计（图表）
const statAvgBytesLoading = ref(false); //平均流量统计Loading
function StatAvgBytes() {
  statAvgBytesLoading.value = true;
  dataTosed.appid = useApplicationStore.appId;
  getStatAvgBytes(dataTosed)
    .then(res => {
      if (res.code === 0 && res.entity && res.entity.datas) {
        const originalTimes = res.entity.datas.map((data: { time: any }) => data.time);
        const seriesData = [res.entity.datas.map((data: { value: any }) => data.value)];
        const params = {
          name: "KB/s",
          typ: res.entity.granularity,
          color: ["#5470C6"],
          titleType: "网络速率",
          originalTimes: originalTimes,
          seriesData: seriesData,
          type: "line",
          areaStyle: true,
          numberType: false
        };
        statAvgBytes.value = getChartOptions(params);
        statAvgBytesLoading.value = false;
      }
    })
    .catch(error => {
      statAvgBytesLoading.value = false;
      console.log(error);
    });
}
onMounted(() => {
  loadData();
});
</script>

<style lang="scss" scoped>
.tb-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.operate {
  color: #0064c8;
  cursor: pointer;
}
.indicator-wrapper > *:not(:last-child) {
  margin-right: 8px;
  box-sizing: border-box;
}
.ranking-wrapper {
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 2px;
  margin-bottom: 10px;
}
:deep(.el-drawer__header) {
  margin: 5px !important;
}
:deep(.el-progress__text) {
  display: none;
}
.demo-progress {
  display: flex;
  align-items: center;
}

.progress-bar {
  min-width: 100px;
  max-width: 250px;
  margin-right: 10px;
}

.progress-percent {
  white-space: nowrap;
}
</style>
