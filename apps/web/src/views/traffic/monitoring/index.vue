<template>
  <div class="flex justify-between indicator-wrapper">
    <BaseEcharts :options="statCpuUsage" height="250px" v-loading="statCpuUsageLoading" />
    <BaseEcharts :options="statMemoryUsage" height="250px" v-loading="statMemoryUsageLoading" />
    <BaseEcharts :options="statTcpConns" height="250px" v-loading="statTcpConnsLoading" />
  </div>
  <div class="flex justify-between indicator-wrapper">
    <BaseEcharts :options="statAvgBytes" height="250px" v-loading="statAvgBytesLoading" />
    <div v-if="showStatdiskFreeSpace" class="ranking-wrapper w-100%">
      <TitlecCom :title="statdiskFreeSpaceTitle"></TitlecCom>
      <el-empty description="暂无数据" />
    </div>
    <BaseEcharts
      v-else
      :options="statdiskFreeSpace"
      height="250px"
      v-loading="statdiskFreeSpaceLoading"
    />
    <div v-if="showStatDiskReadSize" class="ranking-wrapper w-100%">
      <TitlecCom :title="statDiskReadSizeTitle"></TitlecCom>
      <el-empty description="暂无数据" />
    </div>
    <BaseEcharts
      v-else
      :options="statDiskReadSize"
      height="250px"
      v-loading="statDiskReadSizeLoading"
    />
  </div>
  <div class="flex justify-start indicator-wrapper">
    <div v-if="showStatDiskWriteSize" class="ranking-wrapper w-100%">
      <TitlecCom :title="statDiskWriteSizeTitle"></TitlecCom>
      <el-empty description="暂无数据" />
    </div>
    <BaseEcharts
      v-else
      :options="statDiskWriteSize"
      height="250px"
      v-loading="statDiskWriteSizeLoading"
    />
    <div v-if="showstatDiskReadIops" class="ranking-wrapper w-100%">
      <TitlecCom :title="statDiskReadIopsTitle"></TitlecCom>
      <el-empty description="暂无数据" />
    </div>
    <BaseEcharts
      v-else
      :options="statDiskReadIops"
      height="250px"
      v-loading="statDiskReadIopsLoading"
    />
    <div v-if="showStatDiskWriteIops" class="ranking-wrapper w-100%">
      <TitlecCom :title="statDiskWriteIopsTitle"></TitlecCom>
      <el-empty description="暂无数据" />
    </div>
    <BaseEcharts
      v-else
      :options="statDiskWriteIops"
      height="250px"
      v-loading="statDiskWriteIopsLoading"
    />
  </div>
</template>

<script setup lang="ts">
import TitlecCom from "@/components/TitleCom/index.vue";
import {
  getStatCpuUsage,
  getStatMemoryUsage,
  getStatTcpConns,
  getStatDiskReadSize,
  getStatDiskWriteSize,
  getStatDiskReadIops,
  getStatDiskWriteIops,
  getStatdiskFreeSpace,
  getStatAvgBytes
} from "@/api/host/hostList";
import BaseEcharts from "@/components/baseEcharts/index.vue";
import { getChartOptions } from "@/components/baseEcharts/chartsOptions";
import { applicationStore } from "@/store/modules/application";
import { serviceNameStore } from "@/store/modules/service";
const useApplicationStore = applicationStore();
const useServiceNameStore = serviceNameStore();
function getTimeUnit(granularity: any) {
  switch (granularity) {
    case 0:
      return "按秒";
    case 1:
      return "按分钟";
    case 2:
      return "按小时";
    case 3:
      return "按天";
    case 4:
      return "按月";
    default:
      return undefined;
  }
}
//监测传参
const dataTosed = reactive({
  appid: "",
  ip: ""
});
//CPU监测（折线图）
const statCpuUsage = ref({}); //CPU监测（折线图）
const statCpuUsageLoading = ref(false); //CPU监测Loading
function StatCpuUsage() {
  statCpuUsageLoading.value = true;
  dataTosed.appid = useApplicationStore.appId;
  dataTosed.ip = useServiceNameStore.serviceName;
  getStatCpuUsage(dataTosed)
    .then(res => {
      if (res.code === 0 && res.entity && res.entity.datas) {
        const originalTimes = res.entity.datas.map((data: { time: any }) => data.time);
        const seriesData = [res.entity.datas.map((data: { value: any }) => data.value)];
        const params = {
          name: "%",
          typ: res.entity.granularity,
          color: ["#5470C6"],
          titleType: "CPU使用率",
          originalTimes: originalTimes,
          seriesData: seriesData,
          type: "line"
        };
        statCpuUsage.value = getChartOptions(params);
        statCpuUsageLoading.value = false;
      }
    })
    .catch(error => {
      statCpuUsageLoading.value = false;
      console.log(error);
    });
}
//内存监测（折线图）
const statMemoryUsage = ref({}); //内存监测（折线图）
const statMemoryUsageLoading = ref(false); //内存监测Loading
function StatMemoryUsage() {
  statMemoryUsageLoading.value = true;
  dataTosed.appid = useApplicationStore.appId;
  dataTosed.ip = useServiceNameStore.serviceName;
  getStatMemoryUsage(dataTosed)
    .then(res => {
      if (res.code === 0 && res.entity && res.entity.datas) {
        const originalTimes = res.entity.datas.map((data: { time: any }) => data.time);
        const seriesData = [res.entity.datas.map((data: { value: any }) => data.value)];
        const params = {
          name: "%",
          typ: res.entity.granularity,
          color: ["#5470C6"],
          titleType: "内存使用率",
          originalTimes: originalTimes,
          seriesData: seriesData,
          type: "line"
        };
        statMemoryUsage.value = getChartOptions(params);
        statMemoryUsageLoading.value = false;
      }
    })
    .catch(error => {
      statMemoryUsageLoading.value = false;
      console.log(error);
    });
}
//TCP连接数监测（折线图）
const statTcpConns = ref({}); //TCP连接数监测（折线图）
const statTcpConnsLoading = ref(false); //TCP连接数监测Loading
function StatTcpConns() {
  statTcpConnsLoading.value = true;
  dataTosed.appid = useApplicationStore.appId;
  dataTosed.ip = useServiceNameStore.serviceName;
  getStatTcpConns(dataTosed)
    .then(res => {
      if (res.code === 0 && res.entity && res.entity.datas) {
        const originalTimes = res.entity.datas.map((data: { time: any }) => data.time);
        const seriesData = [res.entity.datas.map((data: { value: any }) => data.value)];
        const params = {
          name: "个",
          typ: res.entity.granularity,
          color: ["#5470C6"],
          titleType: "网络连接数",
          originalTimes: originalTimes,
          seriesData: seriesData,
          type: "line"
        };
        statTcpConns.value = getChartOptions(params);
        statTcpConnsLoading.value = false;
      }
    })
    .catch(error => {
      statTcpConnsLoading.value = false;
      console.log(error);
    });
}
//磁盘读速率（折线图）
const statDiskReadSize = ref({}); //磁盘读速率（折线图）
const statDiskReadSizeLoading = ref(false); //磁盘读速率Loading
const showStatDiskReadSize = ref(false); //磁盘读速率是否有数据
const statDiskReadSizeTitle = ref(""); //磁盘读速率标题
function StatDiskReadSize() {
  statDiskReadSizeLoading.value = true;
  dataTosed.appid = useApplicationStore.appId;
  dataTosed.ip = useServiceNameStore.serviceName;
  getStatDiskReadSize(dataTosed)
    .then(res => {
      if (res.code === 0 && res.entity && res.entity.datas) {
        if (Object.keys(res.entity.datas).length === 0) {
          showStatDiskReadSize.value = true;
          statDiskReadSizeLoading.value = false;
          statDiskReadSizeTitle.value = `磁盘读带宽 （${getTimeUnit(res.entity.granularity)}）`;
          return;
        }
        const firstKey = Object.keys(res.entity.datas)[0];
        const originalTimes = res.entity.datas[firstKey].map((data: { time: any }) => data.time);
        const seriesData = Object.keys(res.entity.datas).map(key =>
          res.entity.datas[key].map((data: { value: any }) => data.value)
        );
        const defaultColors = ["#78bf75", "#4ea3ef", "#ff9a3e", "#ef4e4e", "#4e9aff"];
        const names = Object.keys(res.entity.datas).map(key => `${key}`);
        const colors = defaultColors.slice(0, names.length);
        const params = {
          names: names,
          color: colors,
          name: "KB/s",
          typ: res.entity.granularity,
          titleType: "磁盘读带宽",
          originalTimes: originalTimes,
          seriesData: seriesData,
          type: "line",
          legend: { show: true }
        };
        statDiskReadSize.value = getChartOptions(params);
        showStatDiskReadSize.value = false;
        statDiskReadSizeLoading.value = false;
      }
    })
    .catch(error => {
      statDiskReadSizeLoading.value = false;
      console.log(error);
    });
}
//磁盘写速率（折线图）
const statDiskWriteSize = ref({}); //磁盘写速率（折线图）
const statDiskWriteSizeLoading = ref(false); //磁盘写速率Loading
const showStatDiskWriteSize = ref(false); //磁盘写速率是否有数据
const statDiskWriteSizeTitle = ref(""); //磁盘写速率标题
function StatDiskWriteSize() {
  statDiskWriteSizeLoading.value = true;
  dataTosed.appid = useApplicationStore.appId;
  dataTosed.ip = useServiceNameStore.serviceName;
  getStatDiskWriteSize(dataTosed)
    .then(res => {
      if (res.code === 0 && res.entity && res.entity.datas) {
        if (Object.keys(res.entity.datas).length === 0) {
          showStatDiskWriteSize.value = true;
          statDiskWriteSizeLoading.value = false;
          statDiskWriteSizeTitle.value = `磁盘写带宽 （${getTimeUnit(res.entity.granularity)}）`;
          return;
        }
        const firstKey = Object.keys(res.entity.datas)[0];
        const originalTimes = res.entity.datas[firstKey].map((data: { time: any }) => data.time);
        const seriesData = Object.keys(res.entity.datas).map(key =>
          res.entity.datas[key].map((data: { value: any }) => data.value)
        );
        const defaultColors = ["#78bf75", "#4ea3ef", "#ff9a3e", "#ef4e4e", "#4e9aff"];
        const names = Object.keys(res.entity.datas).map(key => `${key}`);
        const colors = defaultColors.slice(0, names.length);
        const params = {
          names: names,
          color: colors,
          name: "KB/s",
          typ: res.entity.granularity,
          titleType: "磁盘写带宽",
          originalTimes: originalTimes,
          seriesData: seriesData,
          type: "line",
          legend: { show: true }
        };
        statDiskWriteSize.value = getChartOptions(params);
        showStatDiskWriteSize.value = false;
        statDiskWriteSizeLoading.value = false;
      }
    })
    .catch(error => {
      statDiskWriteSizeLoading.value = false;
      console.log(error);
    });
}
// 磁盘读操作速率（折线图）
const statDiskReadIops = ref({}); //磁盘读操作速率（折线图）
const statDiskReadIopsLoading = ref(false); //磁盘读操作速率Loading
const showstatDiskReadIops = ref(false); //磁盘读操作速率是否有数据
const statDiskReadIopsTitle = ref(""); //磁盘读操作速率标题
function StatDiskReadIops() {
  statDiskReadIopsLoading.value = true;
  dataTosed.appid = useApplicationStore.appId;
  dataTosed.ip = useServiceNameStore.serviceName;
  getStatDiskReadIops(dataTosed)
    .then(res => {
      if (res.code === 0 && res.entity && res.entity.datas) {
        if (Object.keys(res.entity.datas).length === 0) {
          showstatDiskReadIops.value = true;
          statDiskReadIopsLoading.value = false;
          statDiskReadIopsTitle.value = `磁盘读IOPS （${getTimeUnit(res.entity.granularity)}）`;
          return;
        }
        const firstKey = Object.keys(res.entity.datas)[0];
        const originalTimes = res.entity.datas[firstKey].map((data: { time: any }) => data.time);
        const seriesData = Object.keys(res.entity.datas).map(key =>
          res.entity.datas[key].map((data: { value: any }) => data.value)
        );
        const defaultColors = ["#78bf75", "#4ea3ef", "#ff9a3e", "#ef4e4e", "#4e9aff"];
        const names = Object.keys(res.entity.datas).map(key => `${key}`);
        const colors = defaultColors.slice(0, names.length);
        const params = {
          names: names,
          color: colors,
          name: "请求/s",
          typ: res.entity.granularity,
          titleType: "磁盘读IOPS",
          originalTimes: originalTimes,
          seriesData: seriesData,
          type: "line",
          legend: { show: true }
        };
        statDiskReadIops.value = getChartOptions(params);
        showstatDiskReadIops.value = false;
        statDiskReadIopsLoading.value = false;
      }
    })
    .catch(error => {
      statDiskReadIopsLoading.value = false;
      console.log(error);
    });
}
// 磁盘写操作速率（折线图）
const statDiskWriteIops = ref({}); //磁盘写操作速率（折线图）
const statDiskWriteIopsLoading = ref(false); //磁盘写操作速率Loading
const showStatDiskWriteIops = ref(false); //磁盘写操作速率是否有数据
const statDiskWriteIopsTitle = ref(""); //磁盘写操作速率标题
function StatDiskWriteIops() {
  statDiskWriteIopsLoading.value = true;
  dataTosed.appid = useApplicationStore.appId;
  dataTosed.ip = useServiceNameStore.serviceName;
  getStatDiskWriteIops(dataTosed)
    .then(res => {
      if (res.code === 0 && res.entity && res.entity.datas) {
        if (Object.keys(res.entity.datas).length === 0) {
          showStatDiskWriteIops.value = true;
          statDiskWriteIopsLoading.value = false;
          statDiskWriteIopsTitle.value = `磁盘写IOPS （${getTimeUnit(res.entity.granularity)}）`;
          return;
        }
        const firstKey = Object.keys(res.entity.datas)[0];
        const originalTimes = res.entity.datas[firstKey].map((data: { time: any }) => data.time);
        const seriesData = Object.keys(res.entity.datas).map(key =>
          res.entity.datas[key].map((data: { value: any }) => data.value)
        );
        const defaultColors = ["#78bf75", "#4ea3ef", "#ff9a3e", "#ef4e4e", "#4e9aff"];
        const names = Object.keys(res.entity.datas).map(key => `${key}`);
        const colors = defaultColors.slice(0, names.length);
        const params = {
          names: names,
          color: colors,
          name: "请求/s",
          typ: res.entity.granularity,
          titleType: "磁盘写IOPS",
          originalTimes: originalTimes,
          seriesData: seriesData,
          type: "line",
          legend: { show: true }
        };
        statDiskWriteIops.value = getChartOptions(params);
        showStatDiskWriteIops.value = false;
        statDiskWriteIopsLoading.value = false;
      }
    })
    .catch(error => {
      statDiskWriteIopsLoading.value = false;
      console.log(error);
    });
}
//磁盘剩余空间（折线图）
const statdiskFreeSpace = ref({}); //磁盘剩余空间（折线图）
const statdiskFreeSpaceLoading = ref(false); //磁盘剩余空间Loading
const showStatdiskFreeSpace = ref(false); //磁盘剩余空间是否有数据
const statdiskFreeSpaceTitle = ref(""); //磁盘剩余空间标题
function StatdiskFreeSpace() {
  statdiskFreeSpaceLoading.value = true;
  dataTosed.appid = useApplicationStore.appId;
  dataTosed.ip = useServiceNameStore.serviceName;
  getStatdiskFreeSpace(dataTosed)
    .then(res => {
      if (res.code === 0 && res.entity && res.entity.datas) {
        if (Object.keys(res.entity.datas).length === 0) {
          showStatdiskFreeSpace.value = true;
          statdiskFreeSpaceLoading.value = false;
          statdiskFreeSpaceTitle.value = `磁盘剩余空间 （${getTimeUnit(res.entity.granularity)}）`;
          return;
        }
        const firstKey = Object.keys(res.entity.datas)[0];
        const originalTimes = res.entity.datas[firstKey].map((data: { time: any }) => data.time);
        const seriesData = Object.keys(res.entity.datas).map(key =>
          res.entity.datas[key].map((data: { value: any }) => data.value)
        );
        const defaultColors = ["#78bf75", "#4ea3ef", "#ff9a3e", "#ef4e4e", "#4e9aff"];
        const names = Object.keys(res.entity.datas).map(key => `${key}`);
        const colors = defaultColors.slice(0, names.length);
        const params = {
          names: names,
          color: colors,
          name: "GB",
          typ: res.entity.granularity,
          titleType: "磁盘剩余空间",
          originalTimes: originalTimes,
          seriesData: seriesData,
          type: "line",
          legend: { show: true }
        };
        statdiskFreeSpace.value = getChartOptions(params);
        showStatdiskFreeSpace.value = false;
        statdiskFreeSpaceLoading.value = false;
      }
    })
    .catch(error => {
      statdiskFreeSpaceLoading.value = false;
      console.log(error);
    });
}
const statAvgBytes = ref({}); //平均流量统计（图表）
const statAvgBytesLoading = ref(false); //平均流量统计Loading
function StatAvgBytes() {
  statAvgBytesLoading.value = true;
  dataTosed.appid = useApplicationStore.appId;
  dataTosed.ip = useServiceNameStore.serviceName;
  getStatAvgBytes(dataTosed)
    .then(res => {
      if (res.code === 0 && res.entity && res.entity.datas) {
        const originalTimes = res.entity.datas.map((data: { time: any }) => data.time);
        const seriesData = [res.entity.datas.map((data: { value: any }) => data.value)];
        const params = {
          name: "KB/s",
          typ: res.entity.granularity,
          color: ["#5470C6"],
          titleType: "网络速率",
          originalTimes: originalTimes,
          seriesData: seriesData,
          type: "line",
          areaStyle: true,
          numberType: false
        };
        statAvgBytes.value = getChartOptions(params);
        statAvgBytesLoading.value = false;
      }
    })
    .catch(error => {
      statAvgBytesLoading.value = false;
      console.log(error);
    });
}
onMounted(() => {
  StatCpuUsage();
  StatMemoryUsage();
  StatTcpConns();
  StatDiskReadSize();
  StatDiskWriteSize();
  StatDiskReadIops();
  StatDiskWriteIops();
  StatdiskFreeSpace();
  StatAvgBytes();
});
</script>

<style lang="scss" scoped>
.indicator-wrapper > *:not(:last-child) {
  margin-right: 8px;
  box-sizing: border-box;
}
.ranking-wrapper {
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 2px;
  margin-bottom: 10px;
}
</style>
