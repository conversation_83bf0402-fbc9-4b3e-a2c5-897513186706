<template>
  <div class="ai-chat-container">
    <!-- 顶部标题 -->
    <div class="header">
      <div class="header-content">
        <h2>🤖 日志分析助手</h2>
        <el-button
          type="primary"
          :icon="Download"
          @click="downloadPDF"
          :loading="downloading"
          :disabled="!analysisData.statistics"
        >
          下载PDF
        </el-button>
      </div>
    </div>
    <!-- 聊天消息区域 -->
    <div class="chat-messages" ref="messagesContainer">
      <div v-if="!isLoading && !analysisData.statistics && !isSSERunning" class="empty-state">
        <div class="empty-content">
          <h3>欢迎使用日志分析助手</h3>
          <p>请前往日志看台页面选择时间范围并点击"日志分析"按钮开始分析</p>
          <el-button type="primary" @click="goToLogOverview">
            <el-icon><Document /></el-icon>
            前往日志看台
          </el-button>
        </div>
      </div>
      <div
        v-for="(message, index) in sseMessages"
        :key="index"
        class="message"
        :class="message.type === 'error' ? 'error-message' : 'ai-message'"
      >
        <div class="message-avatar">🤖</div>
        <div class="message-content">
          <div class="message-text" v-html="formatMessage(message.content)"></div>
          <div class="message-time">{{ message.timestamp }}</div>
        </div>
      </div>
      <div v-if="isLoading && !analysisData.statistics" class="message ai-message">
        <div class="message-avatar">🤖</div>
        <div class="message-content">
          <div class="loading-message">
            <div class="loading-spinner"></div>
            <div class="loading-text">正在分析日志数据，请稍候...</div>
          </div>
        </div>
      </div>
      <div v-if="analysisData.statistics" class="message ai-message">
        <div class="message-content">
          <div class="result-card">
            <div class="result-section" v-if="analysisData.statistics">
              <h4>📊 统计概览</h4>
              <div class="stats-grid">
                <div class="stat-item">
                  <div class="stat-value">
                    {{ parseInt(analysisData.statistics.totalLogs).toLocaleString() }}
                  </div>
                  <div class="stat-label">总日志数</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">{{ analysisData.statistics.errorRate.toFixed(2) }}%</div>
                  <div class="stat-label">错误率</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">{{ analysisData.statistics.timeRange }}</div>
                  <div class="stat-label">时间范围</div>
                </div>
              </div>
              <div class="level-distribution">
                <h4>📝 日志级别分布</h4>
                <div class="level-tags">
                  <el-tag
                    v-for="(count, level) in analysisData.statistics.levelDistribution"
                    :key="level"
                    :type="getLevelType(String(level))"
                    size="large"
                    class="level-tag"
                  >
                    {{ level }}: {{ parseInt(String(count)).toLocaleString() }}
                  </el-tag>
                </div>
              </div>
            </div>
            <div class="result-section" v-if="analysisData.timeTrend">
              <h4>📈 日志级别趋势</h4>
              <div class="trend-chart">
                <BaseEcharts :options="timeTrendChartOptions" height="200px" />
              </div>
            </div>
            <div class="result-section" v-if="analysisData.keywords">
              <h4>🔍 关键词分析</h4>
              <div class="keywords-grid">
                <div
                  v-for="(keyword, index) in analysisData.keywords.slice(0, 10)"
                  :key="index"
                  class="keyword-card"
                >
                  <div class="keyword-header">
                    <span class="keyword-text"
                      >{{ keyword.keyword }}
                      <span class="keyword-freq">({{ keyword.frequency }})</span></span
                    >
                  </div>
                  <div class="keyword-levels">
                    <el-tag
                      v-for="level in keyword.associatedLevels"
                      :key="level"
                      :type="getLevelType(String(level))"
                      size="small"
                    >
                      {{ level }}
                    </el-tag>
                  </div>
                </div>
              </div>
            </div>
            <div class="result-section" v-if="analysisData.sources">
              <h4>🌐 来源分析</h4>
              <div class="source-analysis">
                <div class="source-section">
                  <h5>IP分布</h5>
                  <div
                    v-for="ip in analysisData.sources.ipDistribution"
                    :key="ip.name"
                    class="source-item"
                  >
                    <span class="source-name">{{ ip.name }}</span>
                    <span class="source-count">{{ parseInt(ip.count).toLocaleString() }}</span>
                    <span class="source-percentage">{{ ip.percentage.toFixed(2) }}%</span>
                  </div>
                </div>
                <div class="source-section">
                  <h5>来源类型</h5>
                  <div
                    v-for="type in analysisData.sources.sourceTypeDistribution"
                    :key="type.name"
                    class="source-item"
                  >
                    <span class="source-name">{{ type.name }}</span>
                    <span class="source-count">{{ parseInt(type.count).toLocaleString() }}</span>
                    <span class="source-percentage">{{ type.percentage.toFixed(2) }}%</span>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="result-section"
              v-if="analysisData.anomalies && analysisData.anomalies.length > 0"
            >
              <h4>⚠️ 异常检测</h4>
              <div class="anomalies-list">
                <div
                  v-for="(anomaly, index) in analysisData.anomalies"
                  :key="index"
                  class="anomaly-item"
                >
                  <div class="anomaly-header">
                    <el-tag
                      :type="
                        anomaly.severity === 'HIGH'
                          ? 'danger'
                          : anomaly.severity === 'MEDIUM'
                            ? 'warning'
                            : 'info'
                      "
                      size="small"
                    >
                      {{ anomaly.patternType }}
                    </el-tag>
                    <span class="anomaly-severity">{{ anomaly.severity }}</span>
                  </div>
                  <div class="anomaly-description">{{ anomaly.description }}</div>
                  <div class="anomaly-recommendation">{{ anomaly.recommendation }}</div>
                </div>
              </div>
            </div>
            <div
              class="result-section"
              v-if="analysisData.errorTop10 && analysisData.errorTop10.length > 0"
            >
              <h4>🚨 重复ERROR日志</h4>
              <div class="error-top10-grid">
                <div
                  v-for="(error, index) in analysisData.errorTop10.slice(0, 10)"
                  :key="index"
                  class="error-card"
                >
                  <div class="error-header">
                    <span class="error-summary">{{ error.errorSummary }}</span>
                    <!-- <el-tag type="danger" size="small" class="severity-tag">
                      {{ error.severity }}
                    </el-tag> -->
                  </div>
                  <div class="error-stats">
                    <span class="error-count">{{ parseInt(error.count).toLocaleString() }}次</span>
                    <span class="error-rate">{{ error.errorRate.toFixed(2) }}%</span>
                  </div>
                  <div class="error-time">
                    <div class="time-item">
                      <span class="time-label">首次出现时间:</span>
                      <span class="time-value">{{ error.firstOccurrence }}</span>
                    </div>
                    <div class="time-item">
                      <span class="time-label">最后出现时间:</span>
                      <span class="time-value">{{ error.lastOccurrence }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="result-section"
              v-if="analysisData.aiSummary !== undefined && analysisData.aiSummary !== ''"
            >
              <h4>🤖 AI分析摘要</h4>
              <div class="ai-summary">
                <div class="ai-message-content">
                  <span
                    v-for="(char, index) in aiMessageChars"
                    :key="index"
                    class="ai-char"
                    :class="{
                      newline: char === '\n',
                      typing: index === aiMessageChars.length - 1 && isSSERunning
                    }"
                  >
                    {{ char === "\n" ? "" : char }}
                  </span>
                  <span v-if="isSSERunning && aiMessageChars.length > 0" class="typing-cursor">
                    |
                  </span>
                </div>
              </div>
            </div>
            <div class="result-section" v-if="analysisData.complete">
              <div class="completion-notice">
                <el-icon><CircleCheck /></el-icon>
                <span>{{ analysisData.complete.message }}</span>
                <span class="completion-time">{{ analysisData.complete.timestamp }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, onUnmounted, nextTick } from "vue";
import { ElMessage } from "element-plus";
import { CircleCheck, Download, Document } from "@element-plus/icons-vue";
import { applicationStore } from "@/store/modules/application";
import { serviceTimeStore } from "@/store/modules/global";
import { useRoute, useRouter } from "vue-router";
import BaseEcharts from "@/components/baseEcharts/index.vue";
import { getChartOptions } from "@/components/baseEcharts/chartsOptions";
import { formatTime as formatDateTime } from "@/utils/dateStr";

// 路由
const route = useRoute();
const router = useRouter();

// Store
const appStore = applicationStore();
const timeStore = serviceTimeStore();

// SSE相关状态
const eventSource = ref<EventSource | null>(null);
const isSSERunning = ref(false);
const sseMessages = ref<Array<{ timestamp: string; content: string; type?: string }>>([]);
const reconnectCount = ref(0);
const lastUpdateTime = ref("");
const isLoading = ref(false);

// 下载相关状态
const downloading = ref(false);

// 分析数据
const analysisData = ref<any>({});

// 参数
const appid = computed(() => appStore.appId);
const startTime = computed(() => {
  // 优先使用路由参数中的时间范围，如果没有则使用全局时间选择器
  return (route.query.startTime as string) || timeStore.serviceTimeData.start_time;
});
const endTime = computed(() => {
  // 优先使用路由参数中的时间范围，如果没有则使用全局时间选择器
  return (route.query.endTime as string) || timeStore.serviceTimeData.end_time;
});

// aiPrompt参数
const aiPrompt = computed(() => {
  return (route.query.aiPrompt as string) || "";
});

// 消息容器引用
const messagesContainer = ref<HTMLElement | null>(null);

// AI消息逐字显示
const aiMessageChars = computed(() => {
  if (!analysisData.value.aiSummary) return [];
  return analysisData.value.aiSummary.split("");
});

// 自动滚动到底部
// const scrollToBottom = () => {
//   nextTick(() => {
//     if (messagesContainer.value) {
//       const container = messagesContainer.value;
//       const scrollHeight = container.scrollHeight;
//       const clientHeight = container.clientHeight;
//       const maxScrollTop = scrollHeight - clientHeight;

//       // 平滑滚动到底部
//       container.scrollTo({
//         top: maxScrollTop,
//         behavior: "smooth"
//       });
//     }
//   });
// };

// 时间趋势图表配置
const timeTrendChartOptions = computed(() => {
  if (!analysisData.value.timeTrend || !analysisData.value.timeTrend.length) {
    return {};
  }

  const timeTrendData = analysisData.value.timeTrend;
  const timestamps = timeTrendData.map((item: any) => {
    return formatDateTime(item.timestamp);
  });

  // 获取所有日志级别
  const allLevels = new Set<string>();
  timeTrendData.forEach((item: any) => {
    if (item.levelCounts) {
      Object.keys(item.levelCounts).forEach(level => allLevels.add(level));
    }
  });

  const levels = Array.from(allLevels).sort();

  // 添加总日志数数据
  const totalLogsData = timeTrendData.map((item: any) => {
    return parseInt(item.totalCount || "0");
  });

  const seriesData = levels.map(level => {
    return timeTrendData.map((item: any) => {
      return parseInt(item.levelCounts?.[level] || "0");
    });
  });

  // 将总日志数添加到系列数据的最前面
  seriesData.unshift(totalLogsData);

  // 根据日志级别设置颜色
  const getLevelColor = (level: string) => {
    const colorMap: { [key: string]: string } = {
      ERROR: "#F56C6C", // 红色
      WARN: "#E6A23C", // 橙色
      INFO: "#67C23A", // 绿色
      DEBUG: "#409EFF", // 蓝色
      TRACE: "#909399" // 灰色
    };
    return colorMap[level] || "#909399";
  };

  const levelColors = levels.map(level => getLevelColor(level));
  const colors = ["#13C2C2", ...levelColors];

  return getChartOptions({
    typ: undefined,
    color: colors,
    titleType: "日志级别趋势",
    originalTimes: timestamps,
    seriesData: seriesData,
    names: ["TOTAL", ...levels],
    type: "bar",
    legend: {
      show: true,
      top: "10px",
      orient: "horizontal"
    },
    areaStyle: false,
    stack: true,
    titleShow: false,
    tooltip: {
      trigger: "axis",
      axisPointer: {
        lineStyle: {
          width: 1,
          color: "#008000"
        }
      },
      formatter: function (params: any) {
        const time = params[0].name;
        let tooltipContent = `<div style="margin-bottom: 8px; font-weight: 600; color: #333;">${time}</div>`;

        params.forEach((item: any) => {
          const { value, seriesName } = item;
          const seriesColor = item.color;
          const displayValue = value.toLocaleString();
          tooltipContent += `  
            <div style="display: flex; align-items: center; margin: 4px 0;">  
              <span style="display: inline-block; width: 8px; height: 8px; background-color: ${seriesColor}; border-radius: 50%; margin-right: 8px;"></span>  
              <span style="font-weight: 500;">${seriesName}：</span>
              <span style="margin-left: 4px;">${displayValue}</span>  
            </div>  
          `;
        });

        return tooltipContent;
      }
    }
  });
});

// SSE URL
const sseUrl = computed(() => {
  if (!appid.value || !startTime.value || !endTime.value) {
    return "缺少必要参数";
  }
  const encodedStartTime = encodeURIComponent(startTime.value);
  const encodedEndTime = encodeURIComponent(endTime.value);
  const encodedAiPrompt = aiPrompt.value ? `&aiPrompt=${encodeURIComponent(aiPrompt.value)}` : "";

  return `/api/logs/analysis/stream?appid=${appid.value}&startTime=${encodedStartTime}&endTime=${encodedEndTime}${encodedAiPrompt}`;
});

// 格式化时间
const formatTime = (date: Date) => {
  return date.toLocaleTimeString();
};

// 获取日志级别类型
const getLevelType = (level: string): "danger" | "warning" | "success" | "info" | "primary" => {
  const typeMap: Record<string, "danger" | "warning" | "success" | "info" | "primary"> = {
    ERROR: "danger",
    WARN: "warning",
    INFO: "success",
    DEBUG: "info",
    TRACE: "info"
  };
  return typeMap[level] || "info";
};

// 格式化消息内容
const formatMessage = (content: string) => {
  // 如果是JSON数据，尝试格式化
  try {
    const data = JSON.parse(content);
    if (data.step && data.message) {
      return `<strong>${data.message}</strong>`;
    }
  } catch {
    // 不是JSON，直接返回
  }
  return content;
};

// 添加消息
const addMessage = (content: string, type: string = "info") => {
  const message = {
    timestamp: formatTime(new Date()),
    content,
    type
  };
  sseMessages.value.push(message);
  lastUpdateTime.value = formatTime(new Date());

  // 限制消息数量
  if (sseMessages.value.length > 100) {
    sseMessages.value = sseMessages.value.slice(-50);
  }
};

// 移除进度消息
const removeProgressMessage = (keyword: string) => {
  const index = sseMessages.value.findIndex(message => message.content.includes(keyword));
  if (index !== -1) {
    sseMessages.value.splice(index, 1);
  }
};

// 处理分析数据
const handleAnalysisData = (eventType: string, data: any) => {
  switch (eventType) {
    case "progress":
      // 显示进度消息
      if (data.step && data.message) {
        addMessage(data.message, "info");
      }
      break;

    case "statistics":
      analysisData.value.statistics = data;
      // 清除"正在分析统计概览..."消息
      removeProgressMessage("正在分析统计概览");
      break;

    case "timeTrend":
      analysisData.value.timeTrend = data;
      // 清除"正在分析时间趋势..."消息
      removeProgressMessage("正在分析时间趋势");
      break;

    case "keywords":
      analysisData.value.keywords = data;
      // 清除"正在提取关键词..."消息
      removeProgressMessage("正在提取关键词");
      break;

    case "sources":
      analysisData.value.sources = data;
      // 清除"正在分析日志来源..."消息
      removeProgressMessage("正在分析日志来源");
      break;

    case "anomalies":
      analysisData.value.anomalies = data;
      // 清除"正在检测异常模式..."消息
      removeProgressMessage("正在检测异常模式");
      break;

    case "errorTop10":
      analysisData.value.errorTop10 = data;
      // 清除"正在分析ERROR日志Top10..."消息
      removeProgressMessage("正在分析ERROR日志Top10");
      break;

    case "aiSummary": {
      // 处理逐字返回的AI摘要消息
      if (analysisData.value.aiSummary === undefined) {
        analysisData.value.aiSummary = "";
      }
      // 处理不同的数据格式
      let newText = "";
      if (typeof data === "string") {
        newText = data;
      } else if (data.data) {
        newText = data.data;
      } else if (data.message) {
        newText = data.message;
      } else {
        newText = JSON.stringify(data);
      }
      analysisData.value.aiSummary += newText;

      // 自动滚动到底部
      // scrollToBottom();
      break;
    }

    case "complete":
      analysisData.value.complete = data;
      // 分析完成后清空所有剩余进度消息并关闭loading
      sseMessages.value = [];
      isLoading.value = false;
      break;

    case "error":
      analysisData.value.error = data;
      isLoading.value = false;
      break;
  }
};

// 启动SSE连接
const startSSE = () => {
  if (eventSource.value) {
    return;
  }

  if (!appid.value || !startTime.value || !endTime.value) {
    return;
  }

  try {
    isLoading.value = true;
    eventSource.value = new EventSource(sseUrl.value);
    isSSERunning.value = true;

    // 连接打开事件
    eventSource.value.onopen = () => {
      reconnectCount.value = 0;
    };

    // 通用消息处理函数
    const handleSSEMessage = (eventType: string, event: Event) => {
      const messageEvent = event as MessageEvent;

      try {
        let data;

        // aiSummary事件特殊处理，直接使用原始文本
        if (eventType === "aiSummary") {
          data = messageEvent.data;
        } else {
          // 其他事件尝试解析JSON，如果失败则作为纯文本处理
          try {
            data = JSON.parse(messageEvent.data);
          } catch {
            // 如果不是JSON，直接使用原始数据
            data = { data: messageEvent.data };
          }
        }
        handleAnalysisData(eventType, data);
      } catch (error) {
        console.error("处理SSE消息失败:", error);
      }
    };

    // 为每种事件类型添加监听器
    eventSource.value.addEventListener("progress", event => handleSSEMessage("progress", event));
    eventSource.value.addEventListener("statistics", event =>
      handleSSEMessage("statistics", event)
    );
    eventSource.value.addEventListener("timeTrend", event => handleSSEMessage("timeTrend", event));
    eventSource.value.addEventListener("keywords", event => handleSSEMessage("keywords", event));
    eventSource.value.addEventListener("sources", event => handleSSEMessage("sources", event));
    eventSource.value.addEventListener("anomalies", event => handleSSEMessage("anomalies", event));
    eventSource.value.addEventListener("errorTop10", event =>
      handleSSEMessage("errorTop10", event)
    );
    eventSource.value.addEventListener("aiSummary", event => handleSSEMessage("aiSummary", event));
    eventSource.value.addEventListener("complete", event => handleSSEMessage("complete", event));
    eventSource.value.addEventListener("error", event => handleSSEMessage("error", event));

    // 通用消息事件（备用）
    eventSource.value.onmessage = event => {
      // 静默处理
    };

    // 错误事件
    eventSource.value.onerror = error => {
      // 关闭连接
      if (eventSource.value) {
        eventSource.value.close();
        eventSource.value = null;
        isSSERunning.value = false;
      }
    };
  } catch (error) {
    // console.error("启动SSE失败:", error);
    ElMessage.error("启动SSE连接失败");
    addMessage(`启动SSE失败: ${error}`, "error");
  }
};

// 跳转到日志看台页面
const goToLogOverview = () => {
  router.push({ path: "/logMonitoring/logOriginal" });
};

// 下载PDF
const downloadPDF = async () => {
  if (!analysisData.value.statistics) {
    ElMessage.warning("暂无分析数据可下载");
    return;
  }

  try {
    downloading.value = true;

    // 使用html2canvas和jsPDF生成PDF
    const html2canvas = (await import("html2canvas")).default;
    const jsPDF = (await import("jspdf")).default;

    // 获取要导出的内容区域
    const element = document.querySelector(".result-card") as HTMLElement;
    if (!element) {
      throw new Error("未找到要导出的内容");
    }

    // 生成canvas
    const canvas = await html2canvas(element, {
      scale: 2,
      useCORS: true,
      allowTaint: true,
      backgroundColor: "#ffffff"
    });

    // 创建PDF
    const pdf = new jsPDF("p", "mm", "a4");
    const imgData = canvas.toDataURL("image/png");
    const imgWidth = 210; // A4宽度
    const pageHeight = 295; // A4高度
    const imgHeight = (canvas.height * imgWidth) / canvas.width;
    let heightLeft = imgHeight;

    let position = 0;

    // 添加第一页
    pdf.addImage(imgData, "PNG", 0, position, imgWidth, imgHeight);
    heightLeft -= pageHeight;

    // 如果内容超过一页，添加新页面
    while (heightLeft >= 0) {
      position = heightLeft - imgHeight;
      pdf.addPage();
      pdf.addImage(imgData, "PNG", 0, position, imgWidth, imgHeight);
      heightLeft -= pageHeight;
    }

    // 下载PDF
    const fileName = `日志分析报告_${new Date().toISOString().slice(0, 10)}.pdf`;
    pdf.save(fileName);

    ElMessage.success("PDF下载成功");
  } catch (error) {
    console.error("下载PDF失败:", error);
    ElMessage.error("下载PDF失败，请重试");
  } finally {
    downloading.value = false;
  }
};

// 关闭SSE连接
const closeSSE = () => {
  if (eventSource.value) {
    eventSource.value.close();
    eventSource.value = null;
    isSSERunning.value = false;
  }
};

// 组件挂载时检查是否需要自动启动SSE
onMounted(() => {
  const shouldStartSSE = route.query.startSSE;
  if (shouldStartSSE === "true") {
    startSSE();
  }
});

// 组件卸载时关闭SSE连接
onUnmounted(() => {
  closeSSE();
});
</script>

<style lang="scss" scoped>
.ai-chat-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  position: relative;
}

.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
}

.header {
  padding: 16px 24px;
  background: white;
  border-bottom: 1px solid #e0e0e0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;

    h2 {
      margin: 0;
      font-size: 20px;
      font-weight: 600;
      color: #333;
    }
  }
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 16px;

  .empty-state {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    min-height: 400px;

    .empty-content {
      text-align: center;
      max-width: 400px;

      .empty-icon {
        font-size: 64px;
        margin-bottom: 16px;
      }

      h3 {
        margin: 0 0 12px 0;
        font-size: 20px;
        font-weight: 600;
        color: #333;
      }

      p {
        margin: 0 0 12px 0;
        font-size: 15px;
        color: #333;
        // line-height: 1.5;
        white-space: nowrap;
      }
    }
  }

  .message {
    display: flex;
    gap: 12px;
    max-width: 100%;

    &.ai-message {
      .message-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: #409eff;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
        flex-shrink: 0;
      }

      .message-content {
        flex: 1;
        // max-width: calc(100% - 44px);
      }
    }

    &.error-message {
      .message-avatar {
        background: #f56c6c;
      }
    }

    .message-text {
      background: white;
      padding: 12px 16px;
      border-radius: 12px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      line-height: 1.5;
      color: #333;
      word-break: break-word;
    }

    .message-time {
      font-size: 12px;
      color: #999;
      margin-top: 4px;
      margin-left: 16px;
    }

    .loading-message {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 16px;
      background: #f8f9fa;
      border-radius: 8px;

      .loading-spinner {
        width: 20px;
        height: 20px;
        border: 2px solid #e3e3e3;
        border-top: 2px solid #409eff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      .loading-text {
        color: #666;
        font-size: 14px;
      }
    }

    @keyframes spin {
      0% {
        transform: rotate(0deg);
      }
      100% {
        transform: rotate(360deg);
      }
    }
  }
}

.result-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  h3 {
    margin: 0 0 20px 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
  }

  .result-section {
    margin-bottom: 24px;

    h4 {
      margin: 0 0 12px 0;
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }

    h5 {
      margin: 0 0 8px 0;
      font-size: 14px;
      font-weight: 500;
      color: #666;
    }
  }

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 16px;
    margin-bottom: 20px;

    .stat-item {
      text-align: center;
      padding: 16px;
      background: #f8f9fa;
      border-radius: 8px;

      .stat-value {
        font-size: 24px;
        font-weight: bold;
        color: #409eff;
        margin-bottom: 4px;
      }

      .stat-label {
        font-size: 15px;
        color: #6d6c6c;
      }
    }
  }

  .level-distribution {
    .level-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      .level-tag {
        margin: 0;
      }
    }
  }

  .trend-chart {
    margin-top: 12px;
    margin-bottom: 0;
    background: white;
    border-radius: 8px;
    padding: 6px;
    // box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .keywords-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 12px;

    .keyword-card {
      display: flex;
      flex-direction: column;
      padding: 12px;
      background: #f8f9fa;
      border-radius: 8px;
      min-height: 80px;

      .keyword-header {
        display: flex;
        flex-direction: column;
        gap: 4px;
        margin-bottom: 8px;

        .keyword-text {
          font-weight: 500;
          color: #333;
          font-size: 18px;
          word-break: break-word;
        }

        .keyword-freq {
          color: #666;
          font-size: 15px;
        }
      }

      .keyword-levels {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;
        margin-top: auto;
      }
    }
  }

  .error-top10-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 12px;

    .error-card {
      display: flex;
      flex-direction: column;
      padding: 12px;
      background: #f8f9fa;
      border-radius: 8px;
      min-height: 100px;

      .error-header {
        display: flex;
        flex-direction: column;
        gap: 4px;
        margin-bottom: 8px;

        .error-summary {
          font-weight: 500;
          color: #333;
          font-size: 14px;
          word-break: break-word;
          line-height: 1.4;
        }

        .severity-tag {
          align-self: flex-start;
        }
      }

      .error-stats {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .error-count {
          font-weight: bold;
          color: #409eff;
          font-size: 16px;
        }

        .error-rate {
          color: #f56c6c;
          font-weight: 500;
          font-size: 14px;
        }
      }

      .error-time {
        display: flex;
        flex-direction: column;
        gap: 2px;
        margin-top: auto;

        .time-item {
          display: flex;
          justify-content: space-between;
          font-size: 12px;

          .time-label {
            color: #666;
          }

          .time-value {
            color: #333;
            font-weight: 500;
          }
        }
      }
    }
  }

  .source-analysis {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;

    .source-section {
      .source-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #eee;

        &:last-child {
          border-bottom: none;
        }

        .source-name {
          font-weight: 500;
          color: #333;
        }

        .source-count {
          color: #409eff;
          font-weight: bold;
        }

        .source-percentage {
          color: #999;
          font-size: 12px;
        }
      }
    }
  }

  .anomalies-list {
    display: flex;
    flex-direction: column;
    gap: 12px;

    .anomaly-item {
      padding: 16px;
      background: #fff3cd;
      border: 1px solid #ffeaa7;
      border-radius: 8px;

      .anomaly-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 8px;

        .anomaly-severity {
          font-size: 12px;
          color: #666;
        }
      }

      .anomaly-description {
        font-weight: 500;
        color: #333;
        margin-bottom: 4px;
      }

      .anomaly-recommendation {
        font-size: 14px;
        color: #666;
      }
    }
  }

  .ai-summary {
    .ai-message-content {
      background: #f8f9fa;
      padding: 8px;
      border-radius: 8px;
      margin: 0;
      font-family: "Courier New", monospace;
      font-size: 14px;
      // line-height: 1.6;
      white-space: pre-wrap;
      word-break: break-word;
      min-height: 60px;
    }

    .ai-char {
      display: inline;
      animation: fadeIn 0.05s ease-out;

      &.typing {
        animation: typing 0.1s ease-out;
      }
    }

    .ai-char.newline {
      display: block;
      height: 1.6em;
    }

    .typing-cursor {
      display: inline-block;
      animation: blink 1s infinite;
      color: #409eff;
      font-weight: bold;
    }

    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: translateY(1px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    @keyframes typing {
      0% {
        opacity: 0;
        transform: translateY(2px);
      }
      100% {
        opacity: 1;
        transform: translateY(0);
      }
    }

    @keyframes blink {
      0%,
      50% {
        opacity: 1;
      }
      51%,
      100% {
        opacity: 0;
      }
    }
  }

  .completion-notice {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    background: #f0f9ff;
    border: 1px solid #bae6fd;
    border-radius: 8px;
    color: #0369a1;

    .completion-time {
      margin-left: auto;
      font-size: 12px;
      color: #666;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .header {
    padding: 12px 16px;
  }

  .chat-messages {
    padding: 16px;
  }

  .result-card {
    padding: 16px;

    .stats-grid {
      grid-template-columns: 1fr;
    }

    .source-analysis {
      grid-template-columns: 1fr;
    }

    .keywords-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: 8px;

      .keyword-card {
        min-height: 70px;
        padding: 8px;

        .keyword-header {
          .keyword-text {
            font-size: 12px;
          }

          .keyword-freq {
            font-size: 10px;
          }
        }

        .keyword-levels {
          gap: 2px;
        }
      }
    }

    .error-top10-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: 8px;

      .error-card {
        min-height: 90px;
        padding: 8px;

        .error-header {
          .error-summary {
            font-size: 12px;
          }
        }

        .error-stats {
          .error-count {
            font-size: 14px;
          }

          .error-rate {
            font-size: 12px;
          }
        }

        .error-time {
          .time-item {
            font-size: 10px;
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .result-card {
    .keywords-grid {
      grid-template-columns: 1fr;
      gap: 6px;

      .keyword-card {
        min-height: 60px;
        padding: 6px;

        .keyword-header {
          .keyword-text {
            font-size: 11px;
          }

          .keyword-freq {
            font-size: 9px;
          }
        }
      }
    }

    .error-top10-grid {
      grid-template-columns: 1fr;
      gap: 6px;

      .error-card {
        min-height: 80px;
        padding: 6px;

        .error-header {
          .error-summary {
            font-size: 11px;
          }
        }

        .error-stats {
          .error-count {
            font-size: 12px;
          }

          .error-rate {
            font-size: 10px;
          }
        }

        .error-time {
          .time-item {
            font-size: 9px;
          }
        }
      }
    }
  }
}
</style>
