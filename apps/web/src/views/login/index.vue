<template>
  <div class="login">
    <div class="login-left"></div>
    <el-card>
      <div class="login-title flex">
        <div class="mr-5 w-6 h-6">
          <img src="../../image/logo.png" alt="" />
        </div>
        <div class="login-title-desc">运维可观测平台</div>
      </div>
      <!-- :rules="loginRules"  -->
      <el-form ref="loginFormRef" :model="loginData" class="login-form" :rules="loginRules">
        <el-form-item prop="mobile" class="mt-2">
          <span>
            <svg-icon name="users" />
          </span>
          <el-input v-model="loginData.mobile" size="large" :placeholder="'账号'" name="mobile" />
        </el-form-item>

        <el-tooltip content="查看密码" placement="right" class="mt-10">
          <el-form-item prop="password" class="mt-8">
            <span>
              <svg-icon name="password" />
            </span>
            <el-input
              v-model="loginData.password"
              :placeholder="'密码'"
              :type="passwordVisible === false ? 'password' : 'input'"
              size="large"
              name="password"
              class="mr-6px"
              @keyup.enter="handleLogin"
            />
            <span @click="passwordVisible = !passwordVisible">
              <svg-icon :name="passwordVisible === false ? 'eye' : 'eye-open'" />
            </span>
          </el-form-item>
        </el-tooltip>

        <el-form-item prop="code" class="mt-8">
          <span>
            <svg-icon name="shield" />
          </span>
          <el-input
            v-model="loginData.code"
            :placeholder="'验证码'"
            size="large"
            name="code"
            maxlength="4"
            @keyup.enter="handleLogin"
          />
          <div class="captcha">
            <img
              v-if="captchaImage"
              :src="captchaImage"
              alt="验证码"
              class="captcha-img"
              @click="refreshCaptcha"
            />
            <div v-else class="image-slot" @click="refreshCaptcha">
              <svg-icon name="refresh" />
              点击刷新
            </div>
          </div>
        </el-form-item>

        <el-button
          :loading="loading"
          type="primary"
          round
          class="login-button mt-8"
          @click.prevent="handleLogin"
          >立 即 登 录
        </el-button>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ILoginRequestData } from "@/api/login/type";
import { userLogin, getCaptcha } from "@/api/login/index";
import { useRouter } from "vue-router";
import { applicationStore } from "@/store/modules/application";
import { getUserApps } from "@/api/application/index";
import { serviceTimeStore } from "@/store/modules/global";
import { userInfoStore } from "@/store/modules/user";
import { getResourceMenu } from "@/api/system/resources";
import { sm4, sm2 } from "sm-crypto";
import { v4 as uuidv4 } from "uuid";
const useUserInfoStore = userInfoStore();
const serTimeStore = serviceTimeStore();
const router = useRouter();
const passwordVisible = ref(false); // 密码是否可见
const loginFormRef = ref(ElForm); // 登录表单ref
const loading = ref(false); // 按钮loading
const useApplicationStore = applicationStore();
const appid = ref("");
const captchaImage = ref(""); // 验证码图片
const captchaId = ref(""); // 验证码ID
const captchaTimer = ref<number | null>(null); // 验证码定时器

const loginData = ref<ILoginRequestData>({
  aesKey: "",
  mobile: "13927452867",
  password: "Fc123abc",
  code: "",
  captchaId: ""
});

const loginRules = computed(() => {
  return {
    mobile: [{ required: true, trigger: "blur", message: `请输入账号` }],
    password: [{ required: true, trigger: "blur", message: "请输入密码" }],
    code: [
      { required: true, trigger: "blur", message: "请输入验证码" },
      {
        validator: (rule: any, value: string, callback: any) => {
          if (value && value.length !== 4) {
            callback(new Error("验证码必须是4位"));
          } else {
            callback();
          }
        },
        trigger: "blur"
      }
    ]
  };
});

/**
 * 获取验证码
 */
const fetchCaptcha = async () => {
  try {
    const res = await getCaptcha();
    if (res.code === 0 && res.entity) {
      captchaImage.value = res.entity.imageData;
      captchaId.value = res.entity.captchaId;
      loginData.value.captchaId = res.entity.captchaId;
    }
  } catch (error) {
    console.error("获取验证码失败", error);
  }
};

/**
 * 启动验证码定时器
 */
const startCaptchaTimer = () => {
  // 清除之前的定时器
  if (captchaTimer.value) {
    clearInterval(captchaTimer.value);
  }

  // 设置新的定时器，每3分钟（180000毫秒）刷新一次验证码
  captchaTimer.value = setInterval(() => {
    fetchCaptcha();
  }, 180000);
};

/**
 * 清除验证码定时器
 */
const clearCaptchaTimer = () => {
  if (captchaTimer.value) {
    clearInterval(captchaTimer.value);
    captchaTimer.value = null;
  }
};

/**
 * 刷新验证码
 */
const refreshCaptcha = () => {
  fetchCaptcha();
  // 刷新验证码后重新启动定时器
  startCaptchaTimer();
};

/**
 * 登录
 */
const handleLogin = async () => {
  // 表单验证
  const valid = await loginFormRef.value?.validate();
  if (!valid) return;

  if (
    loginData.value.mobile === "" ||
    loginData.value.password === "" ||
    loginData.value.code === ""
  )
    return;

  // 生成 16 字节的 SM4 密钥
  const sm4Key = uuidv4().replace(/-/g, "");
  // 用 SM2 加密 SM4 密钥
  const sm2PublicKey =
    "04D969AAF5ECCFFFC381674E6C3E46A0F81B761F162A6EED0795136F0200C7C750273BBC393BADC723685F81E1137D5BCC270B7341D01574E6C4943BD56AF11E55";
  const desPhone = sm4.encrypt(loginData.value.mobile.toString(), sm4Key);
  const desPassword = sm4.encrypt(loginData.value.password.toString(), sm4Key);
  const desUuid = sm2.doEncrypt(sm4Key, sm2PublicKey, 1);
  loading.value = true;
  try {
    const res = await userLogin({
      mobile: desPhone,
      password: desPassword,
      code: loginData.value.code,
      captchaId: loginData.value.captchaId,
      aesKey: desUuid
    });
    if (res?.code == -1) {
      ElMessage.error(res.desc || "登录失败，请稍后再试");
      refreshCaptcha();
      loading.value = false;
      return;
    }
    useUserInfoStore.setUserInfo(res || {});
    await getUserAppsData();
    handleTimeDate();
    await fetchmenuList();
    router.push({ path: "/Topology" });
  } catch (err) {
    console.error("登录流程异常", err);
    // 登录失败时刷新验证码
    refreshCaptcha();
    loading.value = false;
  } finally {
    // loading.value = false;
  }
};

const formatDateTime = (date: Date) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

function handleTimeDate() {
  const now = new Date();
  const fiveMinutesAgo = new Date(now.getTime() - 5 * 60 * 1000); // 最近的五分钟
  const selectedRange = [formatDateTime(fiveMinutesAgo), formatDateTime(now)];
  serTimeStore.setServiceTIme(selectedRange, 0); // 设置全局的服务器时间
}
const appName = ref("");
const organId = ref("");
async function getUserAppsData() {
  const params = {
    page: 1,
    rows: 10
  };
  await getUserApps(params)
    .then(res => {
      appid.value = appid.value ? appid.value : res.records?.[0]?.appid || "";
      organId.value = organId.value ? organId.value : res.records?.[0]?.organId || "";
      appName.value = res.records?.[0]?.name || "";
      useApplicationStore.setAppId(appid.value);
      useApplicationStore.setAppName(appName.value);
      useApplicationStore.setOrganId(organId.value);
    })
    .catch(err => {
      console.log(err);
      // router.push({ path: "/Topology" });
    });
}

const menuTypeData = ref([]);

const fetchmenuList = async () => {
  const params = {
    page: 1,
    rows: 10,
    status: true
  };

  try {
    const res = await getResourceMenu(params);
    if (res.code === 0) {
      menuTypeData.value = res.records.map((item: any) => {
        const routeName = JSON.parse(item.extJson);
        const childrenRoutes =
          item.children?.map((arr: any) => ({
            name: JSON.parse(arr.extJson),
            path: arr.path,
            component: arr.component,
            meta: {
              icon: arr.icon,
              iconType: "el",
              title: arr.title
            }
          })) || [];

        return {
          name: routeName,
          path: item.path,
          component: item.component,
          meta: {
            icon: item.icon,
            iconType: "el",
            title: item.title
          },
          children: childrenRoutes
        };
      });

      localStorage.setItem("cachedMenu", JSON.stringify(menuTypeData.value));
    }
  } catch (error) {
    console.error("菜单加载失败", error);
  }
};

let removeAfterEach: any;

onMounted(() => {
  sessionStorage.clear();
  fetchCaptcha(); // 组件挂载时获取验证码
  startCaptchaTimer(); // 启动验证码定时器

  // 监听路由跳转完成后关闭 loading
  removeAfterEach = router.afterEach(() => {
    setTimeout(() => {
      loading.value = false;
    }, 100);
  });
});

onBeforeUnmount(() => {
  removeAfterEach?.();
  clearCaptchaTimer(); // 清除验证码定时器
});
</script>

<style lang="scss" scoped>
.login {
  height: 100%;
  width: 100%;
  background: url("@/image/syg.png") no-repeat 100% 100% / cover;
  color: var(--el-fill-color-light);
  position: relative;

  &-left {
    position: absolute;
    left: 12%;
    top: 18%;
    width: 514px;
    height: 584px;
    background: url("@/image/bg.png") no-repeat 100% 100% / cover;
  }

  &-title {
    text-align: center;
    justify-content: center;
    align-items: center;

    &-desc {
      text-align: center;
      font-weight: bold;
      font-size: 30px;
      color: #020217;
    }
  }

  &-button {
    width: 100% !important;
    padding: 20px !important;
  }

  &-form {
    padding: 30px 10px;
    width: 320px;

    .captcha {
      position: relative;
      margin-left: 10px;
      flex-shrink: 0;
      display: flex;
      align-items: center;
      justify-content: center;

      &-img {
        width: 100px;
        // height: 40px;
        cursor: pointer;
        border-radius: 4px;
        // border: 1px solid var(--el-border-color);
      }

      .image-slot {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100px;
        height: 40px;
        font-size: 12px;
        // border: 1px solid var(--el-border-color);
        border-radius: 4px;
        background-color: var(--el-fill-color-light);

        svg {
          margin-right: 5px;
        }
      }
    }
  }
}

.el-card {
  position: absolute;
  right: 12%;
  top: 25%;
  border-radius: 10px;

  :deep(.el-card__body) {
    padding: 70px 60px 30px 60px;
  }
}

:deep(.el-input) {
  flex: 1;
  margin-left: 10px;
  min-width: 0; // 防止flex子元素溢出

  .el-input__wrapper {
    padding: 0;
    background-color: transparent;
    box-shadow: none;
    height: 40px; // 确保所有输入框高度一致

    &.is-focus,
    &:hover {
      box-shadow: none !important;
    }

    input:-webkit-autofill {
      transition: background-color 1000s ease-in-out 0s;
      /* 通过延时渲染背景色变相去除背景颜色 */
    }
  }
}

:deep(.el-form-item__error) {
  padding-top: 14px;
}

.el-form-item {
  background: var(--el-input-bg-color);
  border: 1px solid var(--el-border-color);
  border-radius: 5px;
  padding: 4px 10px;

  &__content {
    display: flex;
    align-items: center;
  }
}
</style>
