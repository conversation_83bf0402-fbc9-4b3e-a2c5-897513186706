<template>
  <div class="flex justify-between">
    <div class="w-50%">
      <BaseEcharts
        :options="statProvinceOptions"
        height="730px"
        v-loading="statProvinceOptionsLoading"
      ></BaseEcharts>
    </div>
    <div class="w-22%">
      <MyTable
        :data="tableData"
        :total="tableData.length"
        pagination-layout=""
        style="width: 100%"
        v-loading="tableLoading"
      >
        <my-column
          type="index"
          property="versionCount"
          label="排名"
          align="center"
          header-align="center"
          width="120"
          :show-overflow-tooltip="false"
        />
        <my-column property="province" align="center" header-align="center" label="省份" />
        <my-column
          property="count"
          align="center"
          header-align="center"
          label="用户数"
          width="120"
          :show-overflow-tooltip="false"
        >
          <template v-slot="{ row }">
            <span>
              {{ formatNums(row.count).fixValue }}{{ formatNums(row.count).unit || "" }}
            </span>
          </template>
        </my-column>
      </MyTable>
    </div>
    <div class="w-27% h-743px">
      <BaseEcharts
        :options="modelTop5Data"
        height="280px"
        class="mb-10px"
        v-loading="modelTop5Loading"
      />
      <Ranking
        v-loading="osTop5Loading"
        class="w-100% mb-10px h-240px"
        :title="'操作系统（Top5）'"
        :rankingList="osTop5RankingList"
      ></Ranking>
      <Ranking
        v-loading="libVersionTop5Loading"
        class="w-100% mb-10px h-240px"
        :title="'微信基础库版本（Top5）'"
        :rankingList="libVersionTop5RankingList"
      ></Ranking>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getModelTop5, getOsTop5, getLibVersionTop5, getStatProvince } from "@/api/mini/feature";
import * as echarts from "echarts";
import chaina from "@/components/china/china.json";
import { formatNums } from "@/utils/formatStr";
import { applicationStore } from "@/store/modules/application";
import { breadcrumbStore } from "@/store/modules/breadcurmb";
const useApplicationStore = applicationStore();
const usebreadcrumbStore = breadcrumbStore();
interface IRankItem {
  name: string; // 标题
  proportion: number; // 占比
  totalScore: number; // 总数
  color?: string; // 颜色
  unit?: string;
}
const tableLoading = ref(false);
const statProvinceOptionsLoading = ref(false); //用户省份loading
const modelTop5Loading = ref(false); //设备型号Top5loading
const osTop5RankingList = ref<IRankItem[]>([]); //操作系统Top5数据
const osTop5Loading = ref(false); //操作系统Top5loading
const libVersionTop5RankingList = ref<IRankItem[]>([]); //微信基础库版本Top5数据
const libVersionTop5Loading = ref(false); //微信基础库版本loading
function handleOsTop5Data() {
  let total = 0;
  osTop5Loading.value = true;
  getOsTop5({ appid: useApplicationStore.appId, mpAppid: usebreadcrumbStore.appOption })
    .then(res => {
      if (res.code === 0 && res.records) {
        res.records.forEach((item: { count: number }) => {
          total += item.count;
        });
        res.records.map((item: { os: string; count: number }) => {
          osTop5RankingList.value.push({
            name: item.os,
            proportion: (item.count / total) * 100,
            totalScore: item.count,
            color: "#445fde",
            unit: "人"
          });
        });
      }
      osTop5Loading.value = false;
    })
    .catch(err => {
      console.log(err);
      osTop5Loading.value = false;
    });
}
//获取微信基础库版本数据
function handeLibVersionTop5Data() {
  let total = 0;
  libVersionTop5Loading.value = true;
  getLibVersionTop5({ appid: useApplicationStore.appId, mpAppid: usebreadcrumbStore.appOption })
    .then(res => {
      if (res.code === 0 && res.records) {
        res.records.forEach((item: { count: number }) => {
          total += item.count;
        });
        res.records.map((item: { libVersion: string; count: number }) => {
          libVersionTop5RankingList.value.push({
            name: item.libVersion,
            proportion: (item.count / total) * 100,
            totalScore: item.count,
            color: "#445fde",
            unit: "人"
          });
        });
      }
      libVersionTop5Loading.value = false;
    })
    .catch(err => {
      console.log(err);
      libVersionTop5Loading.value = false;
    });
}
//图表通用配置
const setting = {
  title: {
    x: "10px",
    y: "1px",
    textStyle: {
      fontSize: 16,
      fontWeight: "400",
      color: "#000000"
    }
  },
  graphic: [
    {
      type: "rect",
      shape: { x: 0, y: 5, width: 4, height: 15 },
      style: { fill: "#3375f9" },
      z: 100
    }
  ],
  grid: {
    left: "1px",
    right: "1%",
    bottom: "1px",
    top: "60px",
    containLabel: true
  }
};
echarts.registerMap("china", chaina as any);
//用户省份分布
const statProvinceOptions = ref({
  ...setting,
  title: {
    ...setting.title,
    text: "用户省份分布"
  },
  tooltip: {
    trigger: "item"
  },
  visualMap: {
    left: "right",
    top: "bottom",
    min: 0,
    max: 0,
    inRange: {
      color: ["#f2e2a0", "#d88273", "#bf444c"]
    },
    text: ["High", "Low"],
    calculable: true
  },
  series: [
    {
      name: "用户省份分布",
      type: "map",
      map: "china",
      roam: false,
      emphasis: {
        label: {
          show: true
        }
      },
      data: []
    }
  ]
});
//获取用户省份分布数据
function statProvince() {
  statProvinceOptionsLoading.value = true;
  tableLoading.value = true;
}
getStatProvince({
  appid: useApplicationStore.appId,
  mpAppid: usebreadcrumbStore.appOption
})
  .then(res => {
    if (res.code === 0) {
      tableData.value = res.records;
      tableLoading.value = false;
      const processedRecords = res.records.map((record: { province: string; count: number }) => ({
        name: record.province.replace(/省$/, ""),
        value: record.count
      }));

      if (processedRecords.length > 0) {
        let maxValue = Math.max(...processedRecords.map((record: { value: any }) => record.value));
        statProvinceOptions.value = {
          ...statProvinceOptions.value,
          series: [
            {
              ...statProvinceOptions.value.series[0],
              data: processedRecords
            }
          ],
          visualMap: {
            ...statProvinceOptions.value.visualMap,
            max: maxValue
          }
        };
      } else {
        statProvinceOptions.value = {
          ...statProvinceOptions.value,
          series: [
            {
              ...statProvinceOptions.value.series[0],
              data: processedRecords
            }
          ],
          visualMap: {
            ...statProvinceOptions.value.visualMap,
            max: 1
          }
        };
      }
    }
    statProvinceOptionsLoading.value = false;
  })
  .catch(err => {
    console.log(err);
    statProvinceOptionsLoading.value = false;
    tableLoading.value = false;
  });
//获取设备型号Top5数据
const modelTop5Data = ref({
  ...setting,
  title: {
    ...setting.title,
    text: "设备型号（Top5）"
  },
  tooltip: {
    trigger: "item"
  },
  legend: {
    bottom: "1%",
    left: "center",
    type: "scroll"
  },
  series: [
    {
      type: "pie",
      radius: ["40%", "65%"],
      avoidLabelOverlap: false,
      label: {
        show: false,
        position: "center"
      },
      emphasis: {
        label: {
          show: true,
          fontSize: 15
        }
      },
      labelLine: {
        show: false
      },
      data: []
    }
  ]
});
const handeModelTop5Data = async () => {
  modelTop5Loading.value = true;
  const queue = {
    appid: useApplicationStore.appId,
    mpAppid: usebreadcrumbStore.appOption
  };
  try {
    const res = await getModelTop5(queue);
    if (res.code === 0 && res.records) {
      const processedRecords = res.records.map((item: { count: number; model: string }) => ({
        value: item.count,
        name: item.model
      }));
      modelTop5Data.value = {
        ...modelTop5Data.value,
        series: [
          {
            ...modelTop5Data.value.series[0],
            data: processedRecords
          }
        ]
      };
    }
    modelTop5Loading.value = false;
  } catch (error) {
    modelTop5Loading.value = false;
    console.error(error);
  }
};
onMounted(() => {
  statProvince();
  handleOsTop5Data();
  handeLibVersionTop5Data();
  handeModelTop5Data();
});
const tableData = ref([]);
</script>

<style lang="scss" scoped></style>
