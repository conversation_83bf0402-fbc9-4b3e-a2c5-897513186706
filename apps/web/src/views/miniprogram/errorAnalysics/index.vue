<template>
  <div v-loading="loading">
    <div class="flex justify-between indicator-wrapper">
      <Indicator :value="errorCountData.value" :unit="'错误次数'" :color="'#f56c6c'"></Indicator>
      <Indicator :value="userCountData.value" :unit="'影响用户数'" :color="'#f56c6c'"></Indicator>
      <Indicator
        :value="versionCountData.value"
        :unit="'影响版本数'"
        :color="'#f56c6c'"
      ></Indicator>
    </div>
  </div>
  <div class="flex justify-between indicator-wrapper mt-10px mb-10px">
    <BaseEcharts :options="stutterCountChartData" height="250px" v-loading="stutterCountLoading" />
    <BaseEcharts :options="miniVersionTop5Data" height="250px" v-loading="miniVersionTop5Loading" />
    <BaseEcharts :options="miniDeviceTop5Data" height="250px" v-loading="miniDeviceTop5Loading" />
  </div>
  <div class="log-container mt-10px">
    <div class="search-container">
      <el-form>
        <el-form-item class="input-group">
          <label for="traceId">用户ID：</label>
          <el-input id="traceId" v-model="pageParams.userId" clearable placeholder="请输入用户ID" />
        </el-form-item>
        <el-form-item class="input-group">
          <label for="model">设备型号：</label>
          <el-select placeholder="请选择设备型号" v-model="pageParams.model" clearable filterable>
            <el-option v-for="model in models" :key="model" :label="model" :value="model" />
          </el-select>
        </el-form-item>
        <el-form-item class="input-group">
          <label for="miniVersion">小程序版本：</label>
          <el-select
            placeholder="请选择小程序版本"
            v-model="pageParams.miniVersion"
            clearable
            filterable
          >
            <el-option
              v-for="miniVersion in miniVersions"
              :key="miniVersion"
              :label="miniVersion"
              :value="miniVersion"
            />
          </el-select>
        </el-form-item>
        <el-form-item class="input-group">
          <label for="networkType">网络类型：</label>
          <el-select
            placeholder="请选择网络类型"
            v-model="pageParams.networkType"
            clearable
            filterable
          >
            <el-option
              v-for="networkType in networkTypes"
              :key="networkType"
              :label="networkType"
              :value="networkType"
            />
          </el-select>
        </el-form-item>
        <el-form-item style="display: flex; margin-top: 20px">
          <el-popconfirm
            @confirm="resetSearch"
            title="确定清空吗？"
            confirm-button-text="确定"
            cancel-button-text="取消"
            icon="el-icon-warning"
            :hide-after="0"
          >
            <template #reference>
              <el-button type="danger" plain style="flex: 1; margin-right: 10px"> 清空 </el-button>
            </template>
          </el-popconfirm>
          <el-button type="primary" :icon="Search" style="flex: 1" @click="search">
            查询
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="tabel-container">
      <MyTable
        :data="list.records"
        :total="list.total"
        v-loading="tableLoading"
        style="width: 100%"
        @sizeChange="handleSizeChange"
        @currentChange="handleCurrentChange"
      >
        <my-column property="message" label="错误摘要">
          <template #default="scope">
            <span class="service-name" @click="shouDetail(scope.row)">
              {{ scope.row.message }}
            </span>
          </template>
        </my-column>
        <my-column property="name" label="错误类型">
          <template #default="{ row }">
            <el-tag :type="'danger'">{{ row.name }} </el-tag>
          </template>
        </my-column>
        <my-column property="os" label="操作系统" />
        <my-column property="networkType" label="网络类型" />
        <my-column property="model" label="设备型号" />
        <my-column property="userId" label="用户ID" />
        <my-column property="time" label="时间" />
      </MyTable>
    </div>
  </div>
  <div>
    <el-drawer v-model="logVisible" :title="title" size="50%">
      <div v-loading="drawerLoading">
        <TitlecCom :title="collapseDetailtitle"></TitlecCom>
        <div class="attr-panel">
          <div class="tab-content">
            <table class="detail-table">
              <tbody>
                <tr>
                  <td class="label">发生时间</td>
                  <td width="35%">{{ detailList.timestamp }}</td>
                  <td class="label">错误页面</td>
                  <td width="35%">{{ detailList.url }}</td>
                </tr>
                <tr>
                  <td class="label">错误类型</td>
                  <td width="35%">{{ detailList.name }}</td>
                  <td class="label">操作系统</td>
                  <td width="35%">{{ detailList.osName }}{{ detailList.osVersion }}</td>
                </tr>
                <tr>
                  <td class="label">网络类型</td>
                  <td width="35%">{{ detailList.networkType }}</td>
                  <td class="label">设备型号</td>
                  <td width="35%">{{ detailList.model }}</td>
                </tr>
                <tr>
                  <td class="label">用户ID</td>
                  <td width="35%">{{ detailList.userId }}</td>
                  <td class="label">IP地址</td>
                  <td width="35%">{{ detailList.ip }}</td>
                </tr>
                <tr>
                  <td class="label">国家</td>
                  <td width="35%">{{ detailList.country }}</td>
                  <td class="label">省份</td>
                  <td width="35%">{{ detailList.province }}</td>
                </tr>
                <tr>
                  <!-- <td class="label">城市</td>
                <td width="35%">{{ detailList.city }}</td> -->
                  <td class="label">设备品牌</td>
                  <td width="35%">{{ detailList.brand }}</td>
                  <td class="label">像素比率</td>
                  <td width="35%">{{ detailList.pixelRatio }}</td>
                </tr>
                <tr>
                  <td class="label">屏幕宽高</td>
                  <td width="35%">{{ detailList.screenArea }}</td>
                  <td class="label">语言</td>
                  <td width="35%">{{ detailList.lang }}</td>
                </tr>
                <tr>
                  <td class="label">小程序基础库版本</td>
                  <td width="35%">{{ detailList.libVersion }}</td>
                  <td class="label">小程序环境</td>
                  <td width="35%">{{ detailList.miniEnv }}</td>
                </tr>
                <tr>
                  <td class="label">小程序线上版本</td>
                  <td width="35%">{{ detailList.miniVersion }}</td>
                  <td class="label">宿主名称</td>
                  <td width="35%">{{ detailList.hostName }}</td>
                </tr>
                <tr>
                  <td class="label">宿主版本号</td>
                  <td width="35%">{{ detailList.hostVersion }}</td>
                  <td class="label">场景值</td>
                  <td width="35%">{{ detailList.scene }}</td>
                </tr>
                <tr>
                  <td class="label">错误摘要</td>
                  <td colspan="3">{{ detailList.message }}</td>
                </tr>
                <!-- <tr>
                  <td class="label">设备标识</td>
                  <td width="35%">{{ detailList.deviceId }}</td>
                </tr> -->
                <!-- <tr>
                <td class="label">场景值</td>
                <td colspan="3">{{ detailList.scene }}</td>
              </tr> -->
              </tbody>
            </table>
          </div>
        </div>
        <div class="event-detail">
          <TitlecCom :title="collapsetitle"></TitlecCom>
          <div style="margin-top: 10px">
            <div style="word-wrap: break-word; overflow-wrap: break-word; font-size: 14px">
              {{ detailList.stack ? detailList.stack : "暂无数据" }}
            </div>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import {
  getErrorOverviewCount,
  getErrorModelTop5,
  getMiniVersionTop5,
  getStatErrorCount,
  getErrorList,
  getErrorModel,
  getMiniVersions,
  getErrorDetail,
  getNetworkTypes
} from "@/api/mini/error";
import { Search } from "@element-plus/icons-vue";
import { applicationStore } from "@/store/modules/application";
import { breadcrumbStore } from "@/store/modules/breadcurmb";
import { getChartOptions } from "@/components/baseEcharts/chartsOptions";
import TitlecCom from "@/components/TitleCom/index.vue";
const useApplicationStore = applicationStore();
const usebreadcrumbStore = breadcrumbStore();
const loading = ref(false); //顶部loading
const tableLoading = ref(false); //表格loading
const stutterCountLoading = ref(false); //错误次数loading
const miniVersionTop5Loading = ref(false); //TOP5版本错误loading
const miniDeviceTop5Loading = ref(false); //TOP5设备错误loading
const logVisible = ref(false); //抽屉是否显示
const title = ref(""); //抽屉标题
const collapseDetailtitle = ref("基础信息"); //抽屉标题
const collapsetitle = ref("异常堆栈"); //抽屉标题
const versionCountData = ref<IIndicatordata>({}); //影响版本数
const userCountData = ref<IIndicatordata>({}); //影响用户数
const errorCountData = ref<IIndicatordata>({}); //错误次数
interface IIndicatordata {
  indicator?: string;
  unit?: string;
  value?: number | string;
  color?: string;
  errorInfo?: string;
}
//获取顶部数据
async function getErrorCountData() {
  try {
    const params = {
      appid: useApplicationStore.appId,
      mpAppid: usebreadcrumbStore.appOption
    };
    loading.value = true;
    const res = await getErrorOverviewCount(params);
    if (res.code === 0) {
      versionCountData.value = {
        unit: "请求总数",
        value: res.entity.versionCount || 0,
        errorInfo: "error"
      };
      userCountData.value = {
        unit: "平均响应时长",
        value: res.entity.userCount || 0,
        errorInfo: "error"
      };
      errorCountData.value = {
        unit: "错误数",
        value: res.entity.errorCount || 0,
        errorInfo: "error"
      };
    }
    loading.value = false;
  } catch (error) {
    loading.value = false;
    console.log(error);
  }
}
const stutterCountChartData = ref({}); //错误图表
const miniVersionTop5Data = ref({}); //TOP5版本错误
const miniDeviceTop5Data = ref({}); //TOP5设备错误
//列表参数
const pageParams = reactive({
  model: "",
  miniVersion: "",
  userId: "",
  networkType: "",
  appid: "",
  mpAppid: "",
  page: 1,
  rows: 10
});
//修改每页条数
const handleSizeChange = (val: number) => {
  pageParams.rows = val;
  loadData();
};
//分页
const handleCurrentChange = (val: number) => {
  pageParams.page = val;
  loadData();
};
const list = reactive({
  records: [],
  total: 0
});
//列表数据
function loadData() {
  tableLoading.value = true;
  pageParams.appid = useApplicationStore.appId;
  pageParams.mpAppid = usebreadcrumbStore.appOption;
  pageParams.userId = pageParams.userId.trim();
  getErrorList(pageParams)
    .then(response => {
      if (response.code === 0) {
        list.records = response.records;
        list.total = Number(response.total);
        tableLoading.value = false;
      }
    })
    .catch(error => {
      tableLoading.value = false;
      console.log(error);
    });
}
//错误图表
const initStutterCountChart = async () => {
  stutterCountLoading.value = true;
  const queue = {
    mpAppid: usebreadcrumbStore.appOption,
    appid: useApplicationStore.appId
  };
  try {
    const res = await getStatErrorCount(queue);
    if (res.code === 0 && res.entity) {
      const originalTimes = res.entity.datas.map((data: { time: any }) => data.time);
      const seriesData = [res.entity.datas.map((data: { value: any }) => data.value)];
      const color = ["#445fde"];
      const params = {
        typ: res.entity.granularity,
        color: color,
        titleType: "错误次数",
        originalTimes: originalTimes,
        seriesData: seriesData,
        type: "line"
      };
      stutterCountChartData.value = getChartOptions(params);
      stutterCountLoading.value = false;
    }
  } catch (error) {
    stutterCountLoading.value = false;
    console.error("Error logs:", error);
  }
};
//TOP5版本错误
const handleMiniVersionTop5 = () => {
  miniVersionTop5Loading.value = true;
  getMiniVersionTop5({
    mpAppid: usebreadcrumbStore.appOption,
    appid: useApplicationStore.appId
  })
    .then(res => {
      if (res.code === 0) {
        const originalTimes = res.records.map((data: { miniVersion: any }) => data.miniVersion);
        const seriesData = [res.records.map((data: { count: any }) => data.count)];
        const color = ["#445fde"];
        const params = {
          color: color,
          titleType: "TOP5版本错误",
          originalTimes: originalTimes,
          seriesData: seriesData
        };
        miniVersionTop5Data.value = getChartOptions(params);
        miniVersionTop5Loading.value = false;
      }
    })
    .catch(error => {
      miniVersionTop5Loading.value = false;
      console.log(error);
    });
};
//TOP5设备错误
const handleMiniDeviceTop5 = () => {
  miniDeviceTop5Loading.value = true;
  getErrorModelTop5({ mpAppid: usebreadcrumbStore.appOption, appid: useApplicationStore.appId })
    .then(res => {
      if (res.code === 0) {
        const originalTimes = res.records.map((data: { model: any }) => data.model);
        const seriesData = [res.records.map((data: { count: any }) => data.count)];
        const color = ["#445fde"];
        const params = {
          color: color,
          titleType: "TOP5设备错误",
          originalTimes: originalTimes,
          seriesData: seriesData
        };
        miniDeviceTop5Data.value = getChartOptions(params);
        miniDeviceTop5Loading.value = false;
      }
    })
    .catch(error => {
      miniDeviceTop5Loading.value = false;
      console.log(error);
    });
};
//清空
function resetSearch() {
  pageParams.model = "";
  pageParams.miniVersion = "";
  pageParams.userId = "";
  pageParams.networkType = "";
}
//搜索
function search() {
  pageParams.page = 1;
  loadData();
}
//设备型号下拉框
const models = ref([]);
const dataToSend = reactive({
  appid: "",
  mpAppid: ""
});
function getModel() {
  dataToSend.mpAppid = usebreadcrumbStore.appOption;
  dataToSend.appid = useApplicationStore.appId;
  getErrorModel(dataToSend).then(response => {
    models.value = response.records;
  });
}
//小程序版本下拉框
const miniVersions = ref([]);
const params = reactive({
  appid: "",
  mpAppid: ""
});
function getMiniVersion() {
  params.mpAppid = usebreadcrumbStore.appOption;
  params.appid = useApplicationStore.appId;
  getMiniVersions(params).then(respone => {
    miniVersions.value = respone.records;
  });
}
//网络类型（下拉框）
const networkTypes = ref([]);
const param = reactive({
  appid: "",
  mpAppid: ""
});
function getNetworkType() {
  param.mpAppid = usebreadcrumbStore.appOption;
  param.appid = useApplicationStore.appId;
  getNetworkTypes(param).then(respone => {
    networkTypes.value = respone.records;
  });
}
//详情字段
const detailList = ref({
  timestamp: "",
  message: "",
  name: "",
  osName: "",
  osVersion: "",
  networkType: "",
  model: "",
  userId: "",
  ip: "",
  country: "",
  province: "",
  city: "",
  brand: "",
  pixelRatio: "",
  screenArea: "",
  lang: "",
  libVersion: "",
  miniEnv: "",
  miniVersion: "",
  hostName: "",
  hostVersion: "",
  sessionId: "",
  deviceId: "",
  scene: "",
  stack: "",
  url: ""
});
//详情
const drawerLoading = ref(false);
const shouDetail = (row: any) => {
  logVisible.value = true;
  title.value = "错误详情 【" + row.id + "】";
  const appid = useApplicationStore.appId;
  drawerLoading.value = true;
  getErrorDetail(row.id, appid).then(response => {
    if (response.code === 0) {
      detailList.value = response.entity;
      drawerLoading.value = false;
    }
  });
};
onMounted(() => {
  getErrorCountData();
  loadData();
  initStutterCountChart();
  handleMiniVersionTop5();
  handleMiniDeviceTop5();
  getModel();
  getMiniVersion();
  getNetworkType();
});
</script>

<style lang="scss" scoped>
.indicator-wrapper > *:not(:last-child) {
  margin-right: 8px;
  box-sizing: border-box;
}
.log-container {
  display: flex;
  overflow-y: auto;
  overflow-x: hidden;
  min-height: 400px;

  .search-container {
    width: 260px;
    height: 380px;
    border: 1px solid #eee;
    background: #ffffff;
    padding: 15px 15px 25px 15px;
    margin-right: 10px;
  }

  .tabel-container {
    flex: 1;
    width: calc(100% - 260px);
  }
}
.input-group {
  margin-bottom: 10px;
}
.service-name {
  cursor: pointer;
  color: #0064c8;
  vertical-align: middle;
}
.event-detail {
  padding: 16px 8px 20px 8px;
  // border-bottom: 1px solid #dedede;
  // border-top: 1px solid #dedede;
  margin-top: 10px;
}
.attr-panel {
  flex: 1;
  margin-left: 10px;

  .span-panel {
    width: 750px;
    height: 875px;
    overflow-x: hidden;
    overflow-y: auto;
    table {
      border: 1px solid #ebeef5;
      border-collapse: collapse;

      tbody {
        tr {
          transition: background 0.5s;

          &:hover {
            background: #f5f7fa;
          }
        }
      }

      td {
        padding: 12px;
        position: relative;

        .duration {
          position: absolute;
          align-items: center;
          padding-left: 15px;
          display: flex;
          left: 5px;
          right: 5px;
          top: 5px;
          bottom: 5px;

          &.success {
            background: #c1e488;
          }

          &.error {
            background: #ff9393;
          }
        }

        .bar {
          width: 3px;
          height: 18px;
          margin-right: 10px;
          display: inline-block;
          vertical-align: middle;

          &.success {
            background: #009431;
          }

          &.error {
            background: #e00000;
          }
        }

        .name {
          vertical-align: middle;
          display: inline-block;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          max-width: 280px;
        }
      }

      thead {
        tr {
          td {
            background: #f9f9f9;
            border-bottom: 1px solid #ebeef5;
          }
        }
      }
    }
  }
}

.detail-table {
  width: 100%;
  color: #606266;
  font-size: 14px;
  border: 1px solid #ebeef5;
  border-collapse: collapse;

  tr {
    td {
      border: 1px solid #ebeef5;
      word-break: break-all;
      padding: 15px;
    }

    td.label {
      background: #f5f7fa;
      width: 120px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}
:deep(.el-drawer__header) {
  margin: 5px !important;
}
:deep(.attr-panel) {
  margin: 0 !important;
}
</style>
