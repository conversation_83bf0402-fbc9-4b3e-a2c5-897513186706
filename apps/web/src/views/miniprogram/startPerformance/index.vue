<template>
  <div v-loading="loading">
    <div class="flex justify-between mb-10px indicator-wrapper">
      <Indicator
        v-for="(item, i) in startCount"
        :key="i"
        :trafficType="item.type"
        :value="item.value"
        :unit="item.label"
        :color="item.color ? item.color : undefined"
      ></Indicator>
    </div>
    <div class="flex justify-between indicator-wrapper mt-10px">
      <BaseEcharts
        v-loading="statLaunchSlowCountLoading"
        :options="statErrorRateData"
        height="250px"
      />
      <BaseEcharts
        v-loading="statLaunchCrashCountLoading"
        :options="statSlowRateData"
        height="250px"
      />
      <BaseEcharts :options="averageStartTimeChartData" height="250px" />
    </div>
  </div>

  <MyTable
    :data="launchStatList"
    class="mt-10px"
    v-loading="loading"
    :total="launchStatTotal"
    style="width: 100%"
    @sizeChange="handleSizeChange"
    @currentChange="handleCurrentChange"
    :default-sort="{ prop: 'count', order: 'descending' }"
    @sort-change="handleSortChange"
  >
    <my-column property="model" label="设备型号" />
    <my-column property="osName" label="操作系统" />
    <my-column property="total" label="总启动数" sortable="custom" />
    <my-column property="slowCount" label="慢启动数" sortable="custom" />
    <my-column property="appStartDuration" label="启动耗时" sortable="custom" />
    <my-column property="appInitDuration" label="初始化耗时" sortable="custom" />
    <my-column property="pageLoadDuration" label="首页加载耗时" sortable="custom" />
    <my-column property="domParseDuration" label="首页DOM解析耗时" sortable="custom" width="250" />
    <my-column property="pageRenderDuration" label="页面渲染耗时" sortable="custom" />
  </MyTable>
</template>

<script setup lang="ts">
import { breadcrumbStore } from "@/store/modules/breadcurmb";
import {
  getStartOverviewCount,
  getStatCount,
  getStatSlowCount,
  getStatAvgDuration,
  getStartList
} from "@/api/mini/start";
import { getChartOptions } from "@/components/baseEcharts/chartsOptions";
import { applicationStore } from "@/store/modules/application";
const usebreadcrumbStore = breadcrumbStore();

const loading = ref(false);
const useApplicationStore = applicationStore();
const statLaunchSlowCountLoading = ref(false);
const statLaunchCrashCountLoading = ref(false);
const avgDurationLoading = ref(false);
const launchStatList = ref([]);
const launchStatTotal = ref(0);

const statErrorRateData = ref({});
const statSlowRateData = ref({});
const averageStartTimeChartData = ref({});

onMounted(() => {
  getLaunchStatListData();
  getStatSlowCountData();
  getAvgDurationData();
  getStatCtashCountData();
  getStartCount();
});
const startCount = ref({});
const getStartCount = async () => {
  loading.value = true;
  const queue = { appid: useApplicationStore.appId, mpAppid: usebreadcrumbStore.appOption };

  try {
    const res = await getStartOverviewCount(queue);
    if (res.code === 0 && res.entity) {
      const { total, slowCount, avgDuration } = res.entity;
      startCount.value = [
        { label: "启动总数", value: total },
        {
          label: "慢启动数",
          value: slowCount,
          color: slowCount > 0 ? "#e6a23c" : ""
        },
        { label: "启动平均耗时（ms）", value: avgDuration }
      ];
      loading.value = false;
    }
  } catch (error) {
    loading.value = false;
    console.error("Error logs:", error);
  }
};

//列表参数
const pageParams = ref({
  page: 1,
  rows: 10,
  sort: "",
  order: ""
});
async function getLaunchStatListData() {
  const queue = { appid: useApplicationStore.appId, mpAppid: usebreadcrumbStore.appOption };
  try {
    const res = await getStartList({ ...queue, ...pageParams.value });
    if (res.code === 0) {
      launchStatList.value = res.records.map(item => {
        return {
          ...item,
          pageRenderDuration: item.pageRenderDuration + " ms",
          domParseDuration: item.domParseDuration + " ms",
          appInitDuration: item.appInitDuration + " ms",
          pageLoadDuration: item.pageLoadDuration + " ms",
          appStartDuration: item.appStartDuration + " ms"
        };
      });
      launchStatTotal.value = Number(res.total);
    }
  } catch (error) {
    console.log(error);
  }
}
const getAvgDurationData = async () => {
  const queue = {
    mpAppid: usebreadcrumbStore.appOption,
    appid: useApplicationStore.appId
  };
  avgDurationLoading.value = true;
  try {
    const res = await getStatAvgDuration(queue);
    if (res.code === 0 && res.entity) {
      const originalTimes = res.entity.datas.map((data: { time: string }) => data.time);
      const seriesData = [res.entity.datas.map((data: { value: number }) => data.value)];
      const color = ["#78bf75"];
      const params = {
        typ: res.entity.granularity,
        color: color,
        name: " ms",
        titleType: "启动平均耗时",
        originalTimes: originalTimes,
        seriesData: seriesData,
        type: "line"
      };
      averageStartTimeChartData.value = getChartOptions(params);
    }
    avgDurationLoading.value = false;
  } catch (error) {
    avgDurationLoading.value = false;
    console.error("Error logs:", error);
  }
};

// 获取错误率统计数据
const getStatSlowCountData = async () => {
  const queue = {
    mpAppid: usebreadcrumbStore.appOption,
    appid: useApplicationStore.appId
  };
  statLaunchSlowCountLoading.value = true;
  try {
    const res = await getStatCount(queue);
    if (res.code === 0 && res.entity) {
      const originalTimes = res.entity.datas.map((data: { time: string }) => data.time);
      const seriesData = [res.entity.datas.map((data: { value: number }) => data.value)];
      const color = ["#78bf75"];
      const params = {
        typ: res.entity.granularity,
        color: color,
        titleType: "启动数统计",
        originalTimes: originalTimes,
        seriesData: seriesData
      };
      statErrorRateData.value = getChartOptions(params);
    }
    statLaunchSlowCountLoading.value = false;
  } catch (error) {
    statLaunchSlowCountLoading.value = false;
    console.error("Error logs:", error);
  }
};

// 获取慢启动统计数据的函数
const getStatCtashCountData = async () => {
  const queue = {
    mpAppid: usebreadcrumbStore.appOption,
    appid: useApplicationStore.appId
  };
  statLaunchCrashCountLoading.value = true;
  try {
    const res = await getStatSlowCount(queue);
    if (res.code === 0 && res.entity) {
      const originalTimes = res.entity.datas.map((data: { time: string }) => data.time);
      const seriesData = [res.entity.datas.map((data: { value: number }) => data.value)];
      const color = ["#e6a23c"];
      const params = {
        typ: res.entity.granularity,
        color: color,
        titleType: "慢启动统计",
        originalTimes: originalTimes,
        seriesData: seriesData
      };
      statSlowRateData.value = getChartOptions(params);
    }
    statLaunchCrashCountLoading.value = false;
  } catch (error) {
    statLaunchCrashCountLoading.value = false;
    console.error("Error logs:", error);
  }
};

const handleSortChange = (val: any) => {
  const order = val.order;
  const sort = val.prop;
  if (order === "ascending") {
    pageParams.value.order = "0";
  } else {
    pageParams.value.order = "1";
  }
  pageParams.value.sort = sort;
  getLaunchStatListData();
};

const handleSizeChange = (val: number) => {
  pageParams.value.rows = val;
  getLaunchStatListData();
};

//分页
const handleCurrentChange = (val: number) => {
  pageParams.value.page = val;
  getLaunchStatListData();
};
</script>

<style lang="scss" scoped>
.indicator-wrapper > *:not(:last-child) {
  margin-right: 8px;
  box-sizing: border-box;
}
</style>
