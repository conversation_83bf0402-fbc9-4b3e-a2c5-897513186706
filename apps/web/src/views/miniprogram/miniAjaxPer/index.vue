<template>
  <div>
    <div class="flex justify-between indicator-wrapper" v-loading="loading">
      <Indicator :value="requestCountData.value" :unit="'请求总数'"></Indicator>
      <Indicator :value="errorCountData.value" :unit="'请求失败数'" :color="'#f56c6c'"></Indicator>
      <Indicator :value="errorRate.value" :unit="'请求失败率（%）'" :color="'#f56c6c'"></Indicator>
      <Indicator :value="interfaceAvtDutation.value" :unit="'平均响应时长（ms）'"></Indicator>
    </div>
    <div class="flex justify-between indicator-wrapper mt-10px">
      <BaseEcharts v-loading="requestCountLoading" :options="statRequestCountData" height="250px" />
      <BaseEcharts
        v-loading="statErrorCountsLoading"
        :options="statErrorCountsData"
        height="250px"
      />
      <BaseEcharts
        v-loading="h5statAvgDurationLoading"
        :options="h5statAvgDurationData"
        height="250px"
      />
    </div>
    <MyTable
      class="mt-10px"
      :data="interPerforList"
      :total="interPerforTotal"
      v-loading="tableLoading"
      style="width: 100%"
      @sizeChange="handleSizeChange"
      @currentChange="handleCurrentChange"
      :default-sort="{
        prop: 'count && slowCount && errorCount',
        order: 'descending'
      }"
      @sort-change="handleSortChange"
    >
      <my-column property="summary" label="请求路径">
        <template #default="scope">
          <span class="url-name" @click="viewDetail(scope)">
            {{ scope.row.url }}
          </span>
        </template>
      </my-column>
      <my-column property="count" label="请求总数" sortable="custom" width="200" />
      <my-column property="errorCount" label="请求错误数" sortable="custom" width="200">
      </my-column>
      <my-column property="errorRate" label="请求错误率" sortable="custom" width="200"> </my-column>
      <my-column property="avgDuration" label="平均响应时长" sortable="custom" width="200">
      </my-column>
    </MyTable>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from "vue-router";
import { breadcrumbStore } from "@/store/modules/breadcurmb";
import {
  getMiniErrorCount,
  getMiniAvgDurationRate,
  getMiniRequestCount,
  getInterPerformanceList,
  IInterPerforList,
  IInterPerforItem,
  getOverviewCount
} from "@/api/mini/ajaxPer";
import { getChartOptions } from "@/components/baseEcharts/chartsOptions";
const useBreadcrumbStore = breadcrumbStore();
const router = useRouter();
interface IIndicatordata {
  indicator?: string;
  unit?: string;
  value?: number | string;
  color?: string;
  errorInfo?: string;
}
const loading = ref(false);
const statErrorCountsLoading = ref(false);
const requestCountLoading = ref(false);
const h5statAvgDurationLoading = ref(false);
const tableLoading = ref(false);

const requestCountData = ref<IIndicatordata>({});
const interfaceAvtDutation = ref<IIndicatordata>({});
const errorRate = ref<IIndicatordata>({});
const errorCountData = ref<IIndicatordata>({});

const interPerforList = ref<IInterPerforItem[]>([]);
const interPerforTotal = ref(0);
const statErrorCountsData = ref({});
const statRequestCountData = ref({});
const h5statAvgDurationData = ref({});
onMounted(() => {
  getMiniInterPerforListData();
  getStatH5ErrorCountData();
  getMiniAvgDurationData();
  getStatEequestCountData();
  getInterCountData();
});

// 上面的请求数，平均耗时，错误率，慢请求率
async function getInterCountData() {
  try {
    loading.value = true;
    const res = await getOverviewCount({});
    if (res.code === 0) {
      requestCountData.value = {
        unit: "请求总数",
        value: res.entity.requestCount || 0,
        errorInfo: "error"
      };
      interfaceAvtDutation.value = {
        unit: "平均响应时长",
        value: res.entity.avgDuration || 0,
        errorInfo: "error"
      };
      errorRate.value = {
        unit: "错误率",
        value: res.entity.errorRate || 0,
        errorInfo: "error"
      };
      errorCountData.value = {
        unit: "错误数",
        value: res.entity.errorCount || 0,
        errorInfo: "error"
      };
    }
    loading.value = false;
  } catch (error) {
    loading.value = false;
    console.log(error);
  }
}

//列表参数
const pageParams = ref({
  page: 1,
  rows: 10,
  sort: "",
  order: ""
});
async function getMiniInterPerforListData() {
  try {
    tableLoading.value = true;
    const res: IInterPerforList = await getInterPerformanceList(pageParams.value);
    if (res.code === 0) {
      interPerforList.value = res?.records.map(item => {
        return {
          ...item,
          errorRate: item.errorRate + "%",
          avgDuration: item.avgDuration + "ms"
        };
      });
      interPerforTotal.value = Number(res.total);
      tableLoading.value = false;
    }
  } catch (error) {
    console.log(error);
    tableLoading.value = false;
  }
}
// 获取错误率统计数据

const getStatH5ErrorCountData = async () => {
  statErrorCountsLoading.value = true;
  try {
    const res = await getMiniErrorCount({});
    if (res.code === 0 && res.entity) {
      const originalTimes = res.entity.datas.map((data: { time: string }) => data.time);
      const seriesData = [res.entity.datas.map((data: { value: number }) => data.value)];
      const color = ["#f56c6c"];
      const params = {
        typ: res.entity.granularity,
        color: color,
        titleType: "请求失败数统计",
        originalTimes: originalTimes,
        seriesData: seriesData
      };
      statErrorCountsData.value = getChartOptions(params);
    }
    statErrorCountsLoading.value = false;
  } catch (error) {
    statErrorCountsLoading.value = false;
    console.error("Error logs:", error);
  }
};

const getStatEequestCountData = async () => {
  try {
    requestCountLoading.value = true;
    const res = await getMiniRequestCount({});
    if (res.code === 0 && res.entity) {
      const originalTimes = res.entity.datas.map((data: { time: string }) => data.time);
      const seriesData = [res.entity.datas.map((data: { value: number }) => data.value)];
      const color = ["#78bf75"];
      const params = {
        typ: res.entity.granularity,
        color: color,
        titleType: "请求数统计",
        originalTimes: originalTimes,
        seriesData: seriesData
      };
      statRequestCountData.value = getChartOptions(params);
    }
    requestCountLoading.value = false;
  } catch (error) {
    requestCountLoading.value = false;
    console.error("Error logs:", error);
  }
};
// 获取统计平均耗时数据
const getMiniAvgDurationData = async () => {
  try {
    h5statAvgDurationLoading.value = true;
    const res = await getMiniAvgDurationRate({});
    if (res.code === 0 && res.entity) {
      const originalTimes = res.entity.datas.map((data: { time: string }) => data.time);
      const seriesData = [res.entity.datas.map((data: { value: number }) => data.value)];
      const color = ["#78bf75"];
      const params = {
        name: " ms",
        typ: res.entity.granularity,
        color: color,
        titleType: "平均响应时长统计",
        originalTimes: originalTimes,
        seriesData: seriesData,
        type: "line"
      };
      h5statAvgDurationData.value = getChartOptions(params);
    }
    h5statAvgDurationLoading.value = false;
  } catch (error) {
    h5statAvgDurationLoading.value = false;
    console.error("Error logs:", error);
  }
};
const handleSortChange = (val: any) => {
  const order = val.order;
  const sort = val.prop;
  if (order === "ascending") {
    pageParams.value.order = "0";
  } else {
    pageParams.value.order = "1";
  }
  pageParams.value.sort = sort;
  getMiniInterPerforListData();
  // loadData();
};

const handleSizeChange = (val: number) => {
  pageParams.value.rows = val;
  getMiniInterPerforListData();
};
//分页
const handleCurrentChange = (val: number) => {
  pageParams.value.page = val;
  getMiniInterPerforListData();
};
// 查看详情
const viewDetail = (scope: any) => {
  router.push("/miniprogram/ajax-performance/detail");
  useBreadcrumbStore.setBreadcrumb(scope.row.url);
};
</script>

<style lang="scss" scoped>
.indicator-wrapper > *:not(:last-child) {
  margin-right: 8px;
  box-sizing: border-box;
}
.url-name {
  cursor: pointer;
  color: #0064c8;
  vertical-align: middle;
}
</style>
