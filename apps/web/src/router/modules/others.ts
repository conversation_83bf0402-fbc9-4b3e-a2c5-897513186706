import { RouteRecordRaw } from "vue-router";

export default [
  {
    name: "TCPdetail",
    path: "/TCPdetail",
    component: () => import("@/views/flowMonitoring/TCPdetail/index.vue")
  },
  {
    name: "developmentBot",
    path: "/development/bot/:id",
    component: () => import("@/views/cozi/development/bot/index.vue")
  },
  {
    name: "developmentAPP",
    path: "/development/app/:id",
    component: () => import("@/views/cozi/development/app/index.vue")
  }
] as RouteRecordRaw[];
