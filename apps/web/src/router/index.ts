import { createRouter, createWebHistory, RouteRecordRaw } from "vue-router";
import { serviceNameStore } from "@/store/modules/service";
const Layout = () => import("@/layout/index.vue");

import login from "./modules/login";
import others from "./modules/others";
import Topo from "./modules/Topo";
import { asyncComponentMap } from "./defineAsyncComponent ";
import appRouter from "./routerJson/appRouter";
import dashRouter from "./routerJson/dashRouter";
import h5Router from "./routerJson/h5Router";
import miniRouter from "./routerJson/miniRouter";
import moduleRouter from "./routerJson/moduleRouter";
const routerMap = {
  appmonitor: appRouter,
  dashboard: dashRouter,
  H5monitor: h5Router,
  miniprogram: miniRouter,
  module: moduleRouter
};
// 静态路由
const cachedMenu: RouteRecordRaw[] = JSON.parse(localStorage.getItem("cachedMenu") || "[]");
const jsjsRouter = cachedMenu.map(item => {
  const localChildren = routerMap[item.name] || [];

  // 构造当前 item 的 children（如果有）
  const mappedChildren =
    item.children?.map(child => ({
      ...child,
      component: child.component && child.name ? asyncComponentMap[child.name] : undefined
    })) || [];

  return {
    ...item,
    component: item.component && item.name ? asyncComponentMap[item.name] : undefined,
    children: [...mappedChildren, ...localChildren]
  };
});
console.log("jsjsRouter", jsjsRouter);

export const constantRoutes: RouteRecordRaw[] = [
  {
    name: "/",
    path: "/",
    component: Layout,
    redirect: "/login",
    children: [
      ...jsjsRouter,

      // ...dashboard,
      // ...h5Monitor,
      // ...appMointor,
      // ...miniProgramMonitor,
      // ...flowMonitoring,
      // ...logMonitoring,
      // // ...warnManage,
      // // ...tablePage,
      // ...system,
      ...Topo
    ]
  },
  ...login,
  ...others
];

/**
 * 创建路由
 */
const router = createRouter({
  history: createWebHistory(),
  routes: constantRoutes,
  // 刷新时，滚动条位置还原
  scrollBehavior: () => ({ left: 0, top: 0 })
});

/**
 * 重置路由
 */
// export function resetRouter() {
//   router.replace({ path: "/login" });
// }

// router.beforeEach((to, from) => {
//   // app.config.globalProperties.$skywalingMonitor.handlePerformanceSky();
// });
router.beforeEach((to, from) => {
  const slashCount = (to.path.match(/\//g) || []).length;

  if (slashCount <= 2) {
    const store = serviceNameStore();
    store.clearServiceName();
    // app.config.globalProperties.$skywalingMonitor.handlePerformanceSky();
  }
});
export default router;
