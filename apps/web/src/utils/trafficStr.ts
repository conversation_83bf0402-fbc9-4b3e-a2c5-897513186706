export function convertBytes(bytes: number) {
  if (bytes === 0)
    return {
      fixValue: 0,
      unit: "Bytes"
    };
  if (bytes < 1024)
    return {
      fixValue: bytes,
      unit: "Bytes"
    };
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return {
    fixValue: parseFloat((bytes / Math.pow(k, i)).toFixed(2)),
    unit: sizes[i]
  };
}
