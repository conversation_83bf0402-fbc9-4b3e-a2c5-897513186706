// 处理时间格式
import dayjs from "dayjs";

/**
 * 将纳秒级时间戳格式化为字符串 "YYYY-MM-DD HH:mm:ss"。
 * @param {number} timestamp - 纳秒级时间戳（如 1713850000000000000）。
 * @returns {string} - 格式化后的日期时间字符串。
 */
export function formatTimestamp(timestamp: number): string {
  return dayjs(timestamp / 1_000_000).format("YYYY-MM-DD HH:mm:ss");
}

/**
 * 将日期对象或时间字符串格式化为 "YYYY-MM-DD HH:mm:ss"。
 * 用于展示常规时间数据。
 * @param {string | Date} time - 时间字符串或 Date 对象。（如 "YYYY-MM-DDTHH:mm:ssZ"）
 * @returns {string} - 格式化后的时间字符串。
 */
export function formatTime(time: string | Date): string {
  return dayjs(time).format("YYYY-MM-DD HH:mm:ss");
}

// 再加一个纳秒级的formatTime
/**
 * 将纳秒级时间戳格式化为 "YYYY-MM-DD HH:mm:ss"。
 * 用于展示常规时间数据。
 * @param {number} timestamp - 纳秒级时间戳（如 1713850000000000000）。
 * @returns {string} - 格式化后的时间字符串。
 */

export function formatTimeWithNanoseconds(time: string): string {
  if (typeof time !== "string") return "";
  const [datePart, timePart] = time.split("T");
  if (!timePart) return time;
  const [hms] = timePart.split(".");
  if (!hms) return time;
  const [hour, minute, second] = hms.split(":");
  if (!hour || !minute || !second) return time;
  // 保留到秒
  return `${datePart} ${hour}:${minute}:${second}`;
}

/**
 * 将 "YYYY-MM-DD HH:mm:ss" 格式的时间字符串转换为毫秒级时间戳。
 * 用于传给后端的时间参数或做时间计算。
 * @param {string} datetimeStr - 时间字符串，如 "2025-04-23 11:06:17"。
 * @returns {number} - 毫秒级时间戳。
 */
export function toMillis(datetimeStr: string): number {
  return new Date(datetimeStr).getTime();
}

/**
 * 将秒数格式化为易读的字符串，按照月、天、时、分、秒展示
 * 并自动根据值的大小调整显示的单位范围
 * @param {number} seconds - 需要格式化的秒数
 * @returns {string} - 格式化的时间字符串
 */
export function formatSeconds(seconds: number): string {
  if (isNaN(seconds) || seconds < 0) {
    return "0秒";
  }

  // 定义时间常数（按30天/月计算）
  const SECONDS_PER_MINUTE = 60;
  const SECONDS_PER_HOUR = 60 * SECONDS_PER_MINUTE;
  const SECONDS_PER_DAY = 24 * SECONDS_PER_HOUR;
  const SECONDS_PER_MONTH = 30 * SECONDS_PER_DAY;

  // 计算所有时间单位
  const months = Math.floor(seconds / SECONDS_PER_MONTH);
  const days = Math.floor((seconds % SECONDS_PER_MONTH) / SECONDS_PER_DAY);
  const hours = Math.floor((seconds % SECONDS_PER_DAY) / SECONDS_PER_HOUR);
  const minutes = Math.floor((seconds % SECONDS_PER_HOUR) / SECONDS_PER_MINUTE);
  const secs = Math.floor(seconds % SECONDS_PER_MINUTE);

  // 根据时间范围构建结果字符串
  const parts: string[] = [];

  if (months > 0) {
    parts.push(`${months}月`);
  }
  if (months > 0 || days > 0) {
    parts.push(`${days}天`);
  }
  if (months > 0 || days > 0 || hours > 0) {
    parts.push(`${hours}时`);
  }
  if (months > 0 || days > 0 || hours > 0 || minutes > 0) {
    parts.push(`${minutes}分`);
  }

  // 总是显示秒数
  parts.push(`${secs}秒`);

  return parts.join("");
}

/**
 * 将毫秒数格式化为智能的时间表示（最大显示单位：天）
 * 只展示必要的单位（有天数则显示天，无天数则显示小时，以此类推）
 * 不足一秒展示毫秒，不足一天也展示毫秒
 *
 * @param {number} milliseconds - 要格式化的毫秒数
 * @returns {string} - 格式化后的时间字符串
 */
export function formatMillisecondsToDays(milliseconds: number): string {
  if (!Number.isFinite(milliseconds) || milliseconds < 0) {
    return "0秒";
  }

  // 计算时间分量
  const totalSeconds = Math.floor(milliseconds / 1000);
  const days = Math.floor(totalSeconds / (24 * 60 * 60));
  const hours = Math.floor((totalSeconds % (24 * 60 * 60)) / (60 * 60));
  const minutes = Math.floor((totalSeconds % (60 * 60)) / 60);
  const seconds = totalSeconds % 60;
  const ms = Math.floor(milliseconds % 1000);

  // 智能构建结果字符串（只显示必要的单位）
  const parts: string[] = [];

  if (days > 0) {
    parts.push(`${days}天`);
    if (hours > 0) parts.push(`${hours}时`);
    if (minutes > 0) parts.push(`${minutes}分`);
    if (seconds > 0) parts.push(`${seconds}秒`);
    if (ms > 0) parts.push(`${ms}毫秒`);
  } else {
    if (hours > 0) {
      parts.push(`${hours}时`);
      if (minutes > 0) parts.push(`${minutes}分`);
      if (seconds > 0) parts.push(`${seconds}秒`);
      if (ms > 0) parts.push(`${ms}毫秒`);
    } else if (minutes > 0) {
      parts.push(`${minutes}分`);
      if (seconds > 0) parts.push(`${seconds}秒`);
      if (ms > 0) parts.push(`${ms}毫秒`);
    } else if (seconds > 0) {
      parts.push(`${seconds}秒`);
      if (ms > 0) parts.push(`${ms} 毫秒`);
    } else {
      parts.push(`${ms} ms`);
    }
  }

  return parts.join("");
}
