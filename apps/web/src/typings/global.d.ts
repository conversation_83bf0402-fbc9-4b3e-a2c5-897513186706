/**
 * 页签对象
 */
interface TagView {
  /** 页签名称 */
  name: string;
  /** 页签路由路径 */
  path: string;
  /** 页签路由完整路径 */
  fullPath?: string;
  /** 路由查询参数 */
  query?: any;
  children?: TagView[];
  meta: {
    /** 页签标题 */
    title: string;
    /** 页签图标 */
    icon?: string;
    iconType?: string;
    /** 是否固定页签 */
    affix?: boolean;
    /** 是否隐藏 */
    hidden?: boolean;
    /** 是否开启缓存 */
    keepAlive?: boolean;
    /** 是详情路由 */
    detailRoute?: boolean;
    /** 不显示在二级页面的tab栏上 */
    hiddenDetailTabs?: boolean;
    /** 模块类型，用于区分不同模块的tab页 */
    moduleType?: string;
  };
}

declare module "skywalking-mointor";
