import { post, get } from "@/http/axios";
import { IEditPwdData, IUserInfoData } from "@/api/user/type";

// 修改密码接口
export const editPassword = (data: IEditPwdData) => {
  return post({}, "/edit_pwd", data);
};

// 获取用户信息
export const getUserInfo = (data: {}) => {
  return get<IUserInfoData>({}, "/principal", data);
};

//注销登录
export const logOut = () => {
  return get({}, "/logout");
};

// 获取租户信息
export const getTenantInfo = (data: {}) => {
  return post({}, "/sys/tenants/page", data);
};

// 切换租户
export const switchTenants = (tenantId: string) => {
  return get({}, `/sys/user/tenant_switch/${tenantId}`);
};
