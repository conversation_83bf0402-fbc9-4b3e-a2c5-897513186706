export interface IBaseResult {
  desc?: string; // 描述
  code?: number; // 状态码
}

export interface IEditPwdData {
  oldPassword: string; // 旧密码
  password: string; // 新密码
  aesKey: string; // 加密密钥
}

export interface IUserInfo {
  entity?: {
    id: number; // 用户id
    name: string; // 用户名
    mobile: string; // 用户手机
    email: string; // 用户邮箱
    remark: string; // 备注
    lockStatus: number; // 是否锁定
    type: number; // 类型
    createdAt: string; // 创建时间
    updatedAt: string; // 更新时间
  };
}

export interface IUserDetailInfo {
  id?: number; // 用户id
  name?: string; // 用户名
  mobile?: string; // 用户手机
  email?: string; // 用户邮箱
  remark?: string; // 备注
  lockStatus?: number; // 是否锁定
  type?: number; // 类型
  createdAt?: string; // 创建时间
  updatedAt?: string; // 更新时间
}

export interface IUserInfoData {
  desc?: string; // 描述
  code?: number; // 状态码
  entity?: {
    id: number; // 用户id
    name: string; // 用户名
    mobile: string; // 用户手机
    email: string; // 用户邮箱
    remark: string; // 备注
    lockStatus: number; // 是否锁定
    type: number; // 类型
    createdAt: string; // 创建时间
    updatedAt: string; // 更新时间
  };
}
