import { post, get } from "@/http/axios";
import { serviceTimeStore } from "@/store/modules/global";
const serTimeStore = serviceTimeStore();
const fetchServiceData = (url: string, data: any) => {
  return get({}, url, {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time
  });
};
// 影响设备数
export const getDeviceCount = (data: any) => {
  return fetchServiceData("/app/crash/device_count", data);
};

// 总崩溃次数
export const getCrashCount = (data: any) => {
  return fetchServiceData("/app/overview/crash_count", data);
};

// 影响版本数
export const getVersionsCount = (data: any) => {
  return fetchServiceData("/app/crash/version_count", data);
};

// 总崩溃数（图表）
export const getCrashCountChart = (data: any) => {
  return fetchServiceData("/app/overview/stat_crash_count", data);
};

// APP版本TOP5
export const getAppVersionTop5 = (data: any) => {
  return fetchServiceData("/app/crash/app_version_top5", data);
};

// APP设备TOP5
export const getAppDeviceTop5 = (data: any) => {
  return fetchServiceData("/app/crash/device_top5", data);
};

// 崩溃统计列表
export const getCollapseStatList = (data: any) => {
  return fetchServiceData("/app/crash/stat_list", data);
};

// 崩溃列表
export const getCollapseList = (data: any) => {
  return fetchServiceData("/app/crash/list", data);
};

// 网络下拉框
export const getCrashNets = (data: any) => {
  return fetchServiceData("/app/crash/nets", data);
};

// APP版本下拉框
export const getCrashAppVersions = (data: any) => {
  return fetchServiceData("/app/crash/app_versions", data);
};

//崩溃详情
export const getCrashDetail = (id: string) => {
  return get({}, "/app/crash/detail/" + id);
};
