import { get } from "@/http/axios";
import { serviceTimeStore } from "@/store/modules/global";
const serTimeStore = serviceTimeStore();
// 通用函数，合并参数
const fetchServiceData = (url: string, data: any) => {
  return get({}, url, {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time
  });
};
export const getPacketPkt = (data: any) => {
  return fetchServiceData("/packet/pkt", data);
};
export const getPacketStatis = (data: any) => {
  return fetchServiceData("/packet/statis", data);
};
