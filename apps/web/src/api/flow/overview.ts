import { post, get } from "@/http/axios";
import { serviceTimeStore } from "@/store/modules/global";
const serTimeStore = serviceTimeStore();
import { applicationStore } from "@/store/modules/application";
const useApplicationStore = applicationStore();

export interface IBaseResponse {
  code: number;
  message: string;
  data: IFlowOverview;
}

export interface IFlowOverview {
  total_bytes: number;
  total_packets: number;
  in_bytes: number;
  in_packets: number;
  out_bytes: number;
  out_packets: number;
}

export interface IDataPacketTypeResponse {
  code: number;
  message: string;
  data: IDataPacketType;
}

export interface IDataPacketType {
  ip_version: IIpVersion;
  protocol: IProtocol;
  cast_type: ICastType;
  tcp_flags: ITcpFlags;
}

export interface ITcpFlags {
  syn_packets: number;
  ack_packets: number;
  fin_packets: number;
  rst_packets: number;
  psh_packets: number;
  total_packets: number;
}

export interface ICastType {
  unicast_packets: number;
  broadcast_packets: number;
  multicast_packets: number;
  total_packets: number;
}

export interface IProtocol {
  tcp_packets: number;
  udp_packets: number;
  tcp_bytes: number;
  udp_bytes: number;
  total_packets: number;
  total_bytes: number;
}

export interface IIpVersion {
  ipv4_packets: number;
  ipv6_packets: number;
  ipv4_bytes: number;
  ipv6_bytes: number;
  total_packets: number;
  total_bytes: number;
}

export interface IDataPacketRangeResponse {
  code: number;
  message: string;
  data: IRanges;
}

export interface IRanges {
  ranges: IRange[];
  total_packets: number;
  total_bytes: number;
  avg_size: number;
}

export interface IRange {
  range_start: number;
  range_end: number;
  count: number;
  percentage: number;
  avg_size: number;
}

export interface Params {
  appid: number;
  startTime: string;
  endTime: string;
}

// 流量顶部详情
export const getFlowOverview = (data: Params) => {
  const start = serTimeStore.serviceTimeData.start_time;
  const end = serTimeStore.serviceTimeData.end_time;
  data = {
    ...data,
    startTime: start,
    endTime: end
  };
  return get({}, `/netflow/overview/flow`, data);
};
// 获取数据包类型
export const getDataPacketType = () => {
  const start = serTimeStore.serviceTimeData.start_time;
  const end = serTimeStore.serviceTimeData.end_time;
  const ipParam = {
    startTime: start,
    endTime: end,
    // ip: ip,
    appid: useApplicationStore.appId
  };
  return get({}, `/netflow/overview/packet_type`, ipParam);
};
// 获取数据包区间
export const getDataPacketRange = () => {
  const start = serTimeStore.serviceTimeData.start_time;
  const end = serTimeStore.serviceTimeData.end_time;
  const ipParam = {
    startTime: start,
    endTime: end,
    // ip: ip,
    appid: useApplicationStore.appId
  };
  return get({}, `/netflow/overview/packet_size`, ipParam);
};

// IP会话
export interface IIpSessionResponse {
  code: number;
  message: string;
  data: ISessionsArr;
}

export interface ISessionsArr {
  sessions: ISessionItem[];
  total_count: number;
}

export interface ISessionItem {
  ip1: string;
  ip2: string;
  protocol: number;
  start_ts: number;
  last_ts: number;
  packets: number;
  bytes: number;
  packets1to2: number;
  packets2to1: number;
  bytes1to2: number;
  bytes2to1: number;
  synCount: number;
  finCount: number;
  rstCount: number;
}
export interface IIpSessionRequest {
  start: string;
  end: string;
  page: string;
  page_size: string;
  order: string;
  sort: string;
  sip: string;
}

export const getIpSessions = (data: any) => {
  return get({}, "/netflow/session/ip/list", data);
};

export interface ISessionTopoResponse {
  code: number;
  message: string;
  data: ITopoItem[];
}

export interface ITopoItem {
  ip: string;
  related_ip: string;
  total_bytes: number;
  in_bytes: number;
  out_bytes: number;
}

export const getSessionTopo = () => {
  const start = serTimeStore.serviceTimeData.start_time;
  const end = serTimeStore.serviceTimeData.end_time;
  const ipParam = {
    startTime: start,
    endTime: end,
    appid: useApplicationStore.appId
    // ip: ip
  };
  return get({}, `/netflow/overview/relation`, ipParam);
};

// 地区统计
export const getOverviewRegion = () => {
  const start = serTimeStore.serviceTimeData.start_time;
  const end = serTimeStore.serviceTimeData.end_time;
  const ipParam = {
    startTime: start,
    endTime: end,
    // ip: ip,
    appid: useApplicationStore.appId
  };
  return get({}, `/netflow/overview/region`, ipParam);
};
