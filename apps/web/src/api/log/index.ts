import { get, post } from "@/http/axios";
import { serviceTimeStore } from "@/store/modules/global";
const serTimeStore = serviceTimeStore();
import { applicationStore } from "@/store/modules/application";
const useApplicationStore = applicationStore();
// 通用函数，合并参数
const fetchServiceData = (url: string, data: any) => {
  return get({}, url, {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time
  });
};
//日志列表
export const getLogList = (data: any) => {
  return fetchServiceData("/server/logs/list", data);
};
//日志图表
export const getLogStatistics = (data: any) => {
  return fetchServiceData("/server/logs/statistics", data);
};
//搜索日志
export const logSearch = (
  bodyData: any,
  queryData: any,
  pageNum: number = 1,
  pageSize: number = 10
) => {
  const requestBody = {
    request: {
      appid: useApplicationStore.appId,
      startTime: bodyData.startTime || serTimeStore.serviceTimeData.start_time,
      endTime: bodyData.endTime || serTimeStore.serviceTimeData.end_time,
      query: queryData.query
    },
    pageRequest: {
      page: pageNum,
      rows: pageSize
    },
    logEntity: {
      level: bodyData.level,
      message: bodyData.message,
      sourceIp: bodyData.sourceIp,
      sourceName: bodyData.sourceName,
      sourceType: bodyData.sourceType,
      metadata: bodyData.metadata
    }
  };

  return post({}, "/logs/searchContent", requestBody, false, false);
};
//日志概览统计
export const logsOverview = () => {
  const requestData = {
    appid: useApplicationStore.appId,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time
  };
  return post({}, "/logs/overview", requestData);
};
//日志看台图表
export const logSearchChart = (bodyData: any, queryData: any) => {
  const requestBody = {
    request: {
      appid: useApplicationStore.appId,
      startTime: serTimeStore.serviceTimeData.start_time,
      endTime: serTimeStore.serviceTimeData.end_time,
      query: queryData.query
    },
    logEntity: {
      level: bodyData.level,
      message: bodyData.message,
      sourceIp: bodyData.sourceIp,
      sourceName: bodyData.sourceName,
      sourceType: bodyData.sourceType,
      metadata: bodyData.metadata
    }
  };

  return post({}, "/logs/searchChart", requestBody, false, false);
};
//来源类型
export const getSourceTypeList = (queryData: any) => {
  const request = {
    request: {
      appid: useApplicationStore.appId,
      startTime: serTimeStore.serviceTimeData.start_time,
      endTime: serTimeStore.serviceTimeData.end_time,
      query: queryData.query
    }
  };
  return post({}, "/logs/getSourceTypeList", request);
};
//日志级别
export const getLevelList = (queryData: any) => {
  const request = {
    request: {
      appid: useApplicationStore.appId,
      startTime: serTimeStore.serviceTimeData.start_time,
      endTime: serTimeStore.serviceTimeData.end_time,
      query: queryData.query
    }
  };
  return post({}, "/logs/getLevelList", request);
};

// 日志top10
export const getTop10Logs = (queryData: any) => {
  const request = {
    request: {
      appid: useApplicationStore.appId,
      startTime: queryData.startTime || serTimeStore.serviceTimeData.start_time,
      endTime: queryData.endTime || serTimeStore.serviceTimeData.end_time,
      query: queryData.query
    },
    pageRequest: {
      page: 1,
      rows: 10
    }
  };
  return post({}, "/logs/top10_keyword", request);
};
