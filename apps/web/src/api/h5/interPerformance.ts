import { post, get } from "@/http/axios";
import { serviceTimeStore } from "@/store/modules/global";
import { applicationStore } from "@/store/modules/application";
import { breadcrumbStore } from "@/store/modules/breadcurmb";
const serTimeStore = serviceTimeStore();
const useApplicationStore = applicationStore();
const usebreadcrumbStore = breadcrumbStore();
export interface IResponseBase {
  code: number;
  desc: string;
  value: number;
}
export interface IRequsetBase {
  startTime?: number;
  endTime?: number;
  appid?: string;
  appName?: string;
  rows?: number;
  sort?: string;
  order?: string;
  page?: number;
  appVersion?: string;
  httpStatusCode?: string | number;
  traceId?: string;
  net?: string;
  url?: string;
  method?: string;
  httpUrl?: string;
}
export interface IInterPerforList {
  code: number;
  desc: string;
  total: number;
  records: IInterPerforItem[];
}
export interface IAjaxListResponse {
  code: number;
  desc: string;
  total: number;
  records: IAjaxListItem[];
}

export interface IAjaxListItem {
  traceId: string;
  result: string | number;
  duration: string;
  country: string;
  province: string;
  os: string;
  ip: string;
  browser: string;
  id: string;
  time: string;
  userId: string;
}

export interface IInterPerforItem {
  count: number;
  errorRate: number;
  url: string;
  httpUrl: string;
  errorCount: number;
  avgDuration: string;
}

export interface IChartResponse {
  code: number;
  desc: string;
  entity: IEntity;
}

export interface IEntity {
  granularity: number;
  datas: IChartItem[];
}

export interface IChartItem {
  time: string;
  value: number;
}

export interface ISelectAppResponse {
  code: number;
  desc: string;
  records: string[];
}

const fetchInterPerformanceData = (url: string, data: IRequsetBase) => {
  const appId = useApplicationStore.appId || "";
  const appName = usebreadcrumbStore.appOption || "";
  return get<IResponseBase>({}, url, {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time,
    appid: appId,
    appName: appName
  });
};
// 请求总数
export const getInterCount = (data: IRequsetBase) => {
  return fetchInterPerformanceData("/h5/ajax/count", data);
};
// 平均耗时
export const getInterAvgDuration = (data: IRequsetBase) => {
  return fetchInterPerformanceData("/h5/ajax/avg_duration", data);
};
// 错误率
export const getInterErrorRate = (data: IRequsetBase) => {
  return fetchInterPerformanceData("/h5/ajax/error_rate", data);
};
export const getInterNums = (data: IRequsetBase) => {
  return fetchInterPerformanceData("/h5/ajax/error_count", data);
};

// 统计列表
export const getInterPerformanceList = (data: IRequsetBase) => {
  const appId = useApplicationStore.appId || "";
  const appName = usebreadcrumbStore.appOption || "";
  const requestData = {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time,
    appid: appId,
    appName: appName
  };
  return get<IInterPerforList>({}, "/h5/ajax/stat_list", requestData);
};
// 统计图
export const getH5RequestCount = (data: IRequsetBase) => {
  const appId = useApplicationStore.appId || "";
  const appName = usebreadcrumbStore.appOption || "";
  const requestData = {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time,
    appid: appId,
    appName: appName
  };
  return get<IChartResponse>({}, "/h5/ajax/stat_count", requestData);
};

export const getH5AvgDurationRate = (data: IRequsetBase) => {
  const appId = useApplicationStore.appId || "";
  const appName = usebreadcrumbStore.appOption || "";
  const requestData = {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time,
    appid: appId,
    appName: appName
  };
  return get<IChartResponse>({}, "/h5/ajax/stat_avg_duration", requestData);
};

export const getH5ErrorCount = (data: IRequsetBase) => {
  const appId = useApplicationStore.appId || "";
  const appName = usebreadcrumbStore.appOption || "";
  const requestData = {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time,
    appid: appId,
    appName: appName
  };
  return get<IChartResponse>({}, "/h5/ajax/stat_error_count", requestData);
};
// 网络请求列表
// /app/net/stat_list
export const getAjaxList = (data: IRequsetBase) => {
  const appId = useApplicationStore.appId || "";
  const appName = usebreadcrumbStore.appOption || "";
  const requestData = {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time,
    appid: appId,
    appName: appName
  };
  return get<IAjaxListResponse>({}, "/h5/ajax/list", requestData);
};

export const getOperators = (data: IRequsetBase) => {
  const appId = useApplicationStore.appId || "";
  const appName = usebreadcrumbStore.appOption || "";
  const requestData = {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time,
    appid: appId,
    appName: appName
  };
  return get<ISelectAppResponse>({}, "/h5/ajax/operators", requestData);
};
export const getOs = (data: IRequsetBase) => {
  const appId = useApplicationStore.appId || "";
  const appName = usebreadcrumbStore.appOption || "";
  const requestData = {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time,
    appid: appId,
    appName: appName
  };
  return get<ISelectAppResponse>({}, "/h5/ajax/os", requestData);
};
export const getProvinces = (data: IRequsetBase) => {
  const appId = useApplicationStore.appId || "";
  const appName = usebreadcrumbStore.appOption || "";
  const requestData = {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time,
    appid: appId,
    appName: appName
  };
  return get<ISelectAppResponse>({}, "/h5/ajax/provinces", requestData);
};
export const getBrowserNames = (data: IRequsetBase) => {
  const appId = useApplicationStore.appId || "";
  const appName = usebreadcrumbStore.appOption || "";
  const requestData = {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time,
    appid: appId,
    appName: appName
  };
  return get<ISelectAppResponse>({}, "/h5/ajax/browser_names", requestData);
};
