import { post, get } from "@/http/axios";
import { serviceTimeStore } from "@/store/modules/global";
const serviceTimeStoreInstance = serviceTimeStore();

const fetchServiceData = (url: string, data: any) => {
  return get({}, url, {
    ...data,
    startTime: serviceTimeStoreInstance.serviceTimeData.start_time,
    endTime: serviceTimeStoreInstance.serviceTimeData.end_time
  });
};

// TTFB平均时间
export const getAverageTTFB = (data: any) => {
  return fetchServiceData("/h5/page_perf/ttfb_avg_duration", data);
};

// DOM解析平均时间
export const getAverageDOMParsing = (data: any) => {
  return fetchServiceData("/h5/page_perf/dom_analysis_avg_duration", data);
};

// 页面加载平均时间
export const getAveragePageLoad = (data: any) => {
  return fetchServiceData("/h5/page_perf/page_load_avg_duration", data);
};

// 首字节平均时间
export const getAverageFirstByte = (data: any) => {
  return fetchServiceData("/h5/page_perf/first_pack_avg_duration", data);
};

// 资源加载平均时间
export const getAverageResourceLoad = (data: any) => {
  return fetchServiceData("/h5/page_perf/res_avg_duration", data);
};

// 统计页面加载平均耗时（图表）
export const getStatAveragePageLoad = (data: any) => {
  return fetchServiceData("/h5/page_perf/stat_page_load_avg_duration", data);
};

// 统计首字节平均耗时（图表）
export const getStatAverageFirstByte = (data: any) => {
  return fetchServiceData("/h5/page_perf/stat_first_pack_avg_duration", data);
};

// 统计TTFB平均耗时（图表）
export const getStatAverageTTFB = (data: any) => {
  return fetchServiceData("/h5/page_perf/stat_ttfb_avg_duration", data);
};

// 统计列表
export const getStartList = (data: any) => {
  return fetchServiceData("/h5/page_perf/stat_list", data);
};

// * 详情

// 接口列表
export const getDetailList = (data: any) => {
  return fetchServiceData("/h5/page_perf/list", data);
};

// 运营商（下拉框）
export const getOperators = (data: any) => {
  return fetchServiceData("/h5/page_perf/operators", data);
};

// 浏览器（下拉框）
export const getBrowserNames = (data: any) => {
  return fetchServiceData("/h5/page_perf/browser_names", data);
};

// 操作系统（下拉框）
export const getOS = (data: any) => {
  return fetchServiceData("/h5/page_perf/os", data);
};

// 省份（下拉框）
export const getProvinces = (data: any) => {
  return fetchServiceData("/h5/page_perf/provinces", data);
};
