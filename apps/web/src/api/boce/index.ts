import { get, post } from "@/http/axios";

import { serviceTimeStore } from "@/store/modules/global";
const serTimeStore = serviceTimeStore();
import { toMillis } from "@/utils/dateStr";
const fetchServiceData = (url: string, data: any) => {
  return post({}, url, {
    ...data,
    beginTime: toMillis(serTimeStore.serviceTimeData.start_time),
    endTime: toMillis(serTimeStore.serviceTimeData.end_time)
  });
};

// 创建拨测任务
export const createBatch = (data: {}) => {
  return post({}, "/boce/v2/create_task", data);
};

// 删除拨测任务
export const deleteTask = (data: {}) => {
  return post({}, "/boce/v2/delete_task", data);
};

// ❌ 查看详情
export const detailTask = (data: {}) => {
  return post({}, "/boce/v2/get_task_detail", data);
};

// 分页查询拨测任务列表
export const listTasks = (data: {}) => {
  return post({}, "/boce/v2/get_tasks", {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time
  });
};

// 恢复拨测任务
export const resumeTasks = (data: {}) => {
  return post({}, "/boce/v2/enable_tasks", data);
};

// 暂停任务
export const pauseTasks = (data: {}) => {
  return post({}, "/boce/v2/disable_tasks", data);
};

// ❌ 更新探测任务属性
export const updateProperties = (data: {}) => {
  return post({}, "/boce/v2/update_task", data);
};

// 查询预览结果
export const previewTasks = (data: {}) => {
  return post({}, "/boce/v2/query_overview", {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time
  });
};

// 查询图表数据
export const chartData = (data: {}) => {
  return post({}, "/boce/v2/query_chart", {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time
  });
};

// 查询详情列表数据
export const detailData = (data: {}) => {
  return post({}, "/boce/v2/task_results", {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time
  });
};

// 查询所有拨测任务
export const showAllTasks = (data: {}) => {
  return post({}, "/boce/v2/get_all_tasks", data);
};

// 查询拨测客户端列表
export const showAllClients = (data: {}) => {
  return get({}, "/boce/v2/client/list", data);
};

// 删除拨测客户端
export const delClient = (data: any) => {
  return get({}, `/boce/v2/client/delete/${data.id}`, data);
};

// 新增拨测客户端配置
export const addClient = (data: any) => {
  return post({}, "/boce/v2/client/add", data);
};

// 编辑拨测客户端配置
export const editClient = (data: any) => {
  return post({}, "/boce/v2/client/edit", data);
};
