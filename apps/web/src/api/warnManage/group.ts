import { get, post } from "@/http/axios";

// 告警通知对象组列表
export const getNotifierGroup = (data: any) => {
  return get({}, "/alarm/notifier_group/list", data);
};

// 新增告警通知对象组
export const addNotifierGroup = (data: any) => {
  return post({}, "/alarm/notifier_group/add", data);
};

// 修改告警通知组对象
export const editNotifierGroup = (data: any) => {
  return post({}, "/alarm/notifier_group/edit", data);
};

// 删除告警通知对象组
export const delNotifierGroup = (data: any) => {
  return get({}, `/alarm/notifier_group/delete/${data.id}`, { appid: data.appid });
};

// 加载对象组详情
export const detailNotifierGroup = (data: any) => {
  return get({}, `/alarm/notifier_group/detail/${data.id}`, { appid: data.appid });
};

// 查询当前组下的所有通知对象
export const getNotifiers = (data: any) => {
  return get({}, `/alarm/notifier_group/notifiers/${data.id}`, data);
};

// 为对象组绑定对象
export const getBindNotifier = (data: any) => {
  return post(
    {},
    `/alarm/notifier_group/bind_notifier/${data.id}?notifierIds=${data.notifierIds}&appid=${data.appid}`,
    {}
  );
};

// 告警通知对象列表
export const getNotifier = (data: any) => {
  return get({}, "/alarm/notifier/list", data);
};

// 分页查询当前对象组下的所有对象
export const getNotifierGroupNotifier = (data: any) => {
  return get({}, "/alarm/notifier/list_by_group", data);
};
