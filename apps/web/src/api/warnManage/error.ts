import { post, get } from "@/http/axios";
import { applicationStore } from "@/store/modules/application";
const useApplicationStore = applicationStore();
import { serviceTimeStore } from "@/store/modules/global";
const serTimeStore = serviceTimeStore();
// 通用函数，合并参数
const fetchServiceData = (url: string, data: any) => {
  return get({}, url, {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time
  });
};
//日志列表
export const getLogList = (data: any) => {
  return fetchServiceData("/log/error/handle/log/list", data);
};
//deepseek
export const deepseekChat = (data: any) => {
  return post({ timeout: 120000 }, "/deepseek/chat", data);
};
//知识库
export const handleErrorList = (data: any) => {
  const appid = useApplicationStore.appId;
  return get({}, "/log/error/handle/list/" + appid, data);
};
//提交
export const errorSava = (data: any) => {
  const appid = useApplicationStore.appId;
  return post({}, "/log/error/handle/save/" + appid, data);
};
// 获取知识库详情
export const getKnowledgeDetail = (id: string) => {
  const appid = useApplicationStore.appId;
  return get({}, `/log/error/handle/detail/${appid}/${id}`);
};

// 编辑知识库
export const editKnowledge = (data: any) => {
  return post({}, "/log/error/handle/edit", data);
};

// 删除知识库
export const deleteKnowledge = (id: string) => {
  const appid = useApplicationStore.appId;
  return get({}, `/log/error/handle/delete/${appid}/${id}`);
};
// 忽略错误信息
export const errorIgnore = (data: any) => {
  const appid = useApplicationStore.appId;
  return post({}, "/log/error/handle/ignore/" + appid, data);
};
//统计状态
export const statisticsCount = () => {
  const appid = useApplicationStore.appId;
  return get({}, "/log/error/handle/statisticsCount/" + appid);
};
