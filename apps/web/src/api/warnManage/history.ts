import { get, post } from "@/http/axios";
import { applicationStore } from "@/store/modules/application";
const useApplicationStore = applicationStore();
const appId = useApplicationStore.appId;
// const appId = "123456";

// 告警历史列表
export const getHistory = (data: any) => {
  return get({}, "/alarm/history/list", data);
};

// 修改状态
export const updateStatus = (data: any) => {
  return get(
    {},
    `/alarm/history/update_status/${data.id}?appid=${appId}&status=${data.status === 1 ? 0 : 1}`
  );
};

// 清空告警历史列表
export const deleteAll = () => {
  return get({}, `/alarm/history/delete_all?appid=${appId}`);
};

// 批量删除
export const deleteBatch = (ids: string) => {
  return get({}, `/alarm/history/delete_batch?appid=${appId}&ids=${ids}`);
};
