import { ILoginRequestData, ICaptchaResponse } from "./type";
import { IUserInfo } from "../user/type";
import { post, get } from "@/http/axios";

export const userLogin = (data: ILoginRequestData) => {
  return post<IUserInfo>({}, "/login", data);
};

export const getUserInfo = (data: {}) => {
  return get({}, "/principal", data);
};

// 获取验证码
export const getCaptcha = () => {
  return get<ICaptchaResponse>({}, "/captcha");
};
