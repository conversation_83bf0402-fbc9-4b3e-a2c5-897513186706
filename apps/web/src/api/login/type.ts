export interface IBaseResult {
  desc: string; // 描述
  code: number; // 状态码
}

export interface ILoginRequestData {
  aesKey: string;
  mobile: string; // 用户明
  password: string; // 密码
  code?: string; // 验证码
  captchaId?: string; // 验证码ID
}

export interface IUserInfo extends IBaseResult {
  entity: {
    id: number;
    name: string;
    mobile: string; // 用户名
    email: string;
    remark: string; //
    lockStatus: number;
    type: number;
    createdAt: string;
    updatedAt: string;
  };
}

// 验证码响应接口
export interface ICaptchaResponse extends IBaseResult {
  entity: {
    captchaId: string; // 验证码ID
    imageData: string; // 验证码BASE64图片数据
  };
  successful: boolean;
}
