import { get, del, post } from "@/http/axios";
import type {
  PageParams,
  WorkflowListResponse,
  CreateWorkflowRequest,
  CreateWorkflowResponse,
  DataSourceListResponse,
  PromptTemplateListResponse
} from "./types";

export const getWorkFlows = (data: PageParams): Promise<WorkflowListResponse> => {
  return get({}, "orchestrator/api/v1/workflows", data);
};

// 创建工作流
export const createWorkflow = (data: CreateWorkflowRequest): Promise<CreateWorkflowResponse> => {
  return post({}, "orchestrator/api/v1/workflows", data);
};

// 删除工作流
export const deleteWorkflow = (id: string): Promise<any> => {
  return del({}, `orchestrator/api/v1/workflows/${id}`, {});
};

// 执行工作流
export const executeWorkflow = (workflowId: string, params?: Record<string, any>): Promise<any> => {
  return post({}, `orchestrator/api/v1/workflows/${workflowId}/execute`, params || {});
};

//获取详情
export const detailWorkflow = (id: string): Promise<any> => {
  return get({}, `orchestrator/api/v1/workflows/${id}`, {});
};

// 获取执行记录
export const getWorkflowExecutions = (workflowId: string): Promise<any> => {
  return get({}, `orchestrator/api/v1/workflows/executions/${workflowId}`, {});
};

// 获取数据源列表
export const getDataSources = (): Promise<DataSourceListResponse> => {
  return get({}, "orchestrator/api/v1/datasources", {});
};

// 获取提示模板列表
export const getPromptTemplates = (): Promise<PromptTemplateListResponse> => {
  return get({}, "orchestrator/api/v1/prompt-template-groups/system", {});
};

//prompt列表
export const getPrompTtemplateGroups = (data: any) => {
  return get({}, "orchestrator/api/v1/prompt-template-groups", data);
};

// 新增prompt模板组
export const createPromptTemplateGroup = (data: { name: string; content: string }) => {
  return post({}, "orchestrator/api/v1/prompt-template-groups", data);
};

// 删除prompt模板组
export const deletePromptTemplateGroup = (group_id: string) => {
  return del({}, `orchestrator/api/v1/prompt-template-groups/${group_id}`, {});
};

//所有系统prompt模板
export const getSystemPromptTemplate = (data: any) => {
  return get({}, "orchestrator/api/v1/prompt-template-groups/system", data);
};
