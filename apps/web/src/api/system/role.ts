import { post, put, del, get } from "@/http/axios";

// 获取角色列表
export const getRoleList = (data: any) => {
  return post({}, "/sys/roles/page", data);
};

// 编辑角色信息
export const updateRole = (data: any) => {
  return put({}, `/sys/roles/${data.id}`, data);
};

// 查询可用角色ID列表
export const getAvailableRoleIds = (data: any) => {
  return post({}, `/sys/roles/ids`, data);
};

// 创建新角色
export const createRole = (data: any) => {
  return post({}, `/sys/roles/create`, data);
};

// 删除角色
export const deleteRole = (data: any) => {
  return del({}, `/sys/roles/${data.id}`, {});
};

// 分配用户
export const initialRole = (data: any) => {
  return put({}, `/sys/roles/${data.roleId}/assign-users`, data);
};

// 分配资源
export const assignRole = (data: any) => {
  return put({}, `/sys/roles/${data.roleId}/assign-resources`, data);
};

// 资源权限
export const getRoleResource = (data: any) => {
  return get({}, `/sys/roles/${data}/permissions`, {});
};

// 角色关联的用户
export const getRoleUser = (data: any) => {
  return get({}, `/sys/roles/${data.id}/users`);
};

// 角色关联的资源
export const getRoleResourceList = (data: any) => {
  return get({}, `/sys/roles/${data.id}/role_res`);
};

// 角色详情
export const getRoleDetail = (data: any) => {
  return get({}, `/sys/roles/${data.id}/detail`);
};
