import { post, put, del } from "@/http/axios";

// 获取租户列表
export const getTenantList = (data: any) => {
  return post({}, "/sys/tenants/page", data);
};

// 编辑租户信息
export const updateTenant = (data: any) => {
  return put({}, `/sys/tenants/${data.id}/modify`, data);
};

// 查询可用租户ID列表
export const getAvailableTenantIds = (data: any) => {
  return post({}, `/sys/tenants/ids`, data);
};

// 创建新租户
export const createTenant = (data: any) => {
  return post({}, `/sys/tenants/create`, data);
};

// 删除租户
export const deleteTenant = (data: any) => {
  return del({}, `/sys/tenants/${data.id}`, {});
};

// 初始化租户信息
export const initialTenant = (data: any) => {
  return put({}, `/sys/tenants/${data.id}/init_sql_script`, data);
};
