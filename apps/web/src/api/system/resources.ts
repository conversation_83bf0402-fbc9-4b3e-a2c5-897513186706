import { post, put, del, get } from "@/http/axios";

// 获取资源列表
export const getResourceList = (data: any) => {
  return get({}, "/sys/resources/page", data);
};

// 新增资源
export const createResource = (data: any) => {
  return post({}, "/sys/resources/create", data);
};

// 修改资源
export const updateResource = (data: any) => {
  return put({}, `/sys/resources/${data.id}/modify`, data);
};

// 菜单路由
export const getResourceMenu = (data: any) => {
  return get({}, "/sys/resources/router", data);
};

// 删除资源
export const deleteResource = (data: any) => {
  return del({}, `/sys/resources/${data.id}`, {});
};
