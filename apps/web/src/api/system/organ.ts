import { get, post, del, put } from "@/http/axios";

//单位列表
export function organList(params: any) {
  return get({}, "/sys/organ/list", params);
}
//根据ID加载详情
export function getOrgan(id: number) {
  return get({}, "/sys/organ/detail/" + id);
}
//新增单位
export function insertOrgan(data: any) {
  return post({}, "/sys/organ/add", data);
}
//修改单位
export function updateOrgan(data: any) {
  return post({}, "/sys/organ/edit", data);
}
//删除单位
export function delOrgan(id: number) {
  return get({}, "/sys/organ/delete/" + id);
}

// 组织架构列表
export const getOrganList = (data: any) => {
  return get({}, "/sys/organ/trees", data);
};

// 创建组织架构
export const createOrganList = (data: any) => {
  return post({}, "/sys/organ/add", data);
};

// 编辑组织架构
export const editOrganList = (data: any) => {
  return put({}, "/sys/organ/edit", data);
};

// 组织架构详情
export const getOrganDetail = (data: any) => {
  return get({}, `/sys/organ/detail/${data.id}`, data);
};

// 删除组织架构
export const delOrganList = (data: any) => {
  return del({}, `/sys/organ/delete/${data.id}`, data);
};

// 获取单位下的应用列表
export const getOrganAppList = (id: number, params: { page: number; rows: number }) => {
  return get({}, `/sys/organ/app/list/${id}`, params);
};

// 获取单位下的用户列表
export const getOrganUserList = (
  id: number,
  params: { page: number; rows: number; keyword?: string }
) => {
  return get({}, `/sys/organ/user/list/${id}`, params);
};
