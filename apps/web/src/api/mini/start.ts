import { get } from "@/http/axios";
import { serviceTimeStore } from "@/store/modules/global";
const serTimeStore = serviceTimeStore();
const fetchServiceData = (url: string, data: any) => {
  return get({}, url, {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time
  });
};
// 启动性能概览
export const getStartOverviewCount = (data: any) => {
  return fetchServiceData("/mini_program/launch_perf/overview", data);
};

// 启动数统计
export const getStatCount = (data: any) => {
  return fetchServiceData("/mini_program/launch_perf/stat_count", data);
};

// 慢启动统计
export const getStatSlowCount = (data: any) => {
  return fetchServiceData("/mini_program/launch_perf/stat_slow_count", data);
};

// 统计平均耗时
export const getStatAvgDuration = (data: any) => {
  return fetchServiceData("/mini_program/launch_perf/stat_avg_duration", data);
};

// 启动性能列表
export const getStartList = (data: any) => {
  return fetchServiceData("/mini_program/launch_perf/list", data);
};
