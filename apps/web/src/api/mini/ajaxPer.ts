import { post, get } from "@/http/axios";
import { serviceTimeStore } from "@/store/modules/global";
import { applicationStore } from "@/store/modules/application";
import { breadcrumbStore } from "@/store/modules/breadcurmb";
const serTimeStore = serviceTimeStore();
const useApplicationStore = applicationStore();
const usebreadcrumbStore = breadcrumbStore();
export interface IResponseBase {
  code: number;
  desc: string;
  value: number;
}
export interface IRequsetBase {
  startTime?: number;
  endTime?: number;
  appid?: string;
  appName?: string;
  rows?: number;
  sort?: string;
  order?: string;
  page?: number;
  appVersion?: string;
  httpStatusCode?: string | number;
  traceId?: string;
  net?: string;
  url?: string;
  method?: string;
  httpUrl?: string;
}
export interface IInterPerforList {
  code: number;
  desc: string;
  total: number;
  records: IInterPerforItem[];
}
export interface IAjaxListResponse {
  code: number;
  desc: string;
  total: number;
  records: IAjaxListItem[];
}

export interface IAjaxListItem {
  traceId: string;
  result: string | number;
  duration: string;
  country: string;
  province: string;
  os: string;
  ip: string;
  browser: string;
  id: string;
  time: string;
  userId: string;
  statusCode: number;
  errMsg: string;
}

export interface IInterPerforItem {
  count: number;
  errorRate: number | string;
  url: string;
  httpUrl: string;
  errorCount: number;
  avgDuration: string;
}

export interface IChartResponse {
  code: number;
  desc: string;
  entity: IEntity;
}

export interface IEntity {
  granularity: number;
  datas: IChartItem[];
}

export interface IChartItem {
  time: string;
  value: number;
}

export interface ISelectAppResponse {
  code: number;
  desc: string;
  records: string[];
}
export interface IOverviewResponse {
  code: number;
  desc: string;
  entity: IOverviewEntity;
}
export interface IOverviewEntity {
  requestCount: number;
  errorRate: number;
  errorCount: number;
  avgDuration: number;
}

const fetchInterPerformanceData = (url: string, data: IRequsetBase) => {
  const appId = useApplicationStore.appId;
  const mpAppid = usebreadcrumbStore.appOption;
  return get<IOverviewResponse>({}, url, {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time,
    appid: appId,
    mpAppid: mpAppid
  });
};

export const getOverviewCount = (data: IRequsetBase) => {
  return fetchInterPerformanceData("/mini_program/http/overview", data);
};

// 统计列表
export const getInterPerformanceList = (data: IRequsetBase) => {
  const appId = useApplicationStore.appId;
  const mpAppid = usebreadcrumbStore.appOption;
  const requestData = {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time,
    appid: appId,
    mpAppid: mpAppid
  };
  return get<IInterPerforList>({}, "/mini_program/http/stat_list", requestData);
};
// 统计图
export const getMiniRequestCount = (data: IRequsetBase) => {
  const appId = useApplicationStore.appId;
  const mpAppid = usebreadcrumbStore.appOption;
  const requestData = {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time,
    appid: appId,
    mpAppid: mpAppid
  };
  return get<IChartResponse>({}, "/mini_program/http/stat_count", requestData);
};

export const getMiniAvgDurationRate = (data: IRequsetBase) => {
  const appId = useApplicationStore.appId;
  const mpAppid = usebreadcrumbStore.appOption;
  const requestData = {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time,
    appid: appId,
    mpAppid: mpAppid
  };
  return get<IChartResponse>({}, "/mini_program/http/stat_avg_duration", requestData);
};

export const getMiniErrorCount = (data: IRequsetBase) => {
  const appId = useApplicationStore.appId;
  const mpAppid = usebreadcrumbStore.appOption;
  const requestData = {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time,
    appid: appId,
    mpAppid: mpAppid
  };
  return get<IChartResponse>({}, "/mini_program/http/stat_error_count", requestData);
};
// 网络请求列表
// /app/net/stat_list
export const getMiniAjaxList = (data: IRequsetBase) => {
  const appId = useApplicationStore.appId;
  const mpAppid = usebreadcrumbStore.appOption;
  const requestData = {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time,
    appid: appId,
    mpAppid: mpAppid
  };
  return get<IAjaxListResponse>({}, "/mini_program/http/list", requestData);
};

export const getDevice = (data: IRequsetBase) => {
  const appId = useApplicationStore.appId;
  const mpAppid = usebreadcrumbStore.appOption;
  const requestData = {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time,
    appid: appId,
    mpAppid: mpAppid
  };
  return get<ISelectAppResponse>({}, "/mini_program/http/model", requestData);
};
export const getOs = (data: IRequsetBase) => {
  const appId = useApplicationStore.appId;
  const mpAppid = usebreadcrumbStore.appOption;
  const requestData = {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time,
    appid: appId,
    mpAppid: mpAppid
  };
  return get<ISelectAppResponse>({}, "/mini_program/http/os", requestData);
};
export const getProvinces = (data: IRequsetBase) => {
  const appId = useApplicationStore.appId;
  const mpAppid = usebreadcrumbStore.appOption;
  const requestData = {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time,
    appid: appId,
    mpAppid: mpAppid
  };
  return get<ISelectAppResponse>({}, "/mini_program/http/provinces", requestData);
};
