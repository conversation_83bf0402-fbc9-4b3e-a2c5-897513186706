import { post, get } from "@/http/axios";
import { serviceTimeStore } from "@/store/modules/global";
const serTimeStore = serviceTimeStore();
import {
  IServiceIndicatorRequest,
  IServiceIndicatorResponse,
  IRequestCountTopResponse,
  IErrorCountTopResponse,
  IAvgDurationTopResponse
} from "./types";
export interface IOverviewInfo {
  code: number;
  desc: string;
  entity: IOverviewItem;
}

export interface IOverviewItem {
  requestCount: number;
  serviceCount: number;
  errorCount: number;
  avgDuration: number;
}

// 通用函数，合并参数
const fetchServiceData = (url: string, data: IServiceIndicatorRequest) => {
  return get<IServiceIndicatorResponse>({}, url, {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time
  });
};

//错误日志数
export const getErrorLogCount = (data: IServiceIndicatorRequest) => {
  return fetchServiceData("/service/overview/error_log_count", data);
};

export const getOverviewInfo = (data: IServiceIndicatorRequest) => {
  const requestData = {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time
  };
  return get<IOverviewInfo>({}, "/service/overview/summary", requestData);
};

// 请求数服务排行（Top5）
export const getRequestCountTopList = (data: IServiceIndicatorRequest) => {
  const requestData = {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time
  };
  return get<IRequestCountTopResponse>({}, "/service/overview/request_count_top5", requestData);
};

// 错误数服务排行（Top5）
export const getErrorCountTopList = (data: IServiceIndicatorRequest) => {
  const requestData = {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time
  };
  return get<IErrorCountTopResponse>({}, "/service/overview/error_count_top5", requestData);
};

// 平均耗时服务排行（Top5）
export const getAvgDurationTopList = (data: IServiceIndicatorRequest) => {
  const requestData = {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time
  };
  return get<IAvgDurationTopResponse>({}, "/service/overview/avg_duration_top5", requestData);
};
//请求数Endpoint排行（Top5）
export const getEndpointCountTopList = (data: IServiceIndicatorRequest) => {
  const requestData = {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time
  };
  return get<IRequestCountTopResponse>({}, "/service/endpoint/request_count_top5", requestData);
};
//错误数Endpoint排行（Top5）
export const getEndpointErrorCountTopList = (data: IServiceIndicatorRequest) => {
  const requestData = {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time
  };
  return get<IErrorCountTopResponse>({}, "/service/endpoint/error_count_top5", requestData);
};
//平均耗时Endpoint排行（Top5）
export const getEndpointAvgDurationTopList = (data: IServiceIndicatorRequest) => {
  const requestData = {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time
  };
  return get<IAvgDurationTopResponse>({}, "/service/endpoint/avg_duration_top5", requestData);
};
