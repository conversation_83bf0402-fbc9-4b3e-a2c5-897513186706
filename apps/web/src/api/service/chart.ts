import { get } from "@/http/axios";
import { serviceTimeStore } from "@/store/modules/global";
const serTimeStore = serviceTimeStore();
// 通用函数，合并参数
const fetchServiceData = (url: string, data: any) => {
  return get({}, url, {
    ...data,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time
  });
};
//请求数统计（图表）
export const getStatRequestCount = (data: any) => {
  return fetchServiceData("/service/overview/stat_request_count", data);
};
//错误数统计（图表）
export const getStatErrorCount = (data: any) => {
  return fetchServiceData("/service/overview/stat_error_count", data);
};
//平均耗时统计（图表）
export const getStatAvgCount = (data: any) => {
  return fetchServiceData("/service/overview/stat_avg_duration", data);
};
