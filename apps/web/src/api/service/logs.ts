import { get, post } from "@/http/axios";
import { serviceTimeStore } from "@/store/modules/global";
import { applicationStore } from "@/store/modules/application";
const useApplicationStore = applicationStore();
const serTimeStore = serviceTimeStore();
interface GetLogsListParams {
  page: number;
  rows: number;
  appid: string;
  startTime: string;
  endTime: string;
  serviceName?: string;
  traceId?: string;
  severityText?: string;
  scopeName?: string;
  instanceId?: string;
  body?: string;
}
interface GetLogsSeveritysParams {
  appid: string;
  startTime: string;
  endTime: string;
}
interface GetLogsServicesParams {
  appid: string;
  startTime: string;
  endTime: string;
}
// 获取日志列表
export const logsList = (data: GetLogsListParams) => {
  const requestData = {
    ...data,
    appid: useApplicationStore.appId,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time
  };
  return post({}, "/service/logs/list", requestData, true);
};

// 获取日志级别
export const logsSeveritys = (data: GetLogsSeveritysParams) => {
  const requestData = {
    ...data,
    appid: useApplicationStore.appId,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time,
    sourceType: "service"
  };
  return post({}, "/service/logs/severitys", requestData, true);
};

// 获取日志服务名
export const logsServices = (data: GetLogsServicesParams) => {
  const requestData = {
    ...data,
    appid: useApplicationStore.appId,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time,
    sourceType: "service"
  };
  return post({}, "/service/logs/services", requestData, true);
};
