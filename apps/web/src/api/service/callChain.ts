import { get, post } from "@/http/axios";
import { serviceTimeStore } from "@/store/modules/global";
const serTimeStore = serviceTimeStore();
import { applicationStore } from "@/store/modules/application";
const useApplicationStore = applicationStore();

interface GetCallChainListParams {
  page: number;
  rows: number;
  appid: string;
  startTime: string;
  endTime: string;
  serviceName?: string;
  status?: string;
  endpoint?: string;
  durationMin?: number;
  durationMax?: number;
  sort?: string;
  order?: string;
}

// 获取调用链列表
export const callChainList = (data: GetCallChainListParams) => {
  const requestData = {
    ...data,
    appid: useApplicationStore.appId,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time
  };
  return post({}, "/service/traces/list", requestData, true);
};

// 获取状态统计
export const callChainStatus = (data: any) => {
  const requestData = {
    ...data,
    appid: useApplicationStore.appId,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time
  };
  return post({}, "/service/traces/status_stat", requestData, true);
};

// 获取调用链详情
export const callChainTraces = (data: any) => {
  const requestData = {
    ...data,
    appid: useApplicationStore.appId
  };
  return post({}, "/service/traces/traces", requestData, true);
};

// 获取调用链详情
export const callChainSpan = (data: any) => {
  const requestData = {
    ...data,
    appid: useApplicationStore.appId
  };
  return post({}, "/service/traces/span", requestData, true);
};

// 获取调用链服务名列表
export const callChainServices = (data: any) => {
  const requestData = {
    ...data,
    appid: useApplicationStore.appId,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time
  };
  return post({}, "/service/traces/services", requestData, true);
};

// 获取调用链详情日志
export const callChainSpanList = (data: any) => {
  const requestData = {
    ...data,
    appid: useApplicationStore.appId
  };
  return post({}, "/service/logs/list_by_span", requestData, true);
};

// 获取调用链详情事件
export const callChainEventList = (data: any) => {
  const requestData = {
    ...data,
    appid: useApplicationStore.appId
  };
  return post({}, "/service/event/list", requestData, true);
};
