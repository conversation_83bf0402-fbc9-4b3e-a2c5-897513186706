import { post, get, del } from "@/http/axios";
import { applicationStore } from "@/store/modules/application";
const useApplicationStore = applicationStore();
const appid = useApplicationStore.appId;
// 查看节点信息
export const networkView = data => {
  return get({}, `/ext/topo-group/all/${data}`, "");
  // return get({}, `/ext/topo-group/list/${appid}`, data);
};
// 查看所有网段信息
export const topoView = data => {
  return get({}, `/ext/topo/${data}`, "");
  // return get({}, `/ext/topo-group/list/${data}`, "");
  // return get({}, `/ext/topo-nodes/network/segment/list/${appid}`, data);
};
// 编辑节点
export const editTopo = data => {
  return post({}, `/ext/topo-nodes/edit/${appid}`, data);
};
// 删除节点
export const delTopo = data => {
  return del({}, `/ext/topo-nodes/delete/${data}`, data);
};
// 查看详情
export const detailTopo = data => {
  return get({}, `/ext/topo-nodes/detail/${data}`, "");
};
// 保存
export const saveTopo = data => {
  return post({}, `/ext/topo/save/${data.appid}`, data);
};
