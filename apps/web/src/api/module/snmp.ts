import { get } from "@/http/axios";
import { applicationStore } from "@/store/modules/application";
import { serviceTimeStore } from "@/store/modules/global";
import { serviceNameStore } from "@/store/modules/service";

const serTimeStore = serviceTimeStore();
const useApplicationStore = applicationStore();
const useServiceNameStore = serviceNameStore();

// 通用函数，合并参数
const fetchData = (url: string, data: any) => {
  return get({}, url, {
    ...data,
    appid: useApplicationStore.appId,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time
  });
};

//查询设备列表
export const getDevicesList = (data: any) => {
  return fetchData("/snmp/devices/list", data);
};

//获取设备详情
export const getDeviceDetail = (data: any) => {
  return fetchData(`/snmp/devices/${useServiceNameStore.serviceName}/detail`, data);
};

//获取设备指标趋势
export const getDevicesTrends = (data: any) => {
  return fetchData(`/snmp/devices/${useServiceNameStore.serviceName}/trends`, data);
};
