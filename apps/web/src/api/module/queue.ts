import { get } from "@/http/axios";
import { applicationStore } from "@/store/modules/application";
import { serviceTimeStore } from "@/store/modules/global";
const serTimeStore = serviceTimeStore();
const useApplicationStore = applicationStore();
// 通用函数，合并参数
const fetchServiceData = (url: string, data: any) => {
  return get({}, url, {
    ...data,
    appid: useApplicationStore.appId,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time
  });
};
//分页查询Kafka集群
export const getKafkaMetricsList = (data: any) => {
  return fetchServiceData("/kafka/metrics/list", data);
};
//分页查询Kafka Topic
export const getTopicsPage = (data: any) => {
  return fetchServiceData("/kafka/metrics/topics/page", data);
};
//Consumer Group分页查询
export const getGroupsPage = (data: any) => {
  return fetchServiceData("/kafka/metrics/groups/page", data);
};
//获取Kafka监控仪表盘
export const getMetricsDashboard = (data: any) => {
  return fetchServiceData("/kafka/metrics/dashboard", data);
};
//Topic多指标趋势图表
export const getTopicMultiTrend = (data: any) => {
  return fetchServiceData("/kafka/metrics/topic/multi-trend", data);
};
//Group多指标趋势图表
export const getGroupMultiTrend = (data: any) => {
  return fetchServiceData("/kafka/metrics/group/multi-trend", data);
};
