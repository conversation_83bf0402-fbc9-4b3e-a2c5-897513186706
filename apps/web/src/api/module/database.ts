import { get, post } from "@/http/axios";
import { serviceTimeStore } from "@/store/modules/global";
import { applicationStore } from "@/store/modules/application";
const serTimeStore = serviceTimeStore();
const useApplicationStore = applicationStore();
// 通用函数，合并参数
const fetchServiceData = (url: string, data: any) => {
  return get({}, url, {
    ...data,
    appid: useApplicationStore.appId,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time
  });
};
const fetchServiceDataPOST = (url: string, data: any) => {
  return post({}, url, {
    ...data,
    appid: useApplicationStore.appId,
    startTime: serTimeStore.serviceTimeData.start_time,
    endTime: serTimeStore.serviceTimeData.end_time
  });
};
//按条件查询关键信息列表
export const getMysqlList = (data: any) => {
  return fetchServiceData("/mysql/metrics/list", data);
};
//查询实例图表信息
export const getMysqlDetailAgg = (data: any) => {
  return fetchServiceData("/mysql/metrics/agg-points", data);
};

// 获取MySQL监控仪表板数据
export const getMysqlDashboardData = (data: any) => {
  return fetchServiceData("/mysql/metrics/dashboard", data);
};
