import { defineStore } from "pinia";

export const serviceNameStore = defineStore(
  "serviceName",
  () => {
    const serviceName = ref("");
    const breadcrumb = ref("");
    const alias = ref("");

    const setServiceName = (val: string) => {
      serviceName.value = val;
    };
    const setAlias = (val: string) => {
      // 新增设置别名方法
      alias.value = val;
    };
    const setBreadcrumb = (val: string) => {
      breadcrumb.value = val;
    };

    const clearServiceName = () => {
      sessionStorage.removeItem("serviceName");
    };

    return {
      serviceName,
      setBreadcrumb,
      setServiceName,
      setAlias,
      clearServiceName,
      alias
    };
  },
  {
    persist: {
      storage: window.sessionStorage,
      key: "serviceName"
    }
  }
);
