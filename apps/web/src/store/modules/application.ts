import { defineStore } from "pinia";
export const applicationStore = defineStore(
  "appParams",
  () => {
    const appName = ref("");
    const appId = ref("");
    const organId = ref("");
    const setAppName = (val: string) => {
      appName.value = val;
    };
    const setAppId = (val: string) => {
      appId.value = val;
    };
    const setOrganId = (val: string) => {
      organId.value = val;
    };
    return {
      appName,
      appId,
      organId,
      setAppName,
      setAppId,
      setOrganId
    };
  },
  {
    persist: {
      storage: window.sessionStorage,
      key: "appParams"
    }
  }
);
