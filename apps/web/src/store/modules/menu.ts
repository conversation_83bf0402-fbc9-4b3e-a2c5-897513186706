// store/modules/menu.ts
import { defineStore } from "pinia";
import { RouteRecordRaw } from "vue-router";

export const useMenuStore = defineStore("menu", {
  state: () => ({
    cachedMenu: [] as RouteRecordRaw[]
  }),
  actions: {
    loadFromLocalStorage() {
      const local = localStorage.getItem("cachedMenu");
      this.cachedMenu = local ? JSON.parse(local) : [];
    },
    setMenu(menu: RouteRecordRaw[]) {
      this.cachedMenu = menu;
      localStorage.setItem("cachedMenu", JSON.stringify(menu));
    }
  }
});
