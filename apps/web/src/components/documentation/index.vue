<template>
  <div class="doc-float" v-if="!isLoginPage">
    <el-popover
      placement="top-start"
      :width="500"
      trigger="click"
      v-model:visible="visible"
      popper-class="doc-popover"
    >
      <template #reference>
        <el-button circle plain class="doc-btn" title="操作指南" :draggable="true">
          <el-tooltip content="操作指南" effect="light">
            <el-icon :size="28">
              <InfoFilled />
            </el-icon>
          </el-tooltip>
        </el-button>
      </template>

      <!-- 内容区域 -->
      <div class="doc-content">
        <div class="doc-header">
          <span class="doc-title">操作指南 - {{ breadcrumbTitle }}</span>
          <el-icon class="doc-close" @click="visible = false">
            <Close />
          </el-icon>
        </div>
        <div v-html="guideContent"></div>
      </div>
    </el-popover>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { InfoFilled } from "@element-plus/icons-vue";
import { guideContents } from "./index";

const visible = ref(false);
const route = useRoute();
const router = useRouter();

// 获取当前页面的指南内容
const currentGuide = computed(() => {
  const routeName = route.name?.toString() || "";
  return guideContents[routeName] || guideContents.default;
});

const guideContent = computed(() => currentGuide.value.content);
const isLoginPage = computed(() => route.name === "login");

// 生成层级标题
const breadcrumbTitle = computed(() => {
  const currentPath = route.path;
  const pathSegments = currentPath.split("/").filter(Boolean);

  // 获取完整层级关系
  const breadcrumbItems = [];
  let accumulatedPath = "";

  for (const segment of pathSegments) {
    accumulatedPath += `/${segment}`;
    const matchedRoute = router.getRoutes().find(r => r.path === accumulatedPath);
    if (matchedRoute?.meta?.title) {
      breadcrumbItems.push({
        path: accumulatedPath,
        title: matchedRoute.meta.title
      });
    }
  }

  // 拼接标题
  const maxLevel = 3;
  const itemsToShow = breadcrumbItems.slice(-maxLevel);
  return itemsToShow.map(item => item.title).join(" > ");
});
// 监听路由变化，自动隐藏弹出框
watch(
  () => route.name,
  () => {
    visible.value = false;
  }
);

// 实现拖动逻辑
const makeElementDraggable = (el: HTMLElement) => {
  el.style.position = "absolute";
  el.onmousedown = function (e: MouseEvent) {
    e.preventDefault();
    const disX = e.clientX - el.offsetLeft;
    const disY = e.clientY - el.offsetTop;

    const onMouseMove = (e: MouseEvent) => {
      el.style.left = e.clientX - disX + "px";
      el.style.top = e.clientY - disY + "px";
    };

    document.addEventListener("mousemove", onMouseMove);

    document.onmouseup = function () {
      document.removeEventListener("mousemove", onMouseMove);
      document.onmouseup = null;
    };
  };
};

// 在组件挂载时应用拖动逻辑
onMounted(() => {
  const button = document.querySelector(".doc-btn") as HTMLElement;
  if (button) {
    makeElementDraggable(button);
  }
});
</script>

<style scoped>
/* 原有样式保持不变 */
.doc-float {
  position: fixed;
  bottom: 200px;
  right: 40px;
  z-index: 9999;
}

.doc-btn {
  position: absolute;
  cursor: move;
  background-color: transparent;
  border: 1px solid #eeeeee;
  color: rgba(64, 158, 255, 0.5);
  box-shadow: none;
  width: 50px;
  height: 50px;
  transition: all 0.3s ease;
}

.doc-btn:hover {
  background-color: #fff;
  border: 1px solid #dcdfe6;
  color: #409eff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.doc-content {
  font-size: 14px;
  color: #333;
  line-height: 1.6;
  max-height: 500px;
  overflow-y: auto;
  padding-right: 8px;
}

.doc-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.doc-title {
  font-weight: bold;
  font-size: 16px;
  color: #409eff;
}

.doc-close {
  cursor: pointer;
  font-size: 18px;
  color: #999;
  transition: color 0.2s;
}
.doc-close:hover {
  color: #333;
}

/* 弹出框样式 */
:deep(.doc-popover) {
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 列表样式 */
:deep(.doc-content ul) {
  padding-left: 20px;
  margin: 12px 0;
}

:deep(.doc-content li) {
  margin-bottom: 8px;
}

:deep(.doc-content strong) {
  color: #333;
}
</style>
