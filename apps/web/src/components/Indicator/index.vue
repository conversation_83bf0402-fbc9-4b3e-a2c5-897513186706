<template>
  <div class="indicator-wrapper">
    <div v-if="!trafficType" class="indicator-wrapper-end" :style="{ color: color }">
      <span class="indicator-wrapper-desc">{{
        errorInfo ? errorInfo : formatNums(Number(value)).fixValue
      }}</span>
      <span class="text-lg">{{ formatNums(Number(value)).unit }}</span>
    </div>
    <div v-else-if="trafficType" class="indicator-wrapper-end" :style="{ color: color }">
      <span class="indicator-wrapper-desc">{{
        errorInfo ? errorInfo : convertBytes(Number(value)).fixValue
      }}</span>
      <span class="text-lg">{{ convertBytes(Number(value)).unit }}</span>
    </div>
    <div class="indicator-wrapper-unit">{{ unit }}</div>
  </div>
</template>
<script lang="ts" setup>
import { formatNums } from "@/utils/formatStr";
import { convertBytes } from "@/utils/trafficStr";
const props = defineProps({
  // 标题
  value: {
    type: [String, Number],
    default: () => "" // 使用函数返回默认值，以确保正确的类型推断
  },
  // 单位
  unit: {
    type: String,
    default: ""
  },
  // 颜色
  color: {
    type: String,
    default: "#3275f9"
  },
  // 如果外层接口报错，这里的默认信息
  errorInfo: {
    type: String,
    default: ""
  },
  // 显示流量单位
  trafficType: {
    type: Boolean,
    default: false
  }
});
</script>
<style lang="scss" scoped>
.indicator-wrapper {
  padding: 26px;
  border: 1px solid rgba(225, 228, 233, 1);
  text-align: center;
  // max-width: 500px;
  border-radius: 2px;
  width: 100%;
  &-end {
    font-size: 40px;
    font-weight: normal;
    font-weight: 500;
  }
  &-unit {
    font-size: 16px;
    font-weight: 400;
    color: #999999;
    margin-top: 5px;
    text-align: center;
    height: 18px;
  }
  &-desc {
    font-size: 35px;
  }
}
</style>
