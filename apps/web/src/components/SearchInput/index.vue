<template>
  <div class="SearchInput">
    <el-input
      style="width: 240px"
      placeholder="请输入关键字"
      v-model="trimmedKeywords"
      clearable
      @input="searchChange"
      @change="enterChange"
      aria-label="Search input"
    ></el-input>
    <el-button
      type="primary"
      style="margin-left: 15px"
      @click="handleSearch"
      :icon="Search"
      aria-label="Search button"
    >
      搜索
    </el-button>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { Search } from "@element-plus/icons-vue";

const emit = defineEmits<{
  (e: "search", keywords: string): void;
  (e: "searchChange", keywords: string): void;
  (e: "enterChange", keywords: string): void;
}>();

const keywords = ref<string>("");

const trimmedKeywords = computed({
  get: () => keywords.value.trim(),
  set: value => {
    keywords.value = value.trim();
  }
});

const handleSearch = () => {
  emit("search", trimmedKeywords.value);
};

const searchChange = () => {
  emit("searchChange", trimmedKeywords.value);
};
const enterChange = () => {
  emit("enterChange", trimmedKeywords.value);
};
</script>
