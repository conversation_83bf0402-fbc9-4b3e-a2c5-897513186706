export interface TitleStyle {
  fontSize: number;
  fontWeight: string;
  color: string;
}

export interface Title {
  text: string;
  x: string;
  y: string;
  textStyle: TitleStyle;
  subTextStyle: TitleStyle;
}

export interface AxisPointerLineStyle {
  width: number;
  color: string;
}

export interface AxisPointer {
  lineStyle: AxisPointerLineStyle;
  type?: string;
}

export interface Tooltip {
  trigger: string;
  axisPointer: AxisPointer;
  formatter: (params: any) => string;
}

export interface GraphicShape {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface GraphicStyle {
  fill: string;
}

export interface Graphic {
  type: string;
  shape: GraphicShape;
  style: GraphicStyle;
  z: number;
}

export interface Grid {
  left: string;
  right: string;
  bottom: string;
  top: string;
  containLabel: boolean;
}

export interface Legend {
  show?: boolean;
  orient?: string;
  align?: string;
  top?: string;
  type?: string;
}

export interface AxisLabel {
  formatter: (value: string) => string;
  align: string;
  interval: number;
}

export interface XAxis {
  type: string;
  data: string[];
  axisLabel: AxisLabel;
}

export interface YAxis {
  type: string;
}

export interface SeriesItem {
  name: string;
  type: string;
  color: string;
  stack?: string;
  smooth?: boolean;
  areaStyle?: object;
  data: number[];
}

export interface ChartOptions {
  title: Title;
  tooltip: Tooltip;
  graphic: Graphic[];
  grid: Grid;
  legend?: Legend;
  xAxis: XAxis;
  yAxis: YAxis;
  series: SeriesItem[];
}

export interface ChartParams {
  typ?: number;
  color: string[];
  titleType: string;
  originalTimes: string[];
  seriesData: number[][];
  type?: string;
  name?: string;
  names?: string[];
  legend?: Legend;
  numberType?: boolean;
  areaStyle?: boolean;
  stack?: boolean;
  enableDataZoom?: boolean;
  setGrid?: Legend;
  onClick?: (params: any) => void;
  titleShow?: boolean;
  grid?: Grid;
  tooltip?: Tooltip;
}
