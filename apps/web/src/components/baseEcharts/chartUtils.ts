export const getTimeValue = (typ: number): string | undefined => {
  switch (typ) {
    case 0:
      return "按秒";
    case 1:
      return "按分钟";
    case 2:
      return "按小时";
    case 3:
      return "按天";
    case 4:
      return "按月";
    default:
      return undefined;
  }
};

export function revertArray(arr: string[]): string[] {
  return arr.map(item => {
    const parts = item.split(" ");
    if (parts.length === 1) {
      return parts[0];
    }
    const [date, time] = parts;
    return `${time} ${date}`;
  });
}
