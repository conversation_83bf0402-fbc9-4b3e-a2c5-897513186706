<template>
  <div v-if="cachedViews.length > 0" class="flex justify-between items-start date-back mt-1">
    <div v-if="isSpecialRouter" style="width: 60%">
      <AppBreadBack
        :key="appBreadBackKey"
        @goBack="goBack"
        :showBackBtn="showBackBtn"
        @changeOption="changeOptionView"
        :options="options"
      />
    </div>
    <template v-else>
      <div v-if="showBackBtn" class="flex items-center date-back-title">
        <!-- <el-icon @click="goBack" class="mr-4 cursor-pointer text-3xl"><Back /></el-icon> -->
        <svg-icon name="back" @click="goBack" class="mr-3 cursor-pointer text-xl" />
        <div class="text-xl mr-3 date-back-desc mt--5px">{{ serviceNameWithAlias }}</div>
      </div>
      <div v-else class="flex items-center date-back-title">
        <div class="text-large mr-3 date-back-desc">{{ breadcrumb }}</div>
      </div>
    </template>

    <Date style="box-sizing: border-box" @changeDate="changeDateView"></Date>
    <!-- 先注释掉，后面再看看 -->
  </div>
</template>

<script setup lang="ts">
import { serviceNameStore } from "@/store/modules/service";
import { useRouter, useRoute } from "vue-router";
import Date from "./components/Date.vue";
import { useSubMenuStore } from "@/store/modules/tagsView";
import { storeToRefs } from "pinia";
import { getAPPList } from "@/api/application/index";
import { getH5AppList } from "@/api/h5/overview";
import { getMiniAppIds } from "@/api/mini/overview";
import { applicationStore } from "@/store/modules/application";
import { breadcrumbStore } from "@/store/modules/breadcurmb";
const useBreadcrumbStore = breadcrumbStore();
const useApplicationStore = applicationStore();
const useServiceNameStore = serviceNameStore();
const currentRoute = useRoute();
const subMenuStore = useSubMenuStore();
const showBackBtn = ref(false);
const breadcrumb = ref("");
const detailPageBreadcrumb = ref("");
const appBreadBackKey = ref(0);
const { cachedViews } = storeToRefs(subMenuStore);
const router = useRouter();
const emit = defineEmits(["dateChange"]);
const isSpecialRouter = ref(false);
const { appId } = storeToRefs(useApplicationStore);
const serviceNameWithAlias = computed(() => {
  return useServiceNameStore.alias
    ? `${useServiceNameStore.serviceName}（${useServiceNameStore.alias}）`
    : useServiceNameStore.serviceName;
});
// 监听 appId 的变化
watch(appId, async newAppId => {
  if (newAppId) {
    useBreadcrumbStore.setAppOption("");
    await changeDateView();
  }
});

// TODO: 这里到时候看怎么刷新
async function changeDateView() {
  const basePath = currentRoute.path.match(/^\/(H5monitor|appmonitor|miniprogram)/)?.[0];
  if (basePath === "/H5monitor") {
    await getH5AppOption();
  } else if (basePath === "/appmonitor") {
    await getAppOption();
  } else if (basePath === "/miniprogram") {
    await getMiniAppOption();
  }
  const hasMatchingOption = options.value.some(
    option => option.value === useBreadcrumbStore.appOption
  );
  if (!hasMatchingOption) {
    useBreadcrumbStore.setAppOption("");
  }
  appBreadBackKey.value++;
  emit("dateChange");
}
function changeOptionView() {
  emit("dateChange");
}
// 返回
const goBack = () => {
  router.go(-1);
  // router.push({
  //   path: "/dashboard/overview"
  // });
};
// 通过/的数量来判断是否是详情页，如果是，则显示返回按钮
function hasMoreThanTwoSlashes(str: string) {
  const parts = str.split("/");
  const slashCount = parts.length - 1;
  return slashCount >= 3;
}

watch(currentRoute, val => {
  // 如果是详情路由，加上这个标识
  showBackBtn.value = hasMoreThanTwoSlashes(val.path);
  breadcrumb.value = matchUrl(val.path);
  detailPageBreadcrumb.value = useServiceNameStore.serviceName;
  handleSpecialRouter();
});
// 根据路由匹配面包屑
function matchUrl(url: string): string {
  const routerMap = {
    dashboard: "服务监测",
    H5monitor: "h5监测",
    appmonitor: "app监测",
    host: "主机监测",
    miniprogram: "小程序监测",
    warnmanage: "告警管理",
    flowMonitoring: "流量监测",
    logMonitoring: "日志监测",
    module: "组件监测",
    assistant: "AI助手"
  };

  for (const key in routerMap) {
    if (url.includes(key)) {
      return routerMap[key as keyof typeof routerMap] || "";
    }
  }
  return "";
}

function hanldleBreadcrumbs() {
  showBackBtn.value = hasMoreThanTwoSlashes(currentRoute.path);
  breadcrumb.value = matchUrl(currentRoute.path);
  detailPageBreadcrumb.value = useServiceNameStore.serviceName;
}
const options = ref([]);
const baseType = ref([]);

async function handleSpecialRouter() {
  useBreadcrumbStore.setStartRequest(false);
  const basePath = currentRoute.path.match(/^\/(H5monitor|appmonitor|miniprogram)/)?.[0];
  // 在数据获取完成之后再更新 isSpecialRouter
  isSpecialRouter.value =
    currentRoute.path.includes("/appmonitor") ||
    currentRoute.path.includes("/H5monitor") ||
    currentRoute.path.includes("/miniprogram");

  if (basePath !== useBreadcrumbStore.basePath) {
    useBreadcrumbStore.setAppOption("");
  }

  if (
    (basePath && basePath !== useBreadcrumbStore.basePath) ||
    (basePath && basePath === useBreadcrumbStore.basePath)
  ) {
    const urlEnd = currentRoute.path.replace(basePath, "");
    switch (urlEnd) {
      case "/overview":
        baseType.value = 1;
        break;
      case "/page-performance":
        baseType.value = 2;
        break;
      case "/ajax-performance":
        baseType.value = 3;
        break;
      case "/error-analysics":
        baseType.value = 4;
        break;
      case "/user-analysics":
        baseType.value = 5;
        break;
      case "/feature-analysics":
        baseType.value = 6;
        break;
      default:
        baseType.value = null;
    }
    useBreadcrumbStore.setBasePath(basePath);
    try {
      if (baseType.value !== null) {
        switch (basePath) {
          case "/H5monitor":
            await getH5AppOption();
            break;
          case "/appmonitor":
            await getAppOption();
            break;
          case "/miniprogram":
            await getMiniAppOption();
            break;
        }
      }
    } catch (error) {
      console.error(error);
    }
  } else if (!basePath) {
    useBreadcrumbStore.setBasePath("");
  }
}

// app监测应用列表
const getAppOption = async () => {
  const appId = useApplicationStore.appId;
  const res = await getAPPList({ appid: appId });
  if (res.code === 0 && res.records) {
    options.value = res.records.map((item: string) => {
      return { value: item, label: item };
    });
    useBreadcrumbStore.setStartRequest(true);
  }
};

// h5监测应用列表
const getH5AppOption = async () => {
  const appId = useApplicationStore.appId;
  const res = await getH5AppList({ appid: appId });
  if (res.code === 0 && res.records) {
    options.value = res.records.map((item: string) => {
      return { value: item, label: item };
    });
    useBreadcrumbStore.setStartRequest(true);
  }
};

// 小程序监测应用列表
const getMiniAppOption = async () => {
  const UrlType = baseType.value;
  const appId = useApplicationStore.appId;
  const res = await getMiniAppIds({ appid: appId, type: UrlType });
  if (res.code === 0 && res.records) {
    options.value = res.records.map((item: string) => {
      return { value: item, label: item };
    });
    useBreadcrumbStore.setStartRequest(true);
  }
};

onMounted(() => {
  hanldleBreadcrumbs();
  handleSpecialRouter();
  // 如果是详情路由并且serviceName为空，则跳转到overview
  if (
    showBackBtn.value &&
    useServiceNameStore.serviceName === "" &&
    currentRoute.path.includes("dashboard")
  ) {
    router.push("/dashboard/overview");
  }
});
</script>

<style lang="scss" scoped>
.date-back {
  align-items: center;

  &-desc {
    font-weight: 500;
    font-size: 20px;
    color: #333333;
  }

  &-title {
    :deep(.el-divider--vertical) {
      border-left: 2px #3275f9 var(--el-border-style);
      height: 0.9em;
      margin: 0px 8px 0px 2px;
    }
  }

  &-wrap {
    :deep(.el-divider--vertical) {
      border-left: 2px #3275f9 var(--el-border-style);
      height: 0.9em;
    }
  }
}
</style>
