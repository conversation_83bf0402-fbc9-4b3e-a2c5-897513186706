<template>
  <template v-for="item in menuList" :key="item.path">
    <template v-if="!item.meta.hidden">
      <!-- 有下级，用el-sub-menu，无下级用el-menu-item -->
      <el-menu-item
        :index="item.path"
        @click="handleMenuIsLink(item)"
        :class="['custome-el-menu', { selectTab: activeTabName === item.path }]"
      >
        <el-icon>
          <!-- <component
            class="text-white"
            v-if="item.meta.iconType == 'el'"
            :is="item.meta.icon"
          ></component>
          <SvgIcon class="text-white" v-else :name="item.meta.icon"></SvgIcon> -->
          <!-- {{ item.meta.icon }} -->
          <SvgIcon class="text-white mr-6px" :name="item.meta.icon"></SvgIcon>
        </el-icon>
        <div class="text-white ml-2px">{{ item.meta.title }}</div>
      </el-menu-item>
    </template>
  </template>
</template>

<script setup lang="ts">
import { useRouter, useRoute } from "vue-router";
import { useSubMenuStore } from "@/store/modules/tagsView";
const props = defineProps({
  /**
   * 当前路由项对象
   */
  menuList: {
    type: Array<any>,
    required: true
  }
});

const router = useRouter();
const currentRoute = useRoute();
const subMenuStore = useSubMenuStore();
const activeTabName = ref(""); // 默认是首页路由

// 点击切换tab函数
const handleMenuIsLink = (value: TagView) => {
  // 如果有二级菜单，加入children，并跳转到第一个子菜单
  if (value.children?.length) {
    subMenuStore.addSubMenu(value.children);
    router.push(value.children[0].path);
  } else {
    // 没的情况
    subMenuStore.addSubMenu(value);
    router.push(value.path);
  }
};
function extractBetweenSlashes(str: string) {
  const firstSlashIndex = str.indexOf("/"); // 找到第一个 / 的索引
  const secondSlashIndex = str.indexOf("/", firstSlashIndex + 1); // 找到第二个 / 的索引

  // 检查是否找到了两个 /
  if (firstSlashIndex !== -1 && secondSlashIndex !== -1) {
    // 返回第一个 / 到第二个 / 之间的内容，保留左边的 /
    return str.substring(firstSlashIndex, secondSlashIndex);
  }

  // 如果没有找到两个 / ，返回空字符串
  return "";
}

watch(currentRoute, val => {
  const str: string = extractBetweenSlashes(val.path);
  subMenuStore.watchRouteTabList(val.meta, val.path);
  activeTabName.value = str;
});

onMounted(() => {
  activeTabName.value = extractBetweenSlashes(currentRoute.path) || "/dashboard/overview";
  // 第一次进来的时候，要让上下两个tab都进入选中状态
  if (subMenuStore.cachedViews.length < 1) {
    const totalMenuList: any = props.menuList.filter((item: any) => {
      return currentRoute.path.includes(item.path);
    });
    handleMenuIsLink(totalMenuList[0]);
  }
});
</script>

<style lang="scss" scoped>
.custome-el-menu.is-active {
  border-bottom: #4877e8 !important;
  color: #fff !important;
}

.custome-el-menu {
  transition: unset;
  padding: 0 14px;
}
.el-menu--horizontal > .el-menu-item {
  border-bottom: none !important;
}

.el-menu--horizontal .el-menu-item:not(.is-disabled):focus,
.el-menu--horizontal .el-menu-item:not(.is-disabled):hover {
  background-color: #4877e8;
  color: unset !important;
}
.selectTab {
  background-color: #4877e8;
  color: unset !important;
}
</style>
