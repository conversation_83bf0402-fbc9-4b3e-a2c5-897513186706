<template>
  <el-menu
    mode="horizontal"
    :default-active="currentRoute.path"
    class="el-menu-vertical-demo"
    :collapse="!globalStore.sidebar.isActive"
    :unique-opened="true"
  >
    <SidebarMenuItem :menuList="permissionList"></SidebarMenuItem>
  </el-menu>
</template>

<script setup lang="ts">
import SidebarMenuItem from "./components/SidebarMenuItem.vue";
import { constantRoutes } from "@/router";
import { useGlobalStore } from "@/store/modules/global";
import { RouteRecordRaw, useRoute, useRouter } from "vue-router";
import { userInfoStore } from "@/store/modules/user";
const useUserInfoStore = userInfoStore();
const globalStore = useGlobalStore();
const currentRoute = useRoute();
const router = useRouter();

const menuList = ref<RouteRecordRaw[]>(constantRoutes[0].children as RouteRecordRaw[]);
const permissionList = computed(() => {
  return menuList.value;
});

onMounted(() => {
  if (menuList.value.length <= 1) {
    router.go(0);
  }
});
</script>

<style lang="scss" scoped>
.el-menu-vertical-demo:not(.el-menu--collapse) {
  max-width: 200px;
}

.el-menu-vertical-demo {
  border-right: none;
  width: 100%;
  background-color: #445fde;
  color: #fff;
}
</style>
