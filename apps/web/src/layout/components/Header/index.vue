<template>
  <div class="header">
    <HeadMenu @appChange="emit('appChange')"></HeadMenu>
    <SidebarMenu />
    <div class="header-right flex items-center justify-between header-uf">
      <!-- <DownloadGuide /> -->
      <FullScreen />
      <User />
    </div>
  </div>
</template>

<script setup lang="ts">
import HeadMenu from "@/layout/components/HeadMenu/index.vue";
import SidebarMenu from "@/layout/components/SidebarMenu/index.vue";
import FullScreen from "./components/FullScreen.vue";
import User from "./components/User.vue";
import DownloadGuide from "./components/DownloadGuide.vue";
const emit = defineEmits(["appChange"]);
</script>

<style lang="scss" scoped>
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: $aside-header-height;

  &-right > div {
    margin-left: 20px;
  }
  &-uf {
    height: 105%;
    background-color: #445fde;
  }
}
</style>
