.svelte-flow{direction:ltr;--xy-edge-stroke-default: #b1b1b7;--xy-edge-stroke-width-default: 1;--xy-edge-stroke-selected-default: #555;--xy-connectionline-stroke-default: #b1b1b7;--xy-connectionline-stroke-width-default: 1;--xy-attribution-background-color-default: rgba(255, 255, 255, .5);--xy-minimap-background-color-default: #fff;--xy-minimap-mask-background-color-default: rgba(240, 240, 240, .6);--xy-minimap-mask-stroke-color-default: transparent;--xy-minimap-mask-stroke-width-default: 1;--xy-minimap-node-background-color-default: #e2e2e2;--xy-minimap-node-stroke-color-default: transparent;--xy-minimap-node-stroke-width-default: 2;--xy-background-color-default: transparent;--xy-background-pattern-dots-color-default: #91919a;--xy-background-pattern-lines-color-default: #eee;--xy-background-pattern-cross-color-default: #e2e2e2;background-color:var(--xy-background-color, var(--xy-background-color-default));--xy-node-color-default: inherit;--xy-node-border-default: 1px solid #1a192b;--xy-node-background-color-default: #fff;--xy-node-group-background-color-default: rgba(240, 240, 240, .25);--xy-node-boxshadow-hover-default: 0 1px 4px 1px rgba(0, 0, 0, .08);--xy-node-boxshadow-selected-default: 0 0 0 .5px #1a192b;--xy-node-border-radius-default: 3px;--xy-handle-background-color-default: #1a192b;--xy-handle-border-color-default: #fff;--xy-selection-background-color-default: rgba(0, 89, 220, .08);--xy-selection-border-default: 1px dotted rgba(0, 89, 220, .8);--xy-controls-button-background-color-default: #fefefe;--xy-controls-button-background-color-hover-default: #f4f4f4;--xy-controls-button-color-default: inherit;--xy-controls-button-color-hover-default: inherit;--xy-controls-button-border-color-default: #eee;--xy-controls-box-shadow-default: 0 0 2px 1px rgba(0, 0, 0, .08);--xy-edge-label-background-color-default: #ffffff;--xy-edge-label-color-default: inherit;--xy-resize-background-color-default: #3367d9}.svelte-flow.dark{--xy-edge-stroke-default: #3e3e3e;--xy-edge-stroke-width-default: 1;--xy-edge-stroke-selected-default: #727272;--xy-connectionline-stroke-default: #b1b1b7;--xy-connectionline-stroke-width-default: 1;--xy-attribution-background-color-default: rgba(150, 150, 150, .25);--xy-minimap-background-color-default: #141414;--xy-minimap-mask-background-color-default: rgba(60, 60, 60, .6);--xy-minimap-mask-stroke-color-default: transparent;--xy-minimap-mask-stroke-width-default: 1;--xy-minimap-node-background-color-default: #2b2b2b;--xy-minimap-node-stroke-color-default: transparent;--xy-minimap-node-stroke-width-default: 2;--xy-background-color-default: #141414;--xy-background-pattern-dots-color-default: #777;--xy-background-pattern-lines-color-default: #777;--xy-background-pattern-cross-color-default: #777;--xy-node-color-default: #f8f8f8;--xy-node-border-default: 1px solid #3c3c3c;--xy-node-background-color-default: #1e1e1e;--xy-node-group-background-color-default: rgba(240, 240, 240, .25);--xy-node-boxshadow-hover-default: 0 1px 4px 1px rgba(255, 255, 255, .08);--xy-node-boxshadow-selected-default: 0 0 0 .5px #999;--xy-handle-background-color-default: #bebebe;--xy-handle-border-color-default: #1e1e1e;--xy-selection-background-color-default: rgba(200, 200, 220, .08);--xy-selection-border-default: 1px dotted rgba(200, 200, 220, .8);--xy-controls-button-background-color-default: #2b2b2b;--xy-controls-button-background-color-hover-default: #3e3e3e;--xy-controls-button-color-default: #f8f8f8;--xy-controls-button-color-hover-default: #fff;--xy-controls-button-border-color-default: #5b5b5b;--xy-controls-box-shadow-default: 0 0 2px 1px rgba(0, 0, 0, .08);--xy-edge-label-background-color-default: #141414;--xy-edge-label-color-default: #f8f8f8}.svelte-flow__background{background-color:var(--xy-background-color-props, var(--xy-background-color, var(--xy-background-color-default)));pointer-events:none;z-index:-1}.svelte-flow__container{position:absolute;width:100%;height:100%;top:0;left:0}.svelte-flow__pane{z-index:1}.svelte-flow__pane.draggable{cursor:grab}.svelte-flow__pane.dragging{cursor:grabbing}.svelte-flow__pane.selection{cursor:pointer}.svelte-flow__viewport{transform-origin:0 0;z-index:2;pointer-events:none}.svelte-flow__renderer{z-index:4}.svelte-flow__selection{z-index:6}.svelte-flow__nodesselection-rect:focus,.svelte-flow__nodesselection-rect:focus-visible{outline:none}.svelte-flow__edge-path{stroke:var(--xy-edge-stroke, var(--xy-edge-stroke-default));stroke-width:var(--xy-edge-stroke-width, var(--xy-edge-stroke-width-default));fill:none}.svelte-flow__connection-path{stroke:var(--xy-connectionline-stroke, var(--xy-connectionline-stroke-default));stroke-width:var(--xy-connectionline-stroke-width, var(--xy-connectionline-stroke-width-default));fill:none}.svelte-flow .svelte-flow__edges{position:absolute}.svelte-flow .svelte-flow__edges svg{overflow:visible;position:absolute;pointer-events:none}.svelte-flow__edge{pointer-events:visibleStroke}.svelte-flow__edge.selectable{cursor:pointer}.svelte-flow__edge.animated path{stroke-dasharray:5;animation:dashdraw .5s linear infinite}.svelte-flow__edge.animated path.svelte-flow__edge-interaction{stroke-dasharray:none;animation:none}.svelte-flow__edge.inactive{pointer-events:none}.svelte-flow__edge.selected,.svelte-flow__edge:focus,.svelte-flow__edge:focus-visible{outline:none}.svelte-flow__edge.selected .svelte-flow__edge-path,.svelte-flow__edge.selectable:focus .svelte-flow__edge-path,.svelte-flow__edge.selectable:focus-visible .svelte-flow__edge-path{stroke:var(--xy-edge-stroke-selected, var(--xy-edge-stroke-selected-default))}.svelte-flow__edge-textwrapper{pointer-events:all}.svelte-flow__edge .svelte-flow__edge-text{pointer-events:none;-webkit-user-select:none;-moz-user-select:none;user-select:none}.svelte-flow__arrowhead polyline{stroke:var(--xy-edge-stroke, var(--xy-edge-stroke-default))}.svelte-flow__arrowhead polyline.arrowclosed{fill:var(--xy-edge-stroke, var(--xy-edge-stroke-default))}.svelte-flow__connection{pointer-events:none}.svelte-flow__connection .animated{stroke-dasharray:5;animation:dashdraw .5s linear infinite}svg.svelte-flow__connectionline{z-index:1001;overflow:visible;position:absolute}.svelte-flow__nodes{pointer-events:none;transform-origin:0 0}.svelte-flow__node{position:absolute;-webkit-user-select:none;-moz-user-select:none;user-select:none;pointer-events:all;transform-origin:0 0;box-sizing:border-box;cursor:default}.svelte-flow__node.selectable{cursor:pointer}.svelte-flow__node.draggable{cursor:grab;pointer-events:all}.svelte-flow__node.draggable.dragging{cursor:grabbing}.svelte-flow__nodesselection{z-index:3;transform-origin:left top;pointer-events:none}.svelte-flow__nodesselection-rect{position:absolute;pointer-events:all;cursor:grab}.svelte-flow__handle{position:absolute;pointer-events:none;min-width:5px;min-height:5px;width:6px;height:6px;background-color:var(--xy-handle-background-color, var(--xy-handle-background-color-default));border:1px solid var(--xy-handle-border-color, var(--xy-handle-border-color-default));border-radius:100%}.svelte-flow__handle.connectingfrom{pointer-events:all}.svelte-flow__handle.connectionindicator{pointer-events:all;cursor:crosshair}.svelte-flow__handle-bottom{top:auto;left:50%;bottom:0;transform:translate(-50%,50%)}.svelte-flow__handle-top{top:0;left:50%;transform:translate(-50%,-50%)}.svelte-flow__handle-left{top:50%;left:0;transform:translate(-50%,-50%)}.svelte-flow__handle-right{top:50%;right:0;transform:translate(50%,-50%)}.svelte-flow__edgeupdater{cursor:move;pointer-events:all}.svelte-flow__pane.selection .svelte-flow__panel{pointer-events:none}.svelte-flow__panel{position:absolute;z-index:5;margin:15px}.svelte-flow__panel.top{top:0}.svelte-flow__panel.bottom{bottom:0}.svelte-flow__panel.top.center,.svelte-flow__panel.bottom.center{left:50%;transform:translate(-15px) translate(-50%)}.svelte-flow__panel.left{left:0}.svelte-flow__panel.right{right:0}.svelte-flow__panel.left.center,.svelte-flow__panel.right.center{top:50%;transform:translateY(-15px) translateY(-50%)}.svelte-flow__attribution{font-size:10px;background:var(--xy-attribution-background-color, var(--xy-attribution-background-color-default));padding:2px 3px;margin:0}.svelte-flow__attribution a{text-decoration:none;color:#999}@keyframes dashdraw{0%{stroke-dashoffset:10}}.svelte-flow__edgelabel-renderer{position:absolute;width:100%;height:100%;pointer-events:none;-webkit-user-select:none;-moz-user-select:none;user-select:none;left:0;top:0}.svelte-flow__viewport-portal{position:absolute;width:100%;height:100%;left:0;top:0;-webkit-user-select:none;-moz-user-select:none;user-select:none}.svelte-flow__minimap{background:var( --xy-minimap-background-color-props, var(--xy-minimap-background-color, var(--xy-minimap-background-color-default)) )}.svelte-flow__minimap-svg{display:block}.svelte-flow__minimap-mask{fill:var( --xy-minimap-mask-background-color-props, var(--xy-minimap-mask-background-color, var(--xy-minimap-mask-background-color-default)) );stroke:var( --xy-minimap-mask-stroke-color-props, var(--xy-minimap-mask-stroke-color, var(--xy-minimap-mask-stroke-color-default)) );stroke-width:var( --xy-minimap-mask-stroke-width-props, var(--xy-minimap-mask-stroke-width, var(--xy-minimap-mask-stroke-width-default)) )}.svelte-flow__minimap-node{fill:var( --xy-minimap-node-background-color-props, var(--xy-minimap-node-background-color, var(--xy-minimap-node-background-color-default)) );stroke:var( --xy-minimap-node-stroke-color-props, var(--xy-minimap-node-stroke-color, var(--xy-minimap-node-stroke-color-default)) );stroke-width:var( --xy-minimap-node-stroke-width-props, var(--xy-minimap-node-stroke-width, var(--xy-minimap-node-stroke-width-default)) )}.svelte-flow__background-pattern.dots{fill:var( --xy-background-pattern-color-props, var(--xy-background-pattern-color, var(--xy-background-pattern-dots-color-default)) )}.svelte-flow__background-pattern.lines{stroke:var( --xy-background-pattern-color-props, var(--xy-background-pattern-color, var(--xy-background-pattern-lines-color-default)) )}.svelte-flow__background-pattern.cross{stroke:var( --xy-background-pattern-color-props, var(--xy-background-pattern-color, var(--xy-background-pattern-cross-color-default)) )}.svelte-flow__controls{display:flex;flex-direction:column;box-shadow:var(--xy-controls-box-shadow, var(--xy-controls-box-shadow-default))}.svelte-flow__controls.horizontal{flex-direction:row}.svelte-flow__controls-button{display:flex;justify-content:center;align-items:center;height:26px;width:26px;padding:4px;border:none;background:var(--xy-controls-button-background-color, var(--xy-controls-button-background-color-default));border-bottom:1px solid var( --xy-controls-button-border-color-props, var(--xy-controls-button-border-color, var(--xy-controls-button-border-color-default)) );color:var( --xy-controls-button-color-props, var(--xy-controls-button-color, var(--xy-controls-button-color-default)) );cursor:pointer;-webkit-user-select:none;-moz-user-select:none;user-select:none}.svelte-flow__controls-button svg{width:100%;max-width:12px;max-height:12px;fill:currentColor}.svelte-flow__edge.updating .svelte-flow__edge-path{stroke:#777}.svelte-flow__edge-text{font-size:10px}.svelte-flow__node.selectable:focus,.svelte-flow__node.selectable:focus-visible{outline:none}.svelte-flow__node-input,.svelte-flow__node-default,.svelte-flow__node-output,.svelte-flow__node-group{padding:10px;border-radius:var(--xy-node-border-radius, var(--xy-node-border-radius-default));width:150px;font-size:12px;color:var(--xy-node-color, var(--xy-node-color-default));text-align:center;border:var(--xy-node-border, var(--xy-node-border-default));background-color:var(--xy-node-background-color, var(--xy-node-background-color-default))}.svelte-flow__node-input.selectable:hover,.svelte-flow__node-default.selectable:hover,.svelte-flow__node-output.selectable:hover,.svelte-flow__node-group.selectable:hover{box-shadow:var(--xy-node-boxshadow-hover, var(--xy-node-boxshadow-hover-default))}.svelte-flow__node-input.selectable.selected,.svelte-flow__node-input.selectable:focus,.svelte-flow__node-input.selectable:focus-visible,.svelte-flow__node-default.selectable.selected,.svelte-flow__node-default.selectable:focus,.svelte-flow__node-default.selectable:focus-visible,.svelte-flow__node-output.selectable.selected,.svelte-flow__node-output.selectable:focus,.svelte-flow__node-output.selectable:focus-visible,.svelte-flow__node-group.selectable.selected,.svelte-flow__node-group.selectable:focus,.svelte-flow__node-group.selectable:focus-visible{box-shadow:var(--xy-node-boxshadow-selected, var(--xy-node-boxshadow-selected-default))}.svelte-flow__node-group{background-color:var(--xy-node-group-background-color, var(--xy-node-group-background-color-default))}.svelte-flow__nodesselection-rect,.svelte-flow__selection{background:var(--xy-selection-background-color, var(--xy-selection-background-color-default));border:var(--xy-selection-border, var(--xy-selection-border-default))}.svelte-flow__nodesselection-rect:focus,.svelte-flow__nodesselection-rect:focus-visible,.svelte-flow__selection:focus,.svelte-flow__selection:focus-visible{outline:none}.svelte-flow__controls-button:hover{background:var( --xy-controls-button-background-color-hover-props, var(--xy-controls-button-background-color-hover, var(--xy-controls-button-background-color-hover-default)) );color:var( --xy-controls-button-color-hover-props, var(--xy-controls-button-color-hover, var(--xy-controls-button-color-hover-default)) )}.svelte-flow__controls-button:disabled{pointer-events:none}.svelte-flow__controls-button:disabled svg{fill-opacity:.4}.svelte-flow__controls-button:last-child{border-bottom:none}.svelte-flow__controls.horizontal .svelte-flow__controls-button{border-bottom:none;border-right:1px solid var( --xy-controls-button-border-color-props, var(--xy-controls-button-border-color, var(--xy-controls-button-border-color-default)) )}.svelte-flow__controls.horizontal .svelte-flow__controls-button:last-child{border-right:none}.svelte-flow__resize-control{position:absolute}.svelte-flow__resize-control.left,.svelte-flow__resize-control.right{cursor:ew-resize}.svelte-flow__resize-control.top,.svelte-flow__resize-control.bottom{cursor:ns-resize}.svelte-flow__resize-control.top.left,.svelte-flow__resize-control.bottom.right{cursor:nwse-resize}.svelte-flow__resize-control.bottom.left,.svelte-flow__resize-control.top.right{cursor:nesw-resize}.svelte-flow__resize-control.handle{width:5px;height:5px;border:1px solid #fff;border-radius:1px;background-color:var(--xy-resize-background-color, var(--xy-resize-background-color-default));translate:-50% -50%}.svelte-flow__resize-control.handle.left{left:0;top:50%}.svelte-flow__resize-control.handle.right{left:100%;top:50%}.svelte-flow__resize-control.handle.top{left:50%;top:0}.svelte-flow__resize-control.handle.bottom{left:50%;top:100%}.svelte-flow__resize-control.handle.top.left,.svelte-flow__resize-control.handle.bottom.left{left:0}.svelte-flow__resize-control.handle.top.right,.svelte-flow__resize-control.handle.bottom.right{left:100%}.svelte-flow__resize-control.line{border-color:var(--xy-resize-background-color, var(--xy-resize-background-color-default));border-width:0;border-style:solid}.svelte-flow__resize-control.line.left,.svelte-flow__resize-control.line.right{width:1px;transform:translate(-50%);top:0;height:100%}.svelte-flow__resize-control.line.left{left:0;border-left-width:1px}.svelte-flow__resize-control.line.right{left:100%;border-right-width:1px}.svelte-flow__resize-control.line.top,.svelte-flow__resize-control.line.bottom{height:1px;transform:translateY(-50%);left:0;width:100%}.svelte-flow__resize-control.line.top{top:0;border-top-width:1px}.svelte-flow__resize-control.line.bottom{border-bottom-width:1px;top:100%}.svelte-flow__edge-label{text-align:center;position:absolute;padding:2px;font-size:10px;color:var(--xy-edge-label-color, var(--xy-edge-label-color-default));background:var(--xy-edge-label-background-color, var(--xy-edge-label-background-color-default))}.svelte-flow__container{-webkit-user-select:none;-moz-user-select:none;user-select:none}:root,:root .tf-theme-light{--tf-primary-color: #2563EB;--xy-node-boxshadow-selected: 0 0 0 1px var(--tf-primary-color);--xy-handle-background-color: var(--tf-primary-color)}.tf-btn{display:flex;align-items:center;justify-content:center;gap:2px;background:#fff;border:1px solid #ccc;cursor:pointer;border-radius:5px;padding:5px;margin:0;height:fit-content;width:fit-content}.tf-btn svg{fill:currentColor;width:16px;height:16px}.tf-btn:hover{border:1px solid var(--tf-primary-color)}.tf-btn.tf-btn-primary{background:var(--tf-primary-color);color:#fff}.tf-btn.tf-btn-primary:hover{border:1px solid #ccc;background:#3a6fe3}.tf-input,.tf-textarea{display:flex;border-radius:5px;border:1px solid #ccc;padding:5px 8px;box-sizing:border-box;resize:vertical;outline:none;line-height:normal}.tf-input::placeholder,.tf-textarea::placeholder{color:#ccc;font-size:12px}.tf-input:focus,.tf-textarea:focus{border-color:var(--tf-primary-color);box-shadow:0 0 5px #51cbee33}.tf-input[disabled],.tf-textarea[disabled]{background-color:#f0f0f0;cursor:not-allowed;color:#aaa}.tf-select-input{display:flex;border:1px solid #ccc;padding:3px 10px;border-radius:5px;font-size:14px;justify-content:space-between;align-items:center;cursor:pointer;background:#fff;height:27px}.tf-select-input:focus{border-color:var(--tf-primary-color);box-shadow:0 0 5px #51cbee33}.tf-select-input-value{height:21px;min-width:10px;font-size:12px;display:flex;align-items:center}.tf-select-input-arrow{display:block;width:16px;height:16px;color:#666}.tf-select-input-placeholder{color:#ccc}.tf-select-content{display:flex;flex-direction:column;background:#fff;margin-top:5px;border:1px solid #ccc;border-radius:5px;padding:5px;width:max-content;min-width:100%;z-index:999;box-sizing:border-box}.tf-select-content-item{display:flex;align-items:center;padding:5px 10px;border:none;background:#fff;border-radius:5px;cursor:pointer;line-height:100%;gap:2px}.tf-select-content-item span{width:16px;display:flex}.tf-select-content-item svg{width:16px;height:16px;margin:auto}.tf-select-content-item:hover{background:#f0f0f0}.tf-select-content-children{padding-left:14px}.tf-checkbox{width:14px;height:14px}.tf-tabs{display:flex;align-items:center;justify-content:center;gap:5px;padding:5px;border-radius:5px;border:none;background:#f4f4f5}.tf-tabs .tf-tabs-item{flex-grow:1;padding:5px 10px;cursor:pointer;border-radius:5px;display:flex;align-items:center;justify-content:center;font-size:14px;color:#808088}.tf-tabs .tf-tabs-item.active{background:#fff;color:#333;font-weight:500;box-shadow:0 0 5px #00000026}h3.tf-heading{font-weight:700;font-size:14px;margin-top:2px;margin-bottom:3px;color:#333}.tf-collapse{border:none;border-radius:5px}.tf-collapse-item-title{display:flex;align-items:center;cursor:pointer;font-size:14px}.tf-collapse-item-title-icon{display:flex;width:26px;height:26px;color:#2563eb;background:#cedafb;border-radius:5px;padding:3px;justify-content:center;align-items:center;margin-right:10px}.tf-collapse-item-title-icon svg{width:22px;height:22px;color:#3474ff}.tf-collapse-item-title-arrow{display:block;width:16px;height:16px;margin-left:auto}.tf-collapse-item-description{font-size:12px;margin:10px 0;color:#999}.svelte-flow__nodes .svelte-flow__node{border:3px solid transparent;border-radius:5px;box-sizing:border-box}.svelte-flow__nodes .svelte-flow__node .svelte-flow__handle{width:16px;height:16px;background:transparent;display:flex;justify-content:center;align-items:center;border:none}.svelte-flow__nodes .svelte-flow__node .svelte-flow__handle:after{content:" ";background:#2563eb;width:8px;height:8px;border-radius:100%;transition:width .1s,height .1s}.svelte-flow__nodes .svelte-flow__node .svelte-flow__handle:hover:after{width:16px;height:16px}.svelte-flow__nodes .svelte-flow__node div.loop_handle_wrapper:after{content:"循环体";background:#2563eb;width:100px;height:20px;border-radius:0;display:flex;color:#fff;justify-content:center;align-items:center}.svelte-flow__nodes .svelte-flow__node div.loop_handle_wrapper:hover:after{width:100px;height:20px}.svelte-flow__nodes .svelte-flow__node:after{content:" ";position:absolute;border-radius:5px;top:-2px;left:-2px;border:1px solid #ccc;height:calc(100% + 2px);width:calc(100% + 2px)}.svelte-flow__nodes .svelte-flow__node:hover{border:3px solid #bacaef7d}.svelte-flow__nodes .svelte-flow__node.selectable.selected{border:3px solid #bacaef7d;box-shadow:var(--xy-node-boxshadow-selected)}.svelte-flow__nodes .svelte-flow__node:hover:after{display:none}.svelte-flow__nodes .svelte-flow__node.selectable.selected:after{display:none}.tf-node-wrapper{border-radius:5px;min-width:300px;background:#fff}.tf-node-wrapper-title{height:30px;background:#eff1f5;color:#bcbcbc;font-size:12px;display:flex;align-items:center;padding-left:5px;border-bottom:1px solid #ccc;font-weight:300;letter-spacing:1px}.tf-node-wrapper-body{padding:10px}.svelte-flow__attribution a{display:none}.tf-toolbar{z-index:200;position:absolute;top:10px;left:10px;display:flex;gap:5px;transition:transform .5s ease,opacity .5s ease;transform:translate(calc(-100% + 20px))}.tf-toolbar.show{transform:translate(0)}.tf-toolbar-container{background:#fff;border:1px solid #eee;border-radius:5px;box-shadow:0 0 5px #0000001a;padding:10px;width:fit-content}.tf-toolbar-container-header{display:flex}.tf-toolbar-container-body{display:flex;margin-top:20px}.tf-toolbar-container-body .tf-toolbar-container-base,.tf-toolbar-container-body .tf-toolbar-container-tools{display:flex;flex-direction:column;gap:4px;flex-grow:1}.tf-toolbar-container-body .tf-toolbar-container-base .tf-btn,.tf-toolbar-container-body .tf-toolbar-container-tools .tf-btn{border:none;width:100%;justify-content:flex-start;height:40px;gap:10px;cursor:grabbing;border-radius:5px}.tf-toolbar-container-body .tf-toolbar-container-base .tf-btn svg,.tf-toolbar-container-body .tf-toolbar-container-tools .tf-btn svg{width:20px;height:20px;fill:#2563eb}.tf-toolbar-container-body .tf-toolbar-container-base .tf-btn:hover,.tf-toolbar-container-body .tf-toolbar-container-tools .tf-btn:hover{background:#f1f1f1}.tinyflow-logo:after{content:"Tinyflow.ai";font-size:145px;display:flex;align-items:center;justify-content:center;width:100%;height:100%;font-weight:800;color:#03153b54;text-shadow:1px 3px 6px #cedafb,0 0 0 #000,1px 3px 6px #fff;opacity:.1}
