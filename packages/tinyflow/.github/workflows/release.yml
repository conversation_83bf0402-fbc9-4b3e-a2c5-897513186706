name: Release and Publish to NPM

on:
  push:
    branches:
      - main # 或者你的主分支名称
    paths:
      - ".changeset/*.md" # 仅在 .changeset 文件夹中的 .md 文件发生变更时触发
#      - "package.json" # 如果 package.json 发生变更也触发
  workflow_dispatch: # 允许手动触发工作流

#on:
#  push:
#    tags:
#      - "v*" # 匹配以 v 开头的标签（如 v1.0.0）
#  workflow_dispatch:


permissions:
  id-token: write
  contents: write
  pull-requests: write

jobs:
  release:
    runs-on: ubuntu-latest
    steps:
      # 1. 检出代码
      - name: Checkout Repository
        uses: actions/checkout@v3
        with:
          fetch-depth: 0 # 获取完整的 Git 历史记录，Changesets 需要它来生成变更日志

      # 2. 设置 Node.js 环境
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 20 # 指定 Node.js 20 或更高版本
          registry-url: https://registry.npmjs.org/ # 设置 NPM 注册表

      # 3. 安装 pnpm
      - name: Install pnpm
        run: npm install -g pnpm

      # 4. 安装依赖
      - name: Install Dependencies
        run: pnpm install --frozen-lockfile

      # 5. 使用 Changesets Action 自动化发布
      - name: Create Release PR or publish stable version to npm
        id: changesets
        uses: changesets/action@v1
        with:
          createGithubReleases: true # 启用自动创建 GitHub Release
          publish: pnpm changeset publish
          version: pnpm changeset version
          title: ${{ github.ref_name == 'main' && 'Publish a new stable version' || 'Publish a new pre-release version' }}
          commit: >-
            ${{ github.ref_name == 'main' && 'chore(release): publish a new release version' || 'chore(release): publish a new pre-release version' }}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
          NPM_CONFIG_PROVENANCE: true # 启用 NPM Provenance 功能

      # 6. 打印日志以确认版本号
      - name: Print Version and Published Package
        if: success() # 仅在上一步成功时执行
        run: |
          echo "Current package.json version: $(node -p "require('./package.json').version")"
          echo "Latest version on NPM: $(npm show @tinyflow-ai/react version)"
