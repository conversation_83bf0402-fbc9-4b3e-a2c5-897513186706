{
//    "extends": "@tsconfig/svelte/tsconfig.json",
    "compilerOptions": {
        "esModuleInterop": true,
        "target": "ESNext",
        "useDefineForClassFields": true,
        "module": "ESNext",
        /**
         * Typecheck JS in `.svelte` and `.js` files by default.
         * Disable checkJs if you'd like to use dynamic types in JS.
         * Note that setting allowJs false does not prevent the use
         * of JS in `.svelte` files.
         */
        "allowJs": true,
        "checkJs": true,
        "isolatedModules": true,
        "moduleDetection": "force",
        "paths": {
            "@tinyflow-ai/ui": ["../../packages/ui/src"],
            "@tinyflow-ai/ui/*": ["../../packages/ui/src/*"]
        }
    },
    "include": ["src/**/*.ts", "src/**/*.js", "src/**/*.svelte"]
}
